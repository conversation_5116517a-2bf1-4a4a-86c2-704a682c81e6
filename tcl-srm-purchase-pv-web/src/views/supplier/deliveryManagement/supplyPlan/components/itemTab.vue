<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      :expand-config="{ lazy: true, loadMethod: loadOrderDetailMethod }"
      keep-source
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="pageType !== 'create'"
      @refresh="getTableData"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <!-- 展开行内容模板 -->
      <template #orderDetailContent="{ row }">
        <div class="expand-content">
          <div class="sub-table-container">
            <sc-table
              :columns="subTableColumns"
              :table-data="row.orderDetails || []"
              :is-show-column-config="false"
              :is-show-right-btn="false"
              :is-show-refresh-bth="false"
              :fix-height="200"
              size="mini"
              show-overflow="tooltip"
              border
            />
          </div>
        </div>
      </template>
    </sc-table>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ScTable from '@/components/ScTable/src/index'
export default {
  name: 'ItemTab',
  components: { ScTable },
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      editRules: {
        deliveryQty: [{ required: true, message: this.$t('必填') }]
      }
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    sourceType() {
      return this.$route.query?.source || 'plan'
    },
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          align: 'center'
        },
        {
          field: 'allocateOrder',
          title: this.$t('手工分配订单'),
          type: 'expand',
          minWidth: 120,
          showOverflow: true,
          slots: {
            content: 'orderDetailContent'
          }
        },
        {
          field: 'deliveryPlanNo',
          title: this.$t('交货计划单号')
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码')
        },
        {
          field: 'itemName',
          title: this.$t('物料名称')
        },
        {
          field: 'specificationModel',
          title: this.$t('规格型号')
        },
        {
          field: 'baseUnitCode',
          title: this.$t('单位'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.baseUnitName : ''
          }
        },
        {
          field: 'quantity',
          title: this.$t('订单数量')
        },
        {
          field: 'preDeliveryQty',
          title: this.$t('待发货数量')
        },
        {
          field: 'deliveryQty',
          title: this.$t('发货数量'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.deliveryQty}
                  placeholder={this.$t('请输入')}
                  min='1'
                  max={row.preDeliveryQty}
                  transfer
                  clearable
                  disabled={this.sourceType === 'plan'}
                />
              ]
            }
          }
        },
        {
          field: 'receivedQuantity',
          title: this.$t('签收数量')
        },
        {
          field: 'returnQuantity',
          title: this.$t('退回数量')
        },
        {
          field: 'orderCode',
          title: this.$t('采购订单号')
        },
        {
          field: 'itemNo',
          title: this.$t('采购订单行号')
        },
        {
          field: 'requiredDeliveryDate',
          title: this.$t('要求交期')
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.categoryName : ''
          }
        },
        {
          field: 'largeCategoryCode',
          title: this.$t('大类'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.largeCategoryName : row.largeCategoryName
          }
        },
        {
          field: 'mediumCategoryCode',
          title: this.$t('中类'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.mediumCategoryName : row.mediumCategoryName
          }
        },
        {
          field: 'smallCategoryCode',
          title: this.$t('小类'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.smallCategoryName : row.smallCategoryName
          }
        },
        {
          field: 'itemRemark',
          title: this.$t('行备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.itemRemark}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: ['create'].includes(this.pageType),
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = []
      if (['create'].includes(this.pageType)) {
        btns = [{ code: 'delete', name: this.$t('删除'), status: 'info', loading: false }]
      }
      return btns
    },
    // 子表格列配置
    subTableColumns() {
      return [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 60,
          align: 'center'
        },
        {
          field: 'materialCode',
          title: this.$t('采购订单编号'),
          width: 180
        },
        {
          field: 'materialName',
          title: this.$t('采购订单行号'),
          width: 120,
          align: 'center'
        },
        {
          field: 'orderQuantity',
          title: this.$t('订单数量'),
          width: 120,
          align: 'center'
        },
        {
          field: 'allocatedQuantity',
          title: this.$t('本次分配数量'),
          width: 120,
          align: 'center'
        },
        {
          field: 'requiredDate',
          title: this.$t('要求交期'),
          width: 120,
          align: 'center'
        },
        {
          field: 'deliveryDate',
          title: this.$t('计划交货日期'),
          width: 120,
          align: 'center'
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          minWidth: 120,
          showOverflow: true
        }
      ]
    }
  },
  mounted() {
    if (this.pageType === 'create') {
      let data = []
      if (this.sourceType === 'plan') {
        data = sessionStorage.getItem('itemTabData')
          ? JSON.parse(sessionStorage.getItem('itemTabData'))
          : []
        data.forEach((item) => {
          item.requiredDeliveryDate = dayjs(Number(item.requiredDeliveryDate)).format('YYYY-MM-DD')
          item.deliveryPlanNo = item.serialNumber
        })
      }
      if (this.sourceType === 'order') {
        data = sessionStorage.getItem('supplyPlanSelectedRecords')
          ? JSON.parse(sessionStorage.getItem('supplyPlanSelectedRecords'))
          : []
      }
      this.tableData = data
      this.$emit('updateDetail', data)
    } else {
      this.getTableData()
    }
  },
  methods: {
    getTableData() {
      this.loading = true
      let params = {
        ids: [this.$route.query?.id]
      }
      this.$API.deliveryManagement
        .getDataByDeliveryIdsDeliveryNoteListApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.data.map((item) => {
              return {
                ...item,
                quantity: item.orderQuantity,
                preDeliveryQty: item.requiredQuantity,
                deliveryQty: item.shippedQuantity,
                itemNo: item.orderLineNo
              }
            })
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'delete':
          this.handleDelete()
          break
        default:
          break
      }
    },
    handleDelete() {
      this.tableRef.removeCheckboxRow()
      const currentViewRecords = this.tableRef.getTableData().visibleData
      this.$emit('updateDetail', currentViewRecords)
    },
    editComplete(args) {
      // const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        // this.tableRef.validate([row]).then((valid) => {
        //   if (valid) {
        //     this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
        //     return
        //   }
        // })
        // 2、保存数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.$emit('updateDetail', currentViewRecords)
      }
    },
    // 懒加载订单明细数据
    loadOrderDetailMethod({ row }) {
      // 如果已经加载过数据，直接返回
      if (row.orderDetails && row.orderDetails.length > 0) {
        return Promise.resolve()
      }

      // 模拟API调用获取订单明细数据
      // 实际项目中应该调用真实的API
      return new Promise((resolve) => {
        setTimeout(() => {
          // 模拟订单明细数据
          row.orderDetails = [
            {
              materialCode: 'TCL-2025-08-000495-HS',
              materialName: '1',
              orderQuantity: 1000,
              allocatedQuantity: 1000,
              requiredDate: '2025-08-10',
              deliveryDate: '2025-08-10',
              remark: '500'
            },
            {
              materialCode: 'TCL-2025-08-000495-HS',
              materialName: '3',
              orderQuantity: 2000,
              allocatedQuantity: 1000,
              requiredDate: '2025-08-15',
              deliveryDate: '2025-08-15',
              remark: '500'
            },
            {
              materialCode: 'TCL-2025-08-000495-HS',
              materialName: '6',
              orderQuantity: 6000,
              allocatedQuantity: 6000,
              requiredDate: '2025-08-20',
              deliveryDate: '2025-08-20',
              remark: '2000'
            }
          ]
          resolve()
        }, 500) // 模拟网络延迟
      })

      // 实际API调用示例（请根据实际API调整）:
      /*
      const params = {
        deliveryPlanNo: row.deliveryPlanNo,
        itemCode: row.itemCode
      }
      return this.$API.deliveryManagement
        .getOrderDetailsByPlanNo(params)
        .then((res) => {
          if (res.code === 200) {
            row.orderDetails = res.data || []
          }
        })
        .catch((error) => {
          console.error('加载订单明细失败:', error)
          this.$toast({ content: this.$t('加载订单明细失败'), type: 'error' })
        })
      */
    }
  }
}
</script>

<style scoped>
.expand-content {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin: 8px 0;
}

.sub-table-container {
  width: 100%;
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 子表格样式调整 */
.expand-content /deep/ .vxe-table {
  border: 1px solid #e4e7ed;
  width: 100% !important;
}

.expand-content /deep/ .vxe-table .vxe-header--row {
  background-color: #f5f7fa;
}

.expand-content /deep/ .vxe-table .vxe-body--row:hover {
  background-color: #f0f9ff;
}

/* 修复固定列对子表格的影响 */
.expand-content /deep/ .vxe-table .vxe-table--fixed-left-wrapper {
  z-index: 1;
}

.expand-content /deep/ .vxe-table .vxe-table--fixed-right-wrapper {
  z-index: 1;
}

/* 确保子表格列宽度正确显示 */
.expand-content /deep/ .vxe-table .vxe-header--column,
.expand-content /deep/ .vxe-table .vxe-body--column {
  min-width: 60px;
}

/* 子表格序号列样式 */
.expand-content /deep/ .vxe-table .vxe-table--seq {
  background-color: #fafafa;
  font-weight: 500;
}

/* 子表格内容对齐 */
.expand-content /deep/ .vxe-table .vxe-cell {
  padding: 8px 6px;
  line-height: 1.4;
}
</style>
