<template>
  <div class="full-height">
    <div class="aptitude-head">
      <div class="aptitude-title">
        <span>{{ fromInfoTop.applyName }}</span>
        <span>{{ screen(fromInfoTop.supplierStatus) }}</span>
      </div>
      <div class="aptitude-encoder">
        <span class="encoder">{{ $t('审查单编码：') }}{{ fromInfoTop.applyCode }}</span>
        <span>{{ $t('创建日期：') }}{{ fromInfoTop.createTime }}</span>
      </div>
      <div class="aptitude-designation">
        <span class="designation">{{ $t('客户名称：') }}{{ fromInfoTop.organizationName }}</span>
        <span>{{ $t('品类：') }}{{ fromInfoTop.categoryName }}</span>
      </div>
      <p class="gobank">
        <span @click="refresh">{{ $t('刷新') }}</span>
        <span
          v-if="fromInfoTop.supplierStatus != '20' && fromInfoTop.supplierStatus != '40'"
          @click="save"
          >{{ $t('保存') }}</span
        >
        <span
          v-if="fromInfoTop.supplierStatus != '20' && fromInfoTop.supplierStatus != '40'"
          @click="saveAndConfirm"
          >{{ this.$t('保存并提交') }}</span
        >
        <span @click="gobank">{{ $t('返回') }}</span>
      </p>
    </div>
    <div style="height: calc(100% - 124px)">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        @showDialog="showDialog"
        @fileChange="handleFileChange"
      />
    </div>
  </div>
</template>
<script>
import { pageConfig, oneArr, tweArr } from './config/index'
import cellFileView from './components/cellFileView.vue' // 单元格附件查看

export default {
  components: {},
  data() {
    return {
      pageConfig: [], //配置项
      fromInfo: {}, //放置存储applycode参数后台需要这个参数
      fromData: [],
      columnData: [],
      dataSource: [], //存储查询审查单的资质项列表
      fromInfoTop: []
    }
  },
  async mounted() {
    this.fromInfo = JSON.parse(localStorage.accessangeMange) //把本地存储的数据放到frominfo里
    await this.getQualificationDescInterface()
    await this.init()
  },
  methods: {
    refresh() {
      this.init()
    },
    async getQualificationDescInterface() {
      await this.$API.accessangeMange
        .getQualificationDesc({
          applyCode: this.fromInfo.applyCode //动态接口后台需要其中的applycode这一项
        })
        .then((res) => {
          if (res.code === 200 && res.data) {
            this.fromInfoTop = res.data
          }
        })
    },
    screen(val) {
      switch (val) {
        case '0':
          return this.$t('待处理')
        case '10':
          return this.$t('待提交')
        case '20':
          return this.$t('待审批')
        case '30':
          return this.$t('被驳回')
        case '40':
          return this.$t('已确认')
      }
    },
    async init() {
      let res = await this.$API.accessangeMange.getDynamicFieldUnionList({
        applyCode: this.fromInfo.applyCode //动态接口后台需要其中的applycode这一项
      })
      console.log(res.data, 'res.datares.data')
      //fromdata存储返回的需要的数据
      this.fromData = res.data.checker.map((item) => {
        let obj = {
          value: item.fieldCode,
          text: item.fieldName,
          required: item.required,
          qualificationCode: item.qualificationCode
        }
        return obj
      })
      // 动态数据
      let arr = res.data.unionList.map((item) => {
        let obj = {
          field: item.fieldCode,
          headerText: this.$t(item.fieldName)
          // allowEditing: false
        }
        // let _fieldCode = [
        //   'contacts',
        //   'effectiveDate',
        //   'expirationDate',
        //   'mobileNo',
        //   'phoneNo',
        //   'remark'
        // ]
        // _fieldCode.map((val) => {
        //   if (item.fieldCode == val) {
        //     ;(obj.allowEditing = true), console.log(this.fromInfoTop.supplierStatus)
        //     obj.headerTemplate = () => {
        //       return {
        //         template: Vue.component('headers', {
        //           template: `
        //       <div class="headers">
        //         <span style="color: red">*</span>
        //         <span class="e-headertext">{{$t('${obj.headerText}')}}</span>
        //       </div>
        //     `
        //         })
        //       }
        //     }
        //   }
        // })
        // if (item.fieldCode == 'contacts') {
        //   // 联系人
        // } else if (item.fieldCode == 'effectiveDate') {
        //   // 证书生效日期
        //   obj.template = () => {
        //     return { template: DataPickerInput }
        //   }
        //   obj.editTemplate = () => {
        //     return { template: DatePicker }
        //   }
        // } else if (item.fieldCode == 'expirationDate') {
        //   // 证书失效日期
        //   obj.template = () => {
        //     return { template: DataPickerInput }
        //   }
        //   obj.editTemplate = () => {
        //     return { template: DatePicker }
        //   }
        // } else if (item.fieldCode == 'mobileNo') {
        //   // 手机号
        // } else if (item.fieldCode == 'phoneNo') {
        //   // 电话
        // } else if (item.fieldCode == 'remark') {
        //   // 备注
        // }
        // 动态字段附件下载
        if (item.fieldCode == 'file') {
          // obj.valueAccessor = function (field, data) {
          //   if (JSON.parse(data.file).length > 0) {
          //     return `查看(${JSON.parse(data.file).length})`
          //   } else {
          //     return `暂无附件`
          //   }
          // }
          // ;(obj.allowEditing = true),
          obj.supplierStatus = this.fromInfoTop.supplierStatus
          obj.template = function () {
            return {
              template: cellFileView
            }
          }
          // obj.editTemplate = () => {
          //   return {
          //     template: cellUpload
          //   }
          // }
          //#region
          // obj.template = () => {
          //   return {
          //     template: Vue.component("modelName", {
          //       template: `<div>
          //         <p v-if="fileList.length<1" style="color:#00469c;cursor:pointer;" @click="fieldDownloadBtn('upload')">{{fileList.length>0 ? fileList[0].fileName:"点击上传"}}</p>
          //         <p v-else v-for="(item,index) in fileList" :key="index" style="color:#00469c;cursor:pointer;" @click="fieldDownloadBtn(item)">{{item.fileName}}</p>
          //       </div>`,
          //       data(){
          //         return{
          //         }
          //       },
          //       computed:{
          //         fileList(){
          //           try {
          //             if(this.data.file && this.data.file != ''){
          //               return  JSON.parse(this.data.file)
          //             }else{
          //               return []
          //             }
          //           }catch(err){
          //             return []
          //           }
          //         }
          //       },
          //       mounted() {
          //       },
          //       methods:{
          //         fieldDownloadBtn(item){
          //           if(item =="upload"){

          //           }else{
          //                this.$API.fileService.downloadPrivateFileTypeOne(item.fileValue).then(res => {
          //               let link = document.createElement("a"); // 创建元素
          //               link.style.display = "none";
          //               let blob = new Blob([res.data], {type:"application/x-msdownload",});
          //               let url = window.URL.createObjectURL(blob);
          //               link.href = url;
          //               link.setAttribute("download", item.fileName); //文件命名
          //               link.click(); // 点击下载
          //               window.URL.revokeObjectURL(url);
          //             })
          //             .catch(() => {
          //               this.$toast({ content: this.$t("导出失败，请重试!"), type: "warning" });
          //             });
          //           }

          //         }
          //       }
          //     }),
          //   };
          // };
          //  obj.editTemplate=()=>{
          //                 return {
          //     template: Vue.component("modelName", {
          //       template: `<div>
          //         <p v-if="fileList.length<1" style="color:#00469c;cursor:pointer;" @click="fieldDownloadBtn('upload')">{{fileList.length>0 ? fileList[0].fileName:"点击上传"}}</p>
          //         <p v-else v-for="(item,index) in fileList" :key="index" style="color:#00469c;cursor:pointer;" @click="fieldDownloadBtn(item)">{{item.fileName}}</p>
          //       </div>`,
          //       data(){
          //         return{
          //         }
          //       },
          //       computed:{
          //         fileList(){
          //           try {
          //             if(this.data.file && this.data.file != ''){
          //               return  JSON.parse(this.data.file)
          //             }else{
          //               return []
          //             }
          //           }catch(err){
          //             return []
          //           }
          //         }
          //       },
          //       mounted() {
          //       },
          //       methods:{
          //         fieldDownloadBtn(){
          //           if(this.fileList.length > 0)return
          //              console.log("上传文件");
          //         }
          //       }
          //     }),
          //   };
          //  }

          // console.log(item,"102");
          // obj.template = () => {
          //   return {
          //     template: Vue.component("modelName", {
          //       template: `<div>
          //         <p v-for="(item,index) in fileList" :key="index" style="color:#00469c;cursor:pointer;" @click="fieldDownloadBtn(item)">{{item.fileName}}</p>
          //       </div>`,
          //       data(){
          //         return{

          //         }
          //       },
          //       computed:{
          //         fileList(){
          //           try {
          //             if(this.data.file && this.data.file != ''){
          //               return  JSON.parse(this.data.file)
          //             }else{
          //               return []
          //             }
          //           }catch(err){
          //             return []
          //           }
          //         }
          //       },
          //       methods:{
          //         fieldDownloadBtn(item){
          //           console.log(this.data,"this.data.file");
          //           if (this.data.file) {
          //             this.$API.fileService.downloadPrivateFileTypeOne(item.fileValue).then(res => {
          //               let link = document.createElement("a"); // 创建元素
          //               link.style.display = "none";
          //               let blob = new Blob([res.data], {type:"application/x-msdownload",});
          //               let url = window.URL.createObjectURL(blob);
          //               link.href = url;
          //               link.setAttribute("download", item.fileName); //文件命名
          //               link.click(); // 点击下载
          //               window.URL.revokeObjectURL(url);
          //             })
          //             .catch(() => {
          //               this.$toast({ content: this.$t("导出失败，请重试!"), type: "warning" });
          //             });
          //           }
          //         }
          //       }
          //     }),
          //   };
          // };
          //#endregion
        }
        return obj
      })

      this.columnData = [...oneArr, ...arr, ...tweArr] // 拼接数据
      let res1 = await this.$API.accessangeMange.getQualificationItemList({
        applyCode: this.fromInfo.applyCode
      })
      this.dataSource = res1.data

      // 循环审查单 表头的逻辑
      this.dataSource.forEach((item) => {
        // 循环fieldList
        item['remakeFieldValue'] = item.remark
        delete item.remark
        item.fieldList.forEach((e) => {
          item[e.fieldCode] = e.fieldValue ? e.fieldValue : ''
          if (e.fieldCode == 'file') {
            item['fieldComment'] = e.fieldComment
          }
        })
        // if (item.supplierStatus == '0') {
        //   item.supplierStatusTxT = '待处理'
        // } else if (item.supplierStatus == '10') {
        //   item.supplierStatusTxT = '待提交'
        // } else if (item.supplierStatus == '20') {
        //   item.supplierStatusTxT = '待审批'
        // } else if (item.supplierStatus == '30') {
        //   item.supplierStatusTxT = '被驳回'
        // } else if (item.supplierStatus == '40') {
        //   item.supplierStatusTxT = '已确认'
        // }
      })
      console.log(
        'this.pageConfig[0].grid.columnData',
        this.fromInfoTop,
        this.columnData,
        this.pageConfig
      )

      this.pageConfig = pageConfig(this.columnData, this.dataSource) // 处理表头和表格里内容
      if (this.fromInfoTop.supplierStatus == '40' || this.fromInfoTop.supplierStatus == '20') {
        // let _columnData = this.pageConfig[0].grid.columnData
        // _columnData.map((item) => {
        //   item.allowEditing = false
        // })
        this.$set(this.pageConfig[0], 'toolbar', [[]])
        // this.$set(this.pageConfig[0].grid, 'columnData', _columnData)
      }
    },
    handleClickToolBar(e) {
      if (e.toolbar.id != 'Edit') return
      // if (this.fromInfo.status != "0" || this.fromInfo.status != "30") {
      //   return this.$toast({
      //     content: "只有待处理和被驳回可以编辑!",
      //     type: "warning",
      //   });
      // }
      // 拿到表格项
      let list = e.gridRef.getMtechGridRecords()
      this.showDialog(list)
    },
    showDialog(list) {
      if (!list[0])
        return this.$toast({
          content: this.$t('请选择一行数据再进行编辑'),
          type: 'warning'
        })
      if (list[0].supplierStatus == '40' || list[0].supplierStatus == '20') {
        return this.$toast({
          content: this.$t('已确认及待审批状态下不可进行编辑'),
          type: 'warning'
        })
      }
      // 只能提交一行
      if (list.length != 1) {
        this.$toast({ content: this.$t('请选择一行'), type: 'warning' })
        return
      }
      let arr = []
      this.fromData &&
        this.fromData.forEach((item) => {
          if (item.qualificationCode == list[0].qualificationCode) {
            arr.push(item)
          }
        })
      this.$nextTick(() => {
        this.$dialog({
          modal: () => import('./components/addDialog.vue'),
          //需要传的参数
          data: {
            title: this.$t('编辑'),
            data: list[0], //点击选中的那一项
            fromData: arr
          },
          success: (val) => {
            let index = -1
            console.log(val, 'valval')
            this.dataSource.forEach((item, i) => {
              if (item.qualificationCode == val.qualificationCode) {
                index = i
              }
            })
            console.log(val)
            this.dataSource.splice(index, 1, val)
            this.pageConfig = pageConfig(this.columnData, this.dataSource)
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      })
    },
    handleClickCellTitle(e) {
      //单元格Title点击
      const { field, data } = e
      if (field === 'modelName' && data && data.id) {
        console.log(e, 'eee')
        this.$API.fileService
          .downloadPrivateFileTypeOne(data.modelUrl)
          .then((res) => {
            let link = document.createElement('a') // 创建元素
            link.style.display = 'none'
            let blob = new Blob([res.data], {
              type: 'application/x-msdownload'
            })
            let url = window.URL.createObjectURL(blob)
            link.href = url
            link.setAttribute('download', `${data.modelName}`) // 给下载后的文件命名
            link.click() // 点击下载
            window.URL.revokeObjectURL(url)
          })
          .catch(() => {
            this.$toast({
              content: this.$t('导出失败，请重试!'),
              type: 'warning'
            })
          })
      } else if (field === 'file' && !data.fieldValue) {
        let list = e.gridRef.getMtechGridRecords()
        const tarList = list.filter((item) => item.id == data.id)
        let arr = []
        this.fromData &&
          this.fromData.forEach((item) => {
            if (item.qualificationCode == tarList.qualificationCode) {
              arr.push(item)
            }
          })
        this.$nextTick(() => {
          this.$dialog({
            modal: () => import('./components/addDialog.vue'),
            //需要传的参数
            data: {
              title: this.$t('编辑'),
              data: tarList, //点击选中的那一项
              fromData: arr
            },
            success: (val) => {
              let index = -1
              this.dataSource.forEach((item, i) => {
                if (item.qualificationCode == val.qualificationCode) {
                  index = i
                }
              })
              this.dataSource.splice(index, 1, val)
              this.pageConfig = pageConfig(this.columnData, this.dataSource)
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        })
      }
    },
    // 返回==按钮
    gobank() {
      this.$router.go(-1)
    },
    // 保存并提交==按钮
    saveAndConfirm() {
      this.saveInterface(1)
    },
    // 保存==按钮
    save() {
      this.saveInterface(0)
    },
    // 保存/提交==接口
    saveInterface(optionType) {
      // const grid = this.$refs.templateRef.getCurrentTabRef().grid
      // let dataSource = grid.getCurrentViewRecords()
      let params = {
        optionType, //操作类型，0-保存，1-提交
        applyCode: this.fromInfo.applyCode,
        itemList: []
      }
      // let flag = true
      console.log(this.dataSource, 'this.dataSource')
      console.log(this.fromData, 'this.fromData')
      this.dataSource.forEach((item) => {
        this.fromData.forEach((e) => {
          let obj = {
            qualificationCode: item.qualificationCode,
            applyCode: item.applyCode,
            fieldCode: e.value,
            fieldName: e.text,
            fieldValue: item[e.value],
            fieldComment: e.value == 'file' ? item.fieldComment : ''
          }
          params.itemList.push(obj)
        })
      })
      this.$API.accessangeMange.saveQualification(params).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          if (optionType === 0) {
            this.init()
            this.getQualificationDescInterface()
          } else {
            this.$router.go(-1)
          }
        }
      })
    },
    // 监听文件列表变化
    handleFileChange(rowId, fileList) {
      this.dataSource.forEach((item) => item.id === rowId && (item.file = JSON.stringify(fileList)))
      this.pageConfig = pageConfig(this.columnData, this.dataSource)
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
  width: 100%;
  padding-top: 20px;
  .aptitude-head {
    width: 100%;
    height: 124px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    position: relative;
    padding: 26px 30px;
    .aptitude-title {
      span:first-child {
        font-size: 20px;
        font-weight: 700;
        color: rgba(41, 41, 41, 1);
        margin-right: 14px;
      }
      span:last-child {
        display: inline-block;
        color: rgba(99, 134, 193, 1);
        background: rgba(99, 134, 193, 0.1);
        border-radius: 2px;
        padding: 5px 10px;
      }
    }
    .gobank {
      position: absolute;
      top: 32px;
      right: 50px;
      font-size: 14px;
      color: rgba(0, 70, 156, 1);
      font-weight: 500;
      span {
        cursor: pointer;
        margin-left: 30px;
      }
    }
    .aptitude-encoder {
      font-size: 12px;
      color: #292929;
      margin-top: 12px;
      margin-bottom: 16px;
      .encoder {
        margin-right: 24px;
      }
    }
    .aptitude-designation {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
      .designation {
        margin-right: 20px;
      }
    }
  }
}

.detail-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 30px 30px 20px 30px;
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid #e8e8e8ff;
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .right_but {
    display: inline-block;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #00469cff;
    margin: 0 10px;
  }
  .left {
    color: #292929;
    .title {
      font-size: 20px;
      font-weight: 600;
    }
    .form {
      padding: 10px 0;
      font-size: 12px;
      font-weight: normal;
    }
    .form_div {
      display: inline-block;
      padding-right: 20px;
    }
    .data {
      padding: 10px 0;
      font-size: 14px;
      font-weight: 600;
      span {
        margin-left: 3px;
        color: #00469c;
      }
    }
  }
}
.detail-content {
  background: #e8e8e8;
  /deep/.e-grid {
    .e-rowcell {
      text-align: left !important;
      .grid-edit-column {
        display: inline-block !important;
      }
    }
  }
}
.full-height {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
