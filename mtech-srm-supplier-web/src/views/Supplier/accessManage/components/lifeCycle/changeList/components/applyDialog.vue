<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="applyName" :label="$t('申请单名称：')">
          <mt-input
            v-model="formObject.applyName"
            float-label-type="Never"
            :placeholder="$t('请输入申请单名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="taskInstanceId" :label="$t('调查表：')">
          <mt-select
            ref="taskInstanceRef"
            v-model="formObject.taskInstanceId"
            float-label-type="Never"
            :data-source="taskInstanceList"
            :fields="{ text: 'taskName', value: 'id' }"
            :placeholder="$t('请选择调查表')"
            @change="changeTaskInstance"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="relationIds" :label="$t('信息变更关联客户：')">
          <mt-multi-select
            ref="customerIdRef"
            v-model="formObject.relationIds"
            float-label-type="Never"
            :data-source="relationList"
            :fields="{ text: 'enterpriseName', value: 'enterpriseId' }"
            :placeholder="$t('请选择模关联客户')"
            @change="changeRelation"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item :label="$t('原因说明：')" prop="applyReason">
          <mt-input
            :multiline="true"
            :rows="2"
            type="text"
            float-label-type="Never"
            v-model="formObject.applyReason"
            :placeholder="$t('请输入变更原因')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
      <div class="relation-list">
        <mt-data-grid
          ref="relationListRef"
          height="100"
          :data-source="selectRelationList"
          :column-data="relationColumnData"
          :allow-filtering="false"
          :allow-sorting="false"
          :allow-paging="false"
        ></mt-data-grid>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
        // {
        //   click: this.confirm,
        //   buttonModel: { isPrimary: "true", content: "保存并进入" },
        // },
      ],
      formObject: {
        applyName: null, //申请单名称
        taskInstanceId: null, //任务实例id
        taskInstanceName: null, //任务实例名称
        relationIds: null,
        applyReason: null
      },
      formRules: {
        applyName: [
          {
            required: true,
            message: this.$t('请输入申请单名称'),
            trigger: 'blur'
          }
        ],
        taskInstanceId: [{ required: true, message: this.$t('请选择调查表'), trigger: 'blur' }],
        relationIds: [
          {
            required: true,
            message: this.$t('请选择关联客户'),
            trigger: 'blur'
          }
        ],
        applyReason: [
          {
            required: true,
            message: this.$t('请输入变更申请原因'),
            trigger: 'blur'
          }
        ]
      },
      //调查表列表
      taskInstanceList: [],
      //关联客户列表
      relationList: [],
      selectForm: {}, //当前选中的表单，taskInstance
      selectRelationList: [], //当前选中的客户列表
      editStatus: false,
      relationColumnData: [
        {
          field: 'enterpriseCode',
          headerText: this.$t('客户编码')
        },
        {
          field: 'enterpriseName',
          headerText: this.$t('客户企业名称')
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.selectForm) {
      let _form = { ...this.modalData.selectForm }
      // this.taskInstanceList = [_form];//从调查表页面，带过来的数据，暂时不重置taskInstanceList数据源
      this.selectForm = _form
      this.formObject.taskInstanceId = _form.id
      this.formObject.taskInstanceName = _form.taskName
    }
    this.getAllTaskList()
  },
  methods: {
    getAllTaskList() {
      this.$API.customerProfileChange
        .getAllCompletedTask({
          relationId: this.modalData.partnerRelationId
        })
        .then((res) => {
          this.taskInstanceList = res.data
        })
    },
    //根据当前选中的表单，获取关联客户列表
    getRelationCustomers() {
      console.log('当前选中的表单', this.selectForm)
      if (this.selectForm && this.selectForm.id) {
        this.$API.customerProfileChange
          .getTaskRelation({
            taskInstanceId: this.selectForm.id
          })
          .then((res) => {
            console.log('任务关联关系--', res)
            let _data = res.data
            if (Array.isArray(_data.supplierPartnerRelationList)) {
              this.relationList = _data.supplierPartnerRelationList
              if (this.relationList.length > 0) {
                this.formObject.relationIds = [this.relationList[0]['id']]
              }
            } else {
              //关联关系--无数据--清空数据
              this.relationList = []
              this.selectRelationList = []
            }
          })
      }
    },
    //切换选择：调查表
    changeTaskInstance(e) {
      console.log('切换---调查表', e)
      if (e && e.itemData) {
        this.selectForm = e.itemData
        this.getRelationCustomers()
      } else {
        //当前切换，无数据，关联的数据清空
        this.relationList = []
        this.selectRelationList = []
        this.selectForm = null
      }
    },
    // select数据赋值
    getSelectList(_list, object = 'relationList') {
      let _res = []
      _list.forEach((e) => {
        _res.push(this[object].find((f) => f.id === e))
      })
      return _res
    },
    //关联客户--切换
    changeRelation(e) {
      if (e.value && Array.isArray(e.value) && e.value.length) {
        this.selectRelationList = this.getSelectList(e.value)
      } else {
        this.selectRelationList = []
      }
    },
    saveApply(type) {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          // //处理下拉框数据赋值
          // this.$utils.assignDataFromRefs(params, [
          //   {
          //     key: "taskInstanceId", //模板类型 templateTypeId下拉框数据
          //     ref: this.$refs.taskInstanceRef.ejsRef,
          //     fields: { taskInstanceName: "taskName" },
          //   },
          // ]);
          let relationDTOList = []
          this.selectRelationList.forEach((e) => {
            relationDTOList.push({
              // applyId: 0, //申请主表id
              bizType: 0, //业务类型,0:公司，1：品类
              customerEnterpriseCode: e.customerEnterpriseCode, //客户编号
              customerEnterpriseName: e.customerEnterpriseName, //客户名称
              // id: 0, //id
              partnerArchiveId: e.partnerArchiveId, //客户档案id
              // partnerCategoryId: "", //品类关系id
              // partnerPunishId: 0, //黑名单id
              partnerRelationId: e.id //客户关系id
              // relatedBillId: 0, //关联单据id
              // remark: "", //备注
              // tenantId: 0, //租户id
            })
          })
          let submitParams = {
            //供方-申请单DTO
            applyInfoDTO: {
              // applyCode: "", //申请单编码
              // applyFinishTime: "", //单据完成时间
              applyName: params.applyName, //申请单名称
              applyReason: params.applyReason //申请原因
              // applyStatus: 0, //申请状态
              // applyType: "", //申请单类型
              // applyerOrgId: 0, //申请人公司id
              // applyerOrgName: "", //申请人公司
              // applyerDeptId: 0, //申请人部门id
              // applyerDeptName: "", //申请人部门
              // applyerId: 0, //申请人id
              // applyerName: "", //申请人
              // id: 0, //id
              // remark: "", //备注
              // tenantId: 0, //租户id
            },
            //供方-变更申请DTO
            formTaskDTO: {
              // applyId: 0, //申请主表id
              // id: 0, //id
              // remark: "", //备注
              taskInstanceId: this.selectForm.id, //任务实例id
              taskInstanceName: this.selectForm.taskName, //任务实例名称
              taskType: 0 //任务类型
              // tenantId: 0, //租户id
            },
            // //供方-变更申请DTO
            // formTaskDTO: {
            //   // applyId: 0, //申请主表id
            //   // id: 0, //id
            //   // remark: "", //备注
            //   taskInstanceId: params.taskInstanceId, //任务实例id
            //   taskInstanceName: params.taskInstanceName, //任务实例名称
            //   taskType: 0, //任务类型
            //   // tenantId: 0, //租户id
            // },
            //供方-申请单关系DTO
            relationDTOList
          }
          console.log('表单--提交--参数', params, submitParams)
          // this.$emit("confirm-function");
          if (this.editStatus) {
            // console.log("编辑", submitParams);
            // this.$API.customerProfileChange
            //   .updateApplyInfo(submitParams)
            //   .then((res) => {
            //     if (res.code == 200) {
            //       this.$emit("confirm-function");
            //     }
            //   });
          } else {
            console.log('新增', submitParams)
            this.$API.customerProfileChange.addApplyInfo(submitParams).then((res) => {
              if (res.code == 200) {
                this.$emit('confirm-function', {
                  type,
                  data: res.data
                })
              }
            })
          }
        }
      })
    },
    confirm() {
      this.saveApply('close')
    },
    confirmDetail() {
      this.saveApply('detail')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
