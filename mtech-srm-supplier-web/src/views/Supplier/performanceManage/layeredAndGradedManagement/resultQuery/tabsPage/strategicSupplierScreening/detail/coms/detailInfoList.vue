<template>
  <div class="main_class">
    <div v-for="item in infolIist" :key="item.title">
      <!-- title区 -->
      <div class="bar_title">
        <span>{{ item.title }}</span>
      </div>
      <!-- 汇总区 -->
      <div class="score_sum">
        <span>
          {{ $t('满分：') }}
          <i>{{ item.fullMark }}</i>
        </span>
        <span style="margin-left: 10px">
          {{ $t('得分：') }}
          <i>{{ item.score }}</i>
        </span>
      </div>
      <!-- 详细数据区 -->
      <div>
        <mt-template-page
          ref="templateRef"
          :template-config="item.pageConfig"
          :hidden-tabs="true"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { pageConfig } from './config/index.js'

export default {
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      pageConfig: [],
      infolIist: []
    }
  },
  mounted() {
    setTimeout(() => {
      this.list.forEach((item) => {
        let tempConfig = pageConfig()
        tempConfig[0].grid.dataSource = item.buyerAssessLayeredLevelResultDetailResponseList
        this.infolIist.push({
          title: item.dimName,
          score: item.score,
          fullMark: item.fullMark,
          pageConfig: tempConfig
        })
      })
    }, 1000)
  }
}
</script>

<style lang="scss" scoped>
::v-deep .toolbar-container {
  height: 0;
  padding: 5px;
}
.bar_title {
  height: 35px;
  background-color: #f2f2f2;
  border: 1px solid #ede8e8;
  padding: 10px;
  font-weight: bold;
}
.score_sum {
  padding: 10px 0 0 0;
}
</style>
