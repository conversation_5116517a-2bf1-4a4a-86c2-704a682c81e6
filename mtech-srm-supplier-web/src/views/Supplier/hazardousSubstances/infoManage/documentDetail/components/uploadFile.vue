<template>
  <div>
    <input
      v-if="disabled"
      ref="file"
      type="file"
      style="display: none"
      @change="chooseFiles"
      multiple="multiple"
      accept=".xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar,.pdf,.PDF"
    />
    <mt-button v-if="disabled" class="mgn-left-10" @click="browseBtn()">{{
      $t('上传文件')
    }}</mt-button>
    <p
      style="margin-top: 5px"
      v-for="(item, index) in data.harmfulCategoryFileRequestList"
      :key="index"
    >
      <a @click="preview(item)">{{ item.fileName }}</a>
      <span v-if="disabled" style="margin-left: 10px; cursor: pointer" @click="deleteFile(item)">{{
        $t('删除')
      }}</span>
      <span style="margin-left: 10px; cursor: pointer" @click="upload(item)">{{ $t('下载') }}</span>
    </p>
  </div>
</template>
<script>
import cloneDeep from 'lodash/cloneDeep'

export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    rowId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      data: {
        harmfulCategoryFileRequestList: []
      },
      harmfulCategoryFileRequestList: [],
      value: [],
      sceneList: [],
      allowFileType: [
        'xls',
        'xlsx',
        'doc',
        'docx',
        'pdf',
        'ppt',
        'pptx',
        'png',
        'jpg',
        'zip',
        'rar'
      ]
    }
  },
  mounted() {
    if (this.rowId) {
      this.getFileList()
    }
  },
  methods: {
    getFileList() {
      this.$API.hazardousSubstances
        .getHazardousSubstancesDetailId({ id: this.rowId })
        .then((res) => {
          const _list = res.data ?? []
          const _harmfulCategoryFileRequestList = []
          const _map = new Map()
          _list.map((item) => {
            if (!_map.get(item.fileName)) {
              _harmfulCategoryFileRequestList.push(item)
              _map.set(item.fileName, true)
            }
          })
          this.data.harmfulCategoryFileRequestList = _harmfulCategoryFileRequestList
          this.$emit('listChange', this.data)
        })
    },
    addFileInfo(dataList, fileInfo) {
      if (!fileInfo) return
      const _dataList = cloneDeep(dataList)
      const _fileInfo = cloneDeep(fileInfo)
      if (dataList.length === 0) {
        _dataList.push(fileInfo)
        return _dataList
      }
      let _flag = _dataList.some((item) => item.fileName === _fileInfo.fileName)
      if (!_flag) {
        _dataList.push(fileInfo)
      }
      return _dataList
    },
    chooseFiles(data) {
      this.$loading()
      console.log('data.targetdata.targetdata.target', data.target)
      let { files } = data.target
      files = Object.values(files)
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // 您未选择需要上传的文件
        return
      } else if (files.length > 5) {
        this.$hloading()
        this.$toast({
          content: this.$t('一次性最多选择5个文件'),
          type: 'warning'
        })
        return
      }
      let bol = files.some((item) => {
        let _tempInfo = item.name.split('.')
        return _tempInfo.length < 2 || !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])
      })
      let repeatFileNameCheck = this.data.harmfulCategoryFileRequestList.some((item) => {
        if (files.some((n) => n.name === item.fileName)) return true
      })
      if (repeatFileNameCheck) {
        this.$toast({
          type: 'error',
          content: this.$t('文件已存在，请勿重复上传')
        })
        this.$hloading()
        return
      }
      if (bol) {
        this.$toast({
          content: this.$t('文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      bol = files.some((item) => {
        return item.size > params.limit * 1024
      })
      if (bol) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      this.$refs.file.value = ''
      this.uploadFile(files)
    },
    uploadFile(files) {
      let arr = []
      const _this = this
      files.forEach((item) => {
        let _data = new FormData()
        _data.append('UploadFiles', item)
        _data.append('useType', 1)
        arr.push(this.$API.SupplierPunishment.fileUploadSitHaz(_data))
      })
      Promise.all(arr).then((res) => {
        res.forEach((item) => {
          if (item.code == 200) {
            item.data.fileId = item.data.fileId ? item.data.fileId : item.data.id
            item.data.fileUrl = item.data.fileUrl ? item.data.fileUrl : item.data.url
            const fileInfo = cloneDeep(item.data)
            delete fileInfo.id
            if (this.data.harmfulCategoryFileRequestList === null) {
              this.data.harmfulCategoryFileRequestList = []
            }
            this.data.harmfulCategoryFileRequestList = this.addFileInfo(
              this.data.harmfulCategoryFileRequestList,
              fileInfo
            )
            console.log('scoringFilelnfos123', this.data.harmfulCategoryFileRequestList)
          }
        })
        this.$hloading()
        _this.$emit('listChange', this.data)
      })
    },
    browseBtn() {
      this.$refs.file.click()
    },
    preview(item) {
      let params = {
        id: item.fileId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    upload(item) {
      this.$API.SupplierPunishment.fileDownload(item.fileId).then((res) => {
        let link = document.createElement('a')
        link.style.display = 'none'
        let blob = new Blob([res.data], { type: 'application/x-msdownload' })
        let url = window.URL.createObjectURL(blob)
        link.href = url
        link.setAttribute('download', `${item.fileName}`) // 给下载后的文件命名
        link.click() // 点击下载
        window.URL.revokeObjectURL(url)
      })
    },
    deleteFile(item) {
      const index = this.data.harmfulCategoryFileRequestList.findIndex(
        (file) => file.id === item.id
      )
      this.$API.hazardousSubstances.deleteHazardousSubstancesHeaderInfo([item.id]).then((res) => {
        if (res) {
          this.$toast({
            type: 'success',
            content: this.$t('删除成功')
          })
        }
      })
      this.data.harmfulCategoryFileRequestList.splice(index, 1)
      this.$emit('deleteFile', this.data)
      this.$emit('listChange', this.data)
    }
  }
}
</script>
