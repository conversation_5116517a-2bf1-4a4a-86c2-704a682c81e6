import { i18n } from '@/main.js'
import dateSelect from '../components/dateSelect.vue'
import input from '../components/input.vue'
import cloneDeep from 'lodash/cloneDeep'

export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: 150,
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    template: () => {
      const template = {
        template: `<span>{{ data.status }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    }
  },
  {
    width: 150,
    field: 'testTypeName',
    headerText: i18n.t('测试类型')
  },
  {
    width: 250,
    field: 'testOrg',
    headerText: i18n.t('测试机构'),
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('测试机构')}}</span>
          </div>
        `
      }
      return { template }
    }
  },
  {
    width: 120,
    field: 'reportTime',
    headerText: i18n.t('出具报告日期')
  },
  {
    width: 180,
    field: 'nextReportTime',
    allowEditing: false,
    headerText: i18n.t('下次报告更新日期')
  },
  {
    width: 200,
    field: 'harmfulCategoryFileRequestList',
    allowEditing: false,
    headerText: i18n.t('报告附件'),
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('报告附件')}}</span>
          </div>
        `
      }
      return { template }
    }
  },
  {
    width: 150,
    field: 'dataDetail',
    headerText: i18n.t('测试数据明细')
  },
  {
    width: 250,
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

export const editColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: 150,
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    width: 150,
    field: 'testTypeName',
    headerText: i18n.t('测试类型')
  },
  {
    width: 250,
    field: 'testOrg',
    headerText: i18n.t('测试机构'),
    allowEditing: true,
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('测试机构')}}</span>
          </div>
        `
      }
      return { template }
    },
    template: () => ({ template: input })
  },
  {
    width: 120,
    field: 'reportTime',
    headerText: i18n.t('出具报告日期'),
    allowEditing: true,
    template: () => ({ template: dateSelect }),
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('出具报告日期')}}</span>
          </div>
        `
      }
      return { template }
    }
  },
  {
    width: 180,
    field: 'nextReportTime',
    allowEditing: true,
    headerText: i18n.t('下次报告更新日期'),
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('下次报告更新日期')}}</span>
          </div>
        `
      }
      return { template }
    },
    template: () => ({ template: dateSelect })
  },
  {
    width: 200,
    field: 'harmfulCategoryFileRequestList',
    headerText: i18n.t('报告附件'),
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('报告附件')}}</span>
          </div>
        `
      }
      return { template }
    },
    template: () => {
      const template = {
        template: `<div>
            <input
              ref="file"
              type="file"
              style="display: none"
              @change="chooseFiles"
              multiple="multiple"
              accept=".xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar,.pdf,.PDF"
            />
            <mt-button v-if="isEdit" class="mgn-left-10" @click="browseBtn()">{{
              $t("上传文件")
            }}</mt-button>
            <p
              style="margin-top:5px;"
              v-for="(item,index) in data.harmfulCategoryFileRequestList"
              :key="index"
            >
              <a @click="preview(item)">{{item.fileName}}</a>
              <span v-if="isEdit" style="margin-left:10px;cursor: pointer;" @click="deleteFile(item)">{{ $t('删除') }}</span>
              <span style="margin-left:10px;cursor: pointer;" @click="upload(item)">{{ $t('下载') }}</span>
            </p>
          </div>`,
        data() {
          return {
            data: {
              harmfulCategoryFileRequestList: []
            },
            sceneList: [],
            allowFileType: [
              'xls',
              'xlsx',
              'doc',
              'docx',
              'pdf',
              'ppt',
              'pptx',
              'png',
              'jpg',
              'zip',
              'rar'
            ]
          }
        },
        computed: {
          isEdit() {
            return [10, '10', 11, '11', 15, '15', 40, '40'].includes(this.$route.query?.status)
          }
        },
        mounted() {
          console.log('scoringFilelnfosscoringFilelnfos', this.data.scoringFilelnfos)
          const _row = this.rowInfo()
          this.$API.hazardousSubstances
            .getHazardousSubstancesDetailId({ id: this.data['id'] })
            .then((res) => {
              const _list = res.data ?? []
              const _harmfulCategoryFileRequestList = []
              const _map = new Map()
              _list.map((item) => {
                if (!_map.get(item.fileName)) {
                  _harmfulCategoryFileRequestList.push(item)
                  _map.set(item.fileName, true)
                }
              })
              this.data.harmfulCategoryFileRequestList = _harmfulCategoryFileRequestList
              _row['harmfulCategoryFileRequestList'] = _harmfulCategoryFileRequestList
            })
        },
        methods: {
          addFileInfo(dataList, fileInfo) {
            if (!fileInfo) return
            const _dataList = cloneDeep(dataList)
            const _fileInfo = cloneDeep(fileInfo)
            if (dataList.length === 0) {
              _dataList.push(fileInfo)
              return _dataList
            }
            let _flag = _dataList.some((item) => item.fileName === _fileInfo.fileName)
            if (!_flag) {
              _dataList.push(fileInfo)
            }
            return _dataList
          },
          rowInfo() {
            const _dataSource = this.$parent.dataSource
            const _dateRow = _dataSource.filter((item) => {
              if (item.id === this.data['id']) {
                return true
              }
            })
            return _dateRow[0]
          },
          chooseFiles(data) {
            this.$loading()
            console.log('data.targetdata.targetdata.target', data.target)
            let { files } = data.target
            files = Object.values(files)
            let params = {
              type: 'array',
              limit: 50 * 1024,
              msg: this.$t('单个文件，限制50M')
            }
            if (files.length < 1) {
              this.$hloading()
              // 您未选择需要上传的文件
              return
            } else if (files.length > 5) {
              this.$hloading()
              this.$toast({
                content: this.$t('一次性最多选择5个文件'),
                type: 'warning'
              })
              return
            }
            let bol = files.some((item) => {
              let _tempInfo = item.name.split('.')
              return (
                _tempInfo.length < 2 ||
                !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])
              )
            })
            let repeatFileNameCheck = this.data.harmfulCategoryFileRequestList.some((item) => {
              if (files.some((n) => n.name === item.fileName)) return true
            })
            if (repeatFileNameCheck) {
              this.$toast({
                type: 'error',
                content: this.$t('文件已存在，请勿重复上传')
              })
              this.$hloading()
              return
            }
            if (bol) {
              this.$toast({
                content: this.$t('文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'),
                type: 'warning'
              })
              this.$hloading()
              return
            }
            bol = files.some((item) => {
              return item.size > params.limit * 1024
            })
            if (bol) {
              this.$hloading()
              this.$toast({
                content: params.msg
              })
              return
            }
            this.$refs.file.value = ''
            this.uploadFile(files)
          },
          uploadFile(files) {
            const _dataSource = this.$parent.dataSource
            const _lineObj = _dataSource.filter((item) => item.id === this.data.id)[0]
            let arr = []
            files.forEach((item) => {
              let _data = new FormData()
              _data.append('UploadFiles', item)
              _data.append('useType', 1)
              arr.push(this.$API.SupplierPunishment.fileUpload(_data))
            })
            Promise.all(arr).then((res) => {
              res.forEach((item) => {
                if (item.code == 200) {
                  item.data.fileId = item.data.fileId ? item.data.fileId : item.data.id
                  item.data.fileUrl = item.data.fileUrl ? item.data.fileUrl : item.data.url
                  const fileInfo = cloneDeep(item.data)
                  delete fileInfo.id
                  if (this.data.harmfulCategoryFileRequestList === null) {
                    this.data.harmfulCategoryFileRequestList = []
                    _lineObj['harmfulCategoryFileRequestList'] = []
                  }
                  this.data.harmfulCategoryFileRequestList = this.addFileInfo(
                    this.data.harmfulCategoryFileRequestList,
                    fileInfo
                  )
                  _lineObj.harmfulCategoryFileRequestList = this.addFileInfo(
                    _lineObj.harmfulCategoryFileRequestList,
                    fileInfo
                  )
                }
              })
              this.$hloading()
              this.$parent.$emit('cellEdit', this.data, 'harmfulCategoryFileRequestList')
            })
          },
          browseBtn() {
            this.$refs.file.click()
          },
          preview(item) {
            let params = {
              id: item.fileId,
              useType: 1
            }
            this.$API.SupplierPunishment.filepreview(params).then((res) => {
              window.open(res.data)
            })
          },
          upload(item) {
            this.$API.SupplierPunishment.fileDownload(item.fileId).then((res) => {
              let link = document.createElement('a')
              link.style.display = 'none'
              let blob = new Blob([res.data], { type: 'application/x-msdownload' })
              let url = window.URL.createObjectURL(blob)
              link.href = url
              link.setAttribute('download', `${item.fileName}`) // 给下载后的文件命名
              link.click() // 点击下载
              window.URL.revokeObjectURL(url)
            })
          },
          deleteFile(item) {
            const index = this.data.harmfulCategoryFileRequestList.findIndex(
              (file) => file.id === item.id
            )
            this.$API.hazardousSubstances
              .deleteHazardousSubstancesListInfo([item.id])
              .then((res) => {
                if (res) {
                  this.$toast({
                    type: 'success',
                    content: this.$t('删除成功')
                  })
                }
              })
            this.data.harmfulCategoryFileRequestList.splice(index, 1)
            this.$parent.$emit('cellEdit', this.data, 'harmfulCategoryFileRequestList')
          }
        }
      }
      return { template }
    }
    // template: () => ({ template: dateSelect })
  },
  {
    width: 150,
    field: 'dataDetail',
    headerText: i18n.t('测试数据明细'),
    headerTemplate: (data) => {
      const template = {
        template: `
          <div class="headers">
            <span v-if="data.testType === 'A'" style="color: red">*</span>
            <span class="e-headertext">{{$t('测试数据明细')}}</span>
          </div>
        `,
        data() {
          return {
            data
          }
        }
      }
      return { template }
    },
    template: () => {
      const template = {
        template: `<div>
          <span>{{ $t('维护明细') }}</span>
        </div>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {
          this.$bus.$on('dataDetailChange', (list) => {
            if (list) {
              const _attrList = [
                'bbp',
                'cd',
                'cr6',
                'dbp',
                'dehp',
                'dibp',
                'hg',
                'pb',
                'pbb',
                'pbde'
              ]
              _attrList.map((item) => {
                this.data[item] = list[item]
              })
            }
          })
        },
        methods: {}
      }
      return { template }
    },
    cellTools: [
      {
        title: i18n.t('维护明细'),
        id: 'operateInfo',
        visibleCondition: (data) => {
          return data.testType === 'A'
        }
      },
      {
        title: i18n.t('维护明细'),
        id: '',
        visibleCondition: (data) => {
          return data.testType !== 'A'
        }
      }
    ]
  },
  {
    width: 250,
    field: 'remark',
    headerText: i18n.t('备注'),
    template: () => ({ template: input })
  },
  {
    width: 120,
    field: 'operation',
    headerText: i18n.t('操作'),
    cellTools: [
      {
        title: i18n.t('删除'),
        id: 'lineDelete'
      }
    ]
  }
]
