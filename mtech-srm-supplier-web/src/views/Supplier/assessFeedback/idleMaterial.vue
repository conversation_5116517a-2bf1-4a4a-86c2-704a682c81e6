<template>
  <div class="full-height">
    <div class="detail-card">
      <div class="desc">
        <div class="desc-title-box">
          <span class="title">
            <div class="detail-item">
              <span>{{ $t('索赔单编号：') }}</span>
              <span>{{ formObject.claimCode }}</span>
            </div>
          </span>
        </div>
        <div class="desc-detail-box mb_7">
          <div class="detail-item">
            <span>{{ $t('状态：') }}</span>
            <span>{{ statusLabel }}</span>
          </div>
          <div class="detail-item">
            <span>{{ $t('索赔维度：') }}</span>
            <span>{{ formObject.claimTypeName }}</span>
          </div>
          <div class="detail-item">
            <span style="display: inline-block">{{ $t('索赔月份：') }}</span>
            <span class="reason">{{ formObject.claimMonth }}</span>
          </div>
          <div class="detail-item">
            <span>{{ $t('所属公司：') }}</span>
            <span>{{ formObject.companyName }}</span>
          </div>
          <div class="detail-item">
            <span>{{ $t('供应商名称：') }}</span>
            <span>{{ formObject.supplierName }}</span>
          </div>
        </div>
        <div class="desc-detail-box">
          <!-- <div class="detail-item">
            <span>{{ $t('索赔品类：') }}</span>
            <span>{{ formObject.itemName }}</span>
          </div> -->
          <div class="detail-item">
            <span>{{ $t('协议书模板：') }}</span>
            <span>{{ formObject.agreementName }}</span>
          </div>
          <div class="detail-item">
            <span>{{ $t('创建人：') }}</span>
            <span>{{ formObject.createUserName }}</span>
          </div>
          <div class="detail-item">
            <span>{{ $t('创建时间：') }}</span>
            <span>{{ createTime }}</span>
          </div>
          <div class="detail-item link" @click="showDetail" v-if="!!formObject.approvalUrl">
            <span>{{ $t('查看审批记录') }}</span>
          </div>
        </div>
      </div>
      <div class="buttons-box">
        <span class="btn" @click="cancel">{{ $t('返回') }}</span>
        <!-- <span class="btn" v-if="queryId" @click="print">{{ $t('协议打印') }}</span> -->
        <!-- <span class="btn" v-if="queryType == 1 || queryType == 4 || queryType == 7" @click="save">{{
          $t('保存')
        }}</span>
        <span
          class="btn"
          v-if="queryType == 1 || queryType == 4 || queryType == 7"
          @click="saveAndCommit"
          >{{ $t('保存并提交') }}</span
        >
        <span
          class="btn"
          v-if="queryType == 1 || queryType == 4 || queryType == 7"
          @click="rejectOrder"
          >{{ $t('驳回') }}</span -->
      </div>
    </div>
    <claim-detail
      ref="claimDetail"
      v-if="this.formObject && this.formObject.id && isReload"
      :info="formObject"
    ></claim-detail>
  </div>
</template>
<script>
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
import claimDetail from './pages/pages/claimDetail.vue'
export default {
  components: {
    claimDetail
  },
  data() {
    return {
      statusList: [
        { code: 1, label: this.$t('新建') },
        { code: 2, label: this.$t('创建驳回') },
        { code: 3, label: this.$t('盖章中') },
        { code: 4, label: this.$t('已盖章') },
        { code: 5, label: this.$t('已废弃') },
        { code: 6, label: this.$t('待审批') },
        { code: 7, label: this.$t('审批拒绝') },
        { code: 8, label: this.$t('付款中') },
        { code: 9, label: this.$t('已完成') }
      ],
      formObject: {
        projectCode: null
      },
      isReload: true
    }
  },

  computed: {
    queryId() {
      return this.$route.query.id
    },
    queryType() {
      return this.$route.query.type
    },
    queryCode() {
      return this.$route.query.code ? this.$route.query.code : this.$route.query.form_code
    },
    createTime() {
      return this.formatTime(new Date(this.formObject.createTime))
    },
    statusLabel() {
      return this.statusList.find((e) => e.code == this.formObject.status)?.label
    }
  },
  mounted() {
    this.getDetail()
  },
  methods: {
    rejectOrder() {
      this.$API.deadMaterials
        .rejectHeader({
          id: this.queryId
        })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.getDetail()
          }
        })
    },
    showDetail() {
      window.open(this.formObject.approvalUrl)
    },
    formatTime(e) {
      if (e) {
        if (e == 0) {
          return (e = '')
        } else if (typeof e == 'object') {
          return utils.formateTime(e)
        } else if (typeof e == 'string') {
          if (e.indexOf('T') != -1) {
            return e.substr(0, 10)
          } else {
            let val = parseInt(e)
            return utils.formateTime(new Date(val))
          }
        } else if (typeof e == 'number') {
          return utils.formateTime(new Date(e))
        } else {
          return e
        }
      } else {
        return e
      }
    },
    async getDetail() {
      await this.$API.deadMaterials.getDetail(this.queryId).then((res) => {
        if (res.code === 200) {
          this.isReload = !this.isReload
          const _data = cloneDeep(res.data)
          this.formObject = { ..._data, ..._data.header }
          this.$nextTick(() => {
            this.isReload = !this.isReload
          })
          // this.$refs.claimDetail.$refs.assessIndexTemplateRef.refreshCurrentGridData()
        }
      })
    },
    cancel() {
      this.$router.push({
        name: 'purchase-assessmanage-assessFeedback'
      })
    },
    async save() {
      this.$refs.claimDetail.$refs.assessIndexTemplateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.endEdit()
      this.$refs.claimDetail.$refs.assessItemTemplateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.endEdit()
      let _standItemDetailList =
        this.$refs.claimDetail.$refs.assessItemTemplateRef.getCurrentUsefulRef().ejsRef.ej2Instances
          .currentViewData
      let _standDetailList =
        this.$refs.claimDetail.$refs.assessIndexTemplateRef.getCurrentUsefulRef().ejsRef
          .ej2Instances.currentViewData
      let _attachmentList =
        this.$refs.claimDetail.$refs.attachmentRef.getCurrentUsefulRef().ejsRef.ej2Instances
          .currentViewData
      _standDetailList.forEach((e) => {
        e.happenTime = Number(new Date(e.happenTime))
      })
      let _tempData = {
        // ...this.$refs.claimDetail.$refs.formInfo.model,
        needAccrual: this.$refs.claimDetail.$refs.formInfo.model.needAccrual,
        needSeal: this.$refs.claimDetail.$refs.formInfo.model.needSeal,
        // feedbackEndTime: Number(this.$refs.claimDetail.$refs.formInfo.model.feedbackEndTime),
        reason: this.$refs.claimDetail.$refs.editorRef.value,
        materialItemList: _standItemDetailList,
        // standDetailList: _standDetailList,
        attachmentList: _attachmentList,
        headerId: this.queryId
      }
      console.log('saveAssessDetail=', this, _tempData)
      await this.$API.deadMaterials.saveDetail(_tempData).then((res) => {
        if (res.code === 200) {
          this.getDetail()
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
        }
      })
      // }, 100)
    },
    saveAndCommit() {
      this.$refs.claimDetail.$refs.assessIndexTemplateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.endEdit()
      this.$refs.claimDetail.$refs.assessItemTemplateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.endEdit()
      setTimeout(() => {
        let _standItemDetailList =
          this.$refs.claimDetail.$refs.assessItemTemplateRef.getCurrentUsefulRef().ejsRef
            .ej2Instances.currentViewData
        let _standDetailList =
          this.$refs.claimDetail.$refs.assessIndexTemplateRef.getCurrentUsefulRef().ejsRef
            .ej2Instances.currentViewData
        let _attachmentList =
          this.$refs.claimDetail.$refs.attachmentRef.getCurrentUsefulRef().ejsRef.ej2Instances
            .currentViewData
        _standDetailList.forEach((e) => {
          e.happenTime = Number(new Date(e.happenTime))
        })
        let _tempData = {
          // ...this.$refs.claimDetail.$refs.formInfo.model,
          needAccrual: this.$refs.claimDetail.$refs.formInfo.model.needAccrual,
          needSeal: this.$refs.claimDetail.$refs.formInfo.model.needSeal,
          // feedbackEndTime: Number(this.$refs.claimDetail.$refs.formInfo.model.feedbackEndTime),
          reason: this.$refs.claimDetail.$refs.editorRef.value,
          materialItemList: _standItemDetailList,
          // standDetailList: _standDetailList,
          attachmentList: _attachmentList,
          headerId: this.queryId
        }
        console.log('saveAssessDetail=', this, _tempData)
        this.$API.deadMaterials.saveAndSubmit(_tempData).then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('保存并提交成功'),
              type: 'success'
            })
            this.cancel()
          }
        })
      }, 100)
    },
    async print() {
      let _id = this.queryId
      const formdata = new FormData()
      formdata.append('id', _id)
      let buffer = await this.$API.deadMaterials.purchaserPrintClaim(formdata).catch(() => {})
      buffer?.data && (buffer = buffer.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.detail-card {
  width: 100%;
  // height: 100px;
  height: auto;
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 20px 70px 20px 20px;
  display: flex;
  .logo {
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    background: #00469c;
    font-size: 40px;
    font-weight: bold;
    color: #fff;
    border-radius: 50%;
    margin-right: 22px;
  }
  .desc {
    flex: 1;
    // height: 57px;
    .desc-title-box {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      .title {
        font-size: 20px;
        font-weight: bold;
        color: #292929;
      }
      .tag {
        font-size: 12px;
        display: inline-block;
        padding: 4px;
        border-radius: 2px;
      }
      .status {
        color: #9a9a9a;
        background: rgba(154, 154, 154, 0.1);
        margin: 0 10px;
      }
      .group {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
    .desc-detail-box {
      display: flex;
      .detail-item {
        font-size: 12px;
        color: #9a9a9a;
        margin-right: 20px;
      }
      .link {
        color: #6386c1;
        cursor: pointer;
      }
    }
  }
  .buttons-box {
    display: flex;
    align-items: center;
    .btn {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      color: #00469c;
      margin-left: 25px;
      cursor: pointer;
    }
    .is-disabled {
      color: #ccc;
    }
  }
}
.full-height {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.operateButton {
  position: absolute;
  right: 20px;
  z-index: 1;
  top: 10px;
}
.mt-tabs {
  width: 100%;
  background-color: #fafafa;
  /deep/.mt-tabs-container {
    width: 100%;
    margin-right: 155px;
  }
}
.reason {
  max-width: 350px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mt-tooptip {
  display: inline-block;
}
.mb_7 {
  margin-bottom: 7px;
}
</style>
