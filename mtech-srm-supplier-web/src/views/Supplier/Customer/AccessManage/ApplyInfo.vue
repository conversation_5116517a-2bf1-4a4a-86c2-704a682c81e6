<template>
  <div class="apply-wrapper">
    <div class="module-wrapper">
      <div class="normal-title">{{ $t('状态') }}</div>
      <div>{{ $t('待审核') }}</div>
    </div>
    <div class="module-wrapper basic-info-wrapper">
      <div class="normal-title">{{ $t('基本信息') }}</div>
      <ul>
        <li>
          <span class="label">{{ $t('供应商类型：') }}</span>
          <span>{{ $t('供应商测试') }}</span>
        </li>
        <li>
          <span class="label">{{ $t('董事长：') }}</span>
          <span>PJ</span>
        </li>
        <li>
          <span class="label">{{ $t('总经理：') }}</span>
          <span>alice</span>
        </li>
        <li>
          <span class="label">{{ $t('公司网站：') }}</span>
          <span>http://jd.com</span>
        </li>
        <li>
          <span class="label">{{ $t('公司邮箱：') }}</span>
          <span><EMAIL></span>
        </li>
        <li>
          <span class="label">{{ $t('公司电话：') }}</span>
          <span>23523422</span>
        </li>
        <li>
          <span class="label">{{ $t('是否上市：') }}</span>
          <span>{{ $t('是') }}</span>
        </li>
        <li>
          <span class="label">{{ $t('股票代码：') }}</span>
          <span>22sdXSDGSG23</span>
        </li>
        <li>
          <span class="label">{{ $t('股权结构图：') }}</span>
          <span>{{ $t('暂无') }}</span>
        </li>
        <li>
          <span class="label">{{ $t('行业排名：') }}</span>
          <span>1</span>
        </li>
        <li>
          <span class="label">{{ $t('建筑物栋数：') }}</span>
          <span>12</span>
        </li>
        <li>
          <span class="label">{{ $t('建筑面积：') }}</span>
          <span>12853m2</span>
        </li>
        <li>
          <span class="label">{{ $t('占地面积：') }}</span>
          <span>12853m2</span>
        </li>
        <li>
          <span class="label">{{ $t('公司员工数：') }}</span>
          <span>232391</span>
        </li>
        <li>
          <span class="label">{{ $t('管理人员数：') }}</span>
          <span>12391</span>
        </li>
        <li>
          <span class="label">{{ $t('组织架构附件：') }}</span>
          <span>{{ $t('暂无') }}</span>
        </li>
        <li>
          <span class="label">{{ $t('主要竞争对手：') }}</span>
          <span>{{ $t('暂无') }}</span>
        </li>
        <li class="whole">
          <span class="label">{{ $t('环境管理措施简介：') }}</span>
          <span>{{ $t('暂无') }}</span>
        </li>
        <li class="whole">
          <span class="label">{{ $t('企业社会责任简介：') }}</span>
          <span>{{ $t('暂无') }}</span>
        </li>
        <li class="whole">
          <span class="label">{{ $t('备注：') }}</span>
          <span>{{ $t('暂无') }}</span>
        </li>
      </ul>
    </div>
    <div class="module-wrapper">
      <div class="normal-title">{{ $t('联系人信息') }}</div>
      <table cellpadding="10">
        <thead>
          <th>{{ $t('姓名') }}</th>
          <th>{{ $t('证件类型') }}</th>
          <th>{{ $t('证件号码') }}</th>
          <th>{{ $t('所属部门') }}</th>
          <th>{{ $t('职务') }}</th>
          <th>{{ $t('手机') }}</th>
        </thead>
        <tbody>
          <tr>
            <td>{{ $t('张三') }}</td>
            <td>{{ $t('身份证') }}</td>
            <td>133238388237274</td>
            <td>{{ $t('综合部') }}</td>
            <td>{{ $t('总监理助理') }}</td>
            <td>13123456543</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="module-wrapper">
      <div class="normal-title">{{ $t('分支机构') }}</div>
      <table cellpadding="10">
        <thead>
          <th>{{ $t('机构名称') }}</th>
          <th>{{ $t('分支机构地址') }}</th>
          <th>{{ $t('联系人') }}</th>
          <th>{{ $t('手机') }}</th>
        </thead>
        <tbody>
          <tr>
            <td>{{ $t('综合部') }}</td>
            <td>{{ $t('苏州金鸡湖') }}</td>
            <td>{{ $t('张三') }}</td>
            <td>13123456543</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ApplyInfo'
}
</script>

<style lang="scss" scoped>
.normal-title {
  width: 100%;
  height: 14px;
  line-height: 14px;
  font-size: 14px;
  color: #292929;

  font-weight: 500;
  margin-bottom: 15px;

  &:before {
    content: ' ';
    display: inline-block;
    vertical-align: baseline;
    width: 2px;
    height: 10px;
    background: rgba(0, 70, 156, 1);
    border-radius: 1px;
    margin-right: 10px;
  }
}
.apply-wrapper {
  padding: 20px;
  .module-wrapper {
    margin-bottom: 20px;
  }
  ul {
    display: flex;
    flex-wrap: wrap;
    color: #35404e;
    line-height: 28px;
    li {
      width: 33%;
      display: flex;
      &.whole {
        width: 100%;
      }
      .label {
        width: 130px;
        text-align: right;
        margin-right: 10px;
      }
    }
  }
  table {
    line-height: 28px;
  }
}
</style>
