<template>
  <!-- 顶部-公用数据过滤 -->
  <div v-if="companyTreeFields.dataSource.length" class="filter-panel">
    <mt-DropDownTree
      class="filter-item"
      :id="'org_' + new Date().getTime()"
      ref="ownerOrgNameRef"
      :placeholder="$t('请选择所属组织')"
      v-model="ownerOrg.ownerOrgId"
      :fields="companyTreeFields"
      :show-clear-button="false"
      @select="selectOwnerOrg"
    ></mt-DropDownTree>

    <mt-select
      ref="planRef"
      class="filter-item"
      v-model="planData.id"
      float-label-type="Never"
      :data-source="performancePlanList"
      :fields="{ text: 'planName', value: 'id' }"
      :placeholder="$t('请选择考评计划')"
      @change="changePlanId"
    ></mt-select>

    <mt-DropDownTree
      class="filter-item"
      :id="'org_' + new Date().getTime()"
      ref="categoryTree"
      :placeholder="$t('请选择最末级品类')"
      v-model="category.categoryCode"
      :fields="categoryTreeFields"
      :show-clear-button="false"
      @select="changeCategory"
    ></mt-DropDownTree>
    <span class="filter-item">{{ $t('参评供应商数：') }}{{ filterParams.supplierCount || 0 }}</span>
    <span class="filter-item"
      >{{ $t('评分问卷提交数：') }}{{ filterParams.instanceCount || 0 }}</span
    >
  </div>
</template>

<script>
import MixIn from '../config/mixin'

export default {
  mixins: [MixIn],
  props: {
    filterParams: {
      type: Object,
      default: () => {
        return {
          orgId: null,
          planId: null,
          supplierCount: 0,
          instanceCount: 0,
          selectSupplier: null
        }
      }
    },
    top10Data: {
      type: Array,
      default: () => {
        return []
      }
    },
    bottom10Data: {
      type: Array,
      default: () => {
        return []
      }
    },
    lineData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      ownerOrg: { ownerOrgId: null, ownerOrgName: null },
      planData: { planDataId: null, planName: '' },
      category: { categoryCode: '', categoryName: '' },
      companyTreeFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      categoryTreeFields: {
        dataSource: [],
        text: 'categoryName',
        value: 'categoryCode',
        child: 'childrens'
      },
      performancePlanList: [], //评分计划列表
      companyTreeList: [], //当前组织列表--Tree
      flatCompanyTreeList: [], //当前组织列表--Array
      categoryList: [] //品类数据
    }
  },
  mounted() {
    this.getCompanyTree()
    // this.getPlanList();
    // this.getCategoryList()
  },
  methods: {
    getCompanyTree() {
      this.$API.analysisOfSetting['TreeByAccountInSit']({
        orgLevelCode: 'ORG02'
      }).then((res) => {
        console.log(this.$t('组织树'), res)
        let _data = []
        if (res && res.data && Array.isArray(res.data)) {
          _data = res.data
        }
        this.companyTreeList = _data
        this.flatCompanyTreeList = this.flatTreeData(_data)
        this.companyTreeFields.dataSource = []
        this.$nextTick(() => {
          this.$set(this.companyTreeFields, 'dataSource', _data)
          console.log(
            '当前组织树-数据',
            this.companyTreeFields,
            this.companyTreeList,
            this.flatCompanyTreeList
          )
        })
      })
    },
    // 获取计划列表
    addListQuery() {
      this.$API.policySetting['addListQuery']({
        companyId: this.ownerOrg.ownerOrgId[0] // 传值级联公司id
      }).then((result) => {
        console.log(result)
        // this.$hloading();
        if (result.code === 200) {
          this.performancePlanList = result.data
        } else {
          this.performancePlanList = []
        }
      })
    },
    // 获取品类数据列表
    getCategoryList() {
      this.$API.planCategoryList['getCategoryList']({
        planId: this.planData.id //传值计划列表id
      }).then((result) => {
        let _data = []

        if (result && result.data && Array.isArray(result.data)) {
          _data = result.data
        }
        this.categoryList = _data
        this.$delete(this.categoryTreeFields, 'dataSource')
        this.$nextTick(() => {
          this.$set(this.categoryTreeFields, 'dataSource', _data)
        })
      })
    },
    //选取组织数据
    selectOwnerOrg(e) {
      if (e.action !== 'select') return
      if (e && e.itemData) {
        this.planData.id = ''

        this.ownerOrg = {
          ownerOrgId: [e.itemData.id],
          ownerOrgName: e.itemData.text
        }
        this.$set(this.ownerOrg, 'ownerOrgId', [e.itemData.id])
        this.$set(this.ownerOrg, 'ownerOrgName', e.itemData.text)
      } else {
        this.ownerOrg = {
          ownerOrgId: null,
          ownerOrgName: null
        }
      }

      this.addListQuery()
      // 清除原先的品类项数据
      this.$delete(this.categoryTreeFields, 'dataSource')
      this.$nextTick(() => {
        this.$set(this.categoryTreeFields, 'dataSource', [])
      })
      this.$emit('changeFilterData', {
        org: {}
      })
      // 清除原先的参评供应商数、评分问卷提交数、已选中的供应商数据
      this.filterParams.supplierCount = 0
      this.filterParams.instanceCount = 0
      this.filterParams.selectSupplier = null
    },
    //切换评分计划
    changePlanId(e) {
      this.planData.id = e.itemData?.id
      console.log(this.planData)
      if (this.planData.id) {
        this.getCategoryList()
      }
      this.$emit('changeFilterData', {
        org: {},
        plan: {}
      })
      this.filterParams.supplierCount = 0
      this.filterParams.instanceCount = 0
      this.filterParams.selectSupplier = null
    },

    // 切换品类
    changeCategory(e) {
      console.log('当前选择的节点e', e)
      let { itemData } = e
      this.getLeafNode(this.categoryTreeFields.dataSource, itemData.id)
      this.$emit('changeFilterData', {
        org: this.ownerOrg,
        plan: this.planData,
        category: e.itemData
      })
    },
    // 判断是否是最内层节点，只能选中最内层的节点
    getLeafNode(data, str) {
      data.forEach((item) => {
        if (str == item.categoryCode) {
          if (item.leafNode !== 1) {
            this.$toast({ content: this.$t('请选择最末级'), type: 'warning' })
            this.$refs['categoryTree'].$refs.ejsRef.clear()
          }
        }
        if (item.childrens.length > 0) {
          this.getLeafNode(item.childrens, str)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-panel {
  flex-shrink: 0;
  background: #fff;
  padding: 10px;
  display: flex;
  align-items: center;
  .filter-item {
    width: 250px;
    &:not(:first-child) {
      margin-left: 10px;
    }
  }
}
</style>
