<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="indexName" :label="$t('指标名称：')" class="full-width">
          <mt-input
            v-model="formObject.indexName"
            float-label-type="Never"
            :placeholder="$t('请输入指标名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="dimensionId" :label="$t('所属维度：')">
          <mt-select
            ref="dimensionRef"
            v-model="formObject.dimensionId"
            float-label-type="Never"
            :data-source="dimensionList"
            :fields="{ text: 'dimensionName', value: 'dimensionId' }"
            :placeholder="$t('所属维度')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="fullScore" :label="$t('满分：')">
          <mt-input-number
            ref="fullScoreRef"
            :min="1"
            v-model="formObject.fullScore"
            class="number-item"
            @change="changeFullScore"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item :label="$t('评分标准简介：')" class="full-width">
          <mt-input
            :multiline="true"
            :rows="3"
            type="text"
            maxlength="200"
            float-label-type="Never"
            v-model="formObject.description"
            :placeholder="$t('请简要说明本指标评分标准')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="downOrgId"
          :label="$t('向下派发：')"
          class="full-width"
          v-if="dropFields.dataSource.length"
        >
          <mt-DropDownTree
            v-if="dropFields.dataSource.length"
            ref="downOrgRef"
            :placeholder="$t('请选择')"
            :fields="dropFields"
            :show-check-box="true"
            :allow-multi-selection="true"
            :auto-check="true"
            v-model="formObject.downOrgId"
          ></mt-DropDownTree>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        id: null, //配置Id
        description: null, //评分标准简介
        dimensionId: null, //所属维度
        downOrgId: null, //下游组织id（多个组织逗号分隔）
        downOrgName: null, //下游组织名称（多个组织逗号分隔）
        fullScore: 1, //	满分
        indexName: null, //	指标名称
        ownerOrgId: null, //	所属组织id
        ownerOrgName: null //所属组织名称
      },
      formRules: {
        indexName: [{ required: true, message: this.$t('请输入指标名称'), trigger: 'blur' }],
        dimensionId: [{ required: true, message: this.$t('请选择所属维度'), trigger: 'blur' }],
        description: [{ required: true, message: this.$t('请输入评分标准简介'), trigger: 'blur' }],
        fullScore: [{ required: true, message: this.$t("请输入'满分'"), trigger: 'blur' }],
        ownerOrgId: [{ required: true, message: this.$t('请选择所属组织'), trigger: 'blur' }]
      },
      dimensionList: [],
      flatDownCompanyTreeList: [], //当前组织--派发组织列表--Array
      dropFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      let _data = { ...this.modalData.data }
      this.getFormDetail(_data.id)
    } else if (this.modalData && this.modalData.ownerOrg) {
      let { ownerOrgId, ownerOrgName } = this.modalData.ownerOrg
      this.formObject.ownerOrgId = ownerOrgId
      this.formObject.ownerOrgName = ownerOrgName
      console.log(this.$t('新增模板'), this.formObject)
      this.serializeDownTreeList()
    }
    this.getDimensionSelectList()
  },
  methods: {
    changeFullScore(e) {
      this.formObject.fullScore = e
    },
    serializeDownTreeList() {
      let _data = this.modalData.flatCompanyTreeList.find(
        (e) => e.id === this.formObject.ownerOrgId[0]
      )
      let downList = [],
        flatDownList = []
      if (_data && _data.children) {
        downList = _data.children
        flatDownList = this.modalData.flatTreeData(_data.children)
      }
      this.dropFields.dataSource = []
      this.$nextTick(() => {
        this.$set(this.dropFields, 'dataSource', downList)
        this.flatDownCompanyTreeList = flatDownList
        console.log('组织树-数据', this.dropFields, this.flatDownCompanyTreeList)
      })
    },
    getFormDetail(id) {
      //维度详情
      this.$API.performanceScoreSetting.indexDetail({ id }).then((res) => {
        console.log(this.$t('指标详情'), res)
        let _data = res.data
        _data.ownerOrgId = [_data.ownerOrgId]
        _data.downOrgId = _data.downOrgId.split(',')
        this.formObject = {
          id: _data.id, //配置Id
          description: _data.description, //评分标准简介
          dimensionId: _data.dimensionId, //所属维度
          downOrgId: _data.downOrgId, //下游组织id（多个组织逗号分隔）
          downOrgName: _data.downOrgName, //下游组织名称（多个组织逗号分隔）
          fullScore: _data.fullScore, //	满分
          indexName: _data.indexName, //	指标名称
          ownerOrgId: _data.ownerOrgId, //	所属组织id
          ownerOrgName: _data.ownerOrgName //所属组织名称
        }
        this.serializeDownTreeList()
        console.log('编辑数据--初始化', this.formObject)
      })
    },
    getDimensionSelectList() {
      this.$API.performanceScoreSetting.dimensionSelectList().then((res) => {
        if (res && res.data && Array.isArray(res.data)) {
          this.dimensionList = res.data
        } else {
          this.dimensionList = []
        }
      })
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.formObject))
          // if (params.dimensionId) {
          //   //根据当前选择的dimensionId，获取dimension对象
          //   let _select = this.$refs.dimensionRef.ejsRef.getDataByValue(
          //     params.dimensionId
          //   );
          //   params.dimensionName = _select.dimensionName;
          //   console.log("current dimension", _select);
          // }
          if (params.downOrgId) {
            //根据当前选择的downOrgId数组，获取downOrgName数组
            if (Array.isArray(params.downOrgId) && params.downOrgId.length) {
              let _ids = [...params.downOrgId],
                _names = []
              _ids.forEach((e) => {
                let _find = this.flatDownCompanyTreeList.find((f) => e === f.id)
                _names.push(_find.name)
              })
              params.downOrgId = _ids.join(',')
              params.downOrgName = _names.join(',')
            }
          }
          if (this.editStatus) {
            console.log('指标定义--编辑', params)
            this.$API.performanceScoreSetting.editIndex(params).then((res) => {
              if (res.code == 200) {
                this.$emit('confirm-function')
              }
            })
          } else {
            delete params.id
            console.log('指标定义--新增', params)
            this.$API.performanceScoreSetting.addIndex(params).then((res) => {
              if (res.code == 200) {
                this.$emit('confirm-function')
              }
            })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .full-width {
    width: 100% !important;
  }
}
</style>
