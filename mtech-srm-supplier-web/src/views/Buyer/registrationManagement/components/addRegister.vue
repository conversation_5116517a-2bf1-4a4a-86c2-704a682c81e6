<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ header }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>
      <div class="slider-content">
        <mt-form ref="ruleForm" class="ruleForm" :model="ruleForm" :rules="rules">
          <mt-row v-if="type != 'view'">
            <mt-col :span="12">
              <mt-form-item prop="companyId" class="form-item" :label="$t('选择客户')">
                <mt-multi-select
                  :disabled="isDis || inputState"
                  v-model="ruleForm.companyId"
                  :data-source="companyArrList"
                  :show-clear-button="true"
                  :allow-filtering="true"
                  :filtering="filteringCompany"
                  :placeholder="$t('请选择客户')"
                  :header-template="hTemplateStr"
                  :fields="{ text: 'orgName', value: 'id' }"
                  @select="selectCompany"
                  @change="changeCompany"
                ></mt-multi-select>
              </mt-form-item>
            </mt-col>
            <mt-col :span="12">
              <mt-form-item prop="supplierType" class="form-item" :label="$t('供应商类型')">
                <mt-select
                  :disabled="isDis || inputState"
                  v-model="ruleForm.supplierType"
                  :data-source="supplierTypeList"
                  @select="selectSupplier"
                  @change="changeSupplier"
                  :placeholder="$t('请选择供应商类型')"
                  :fields="{ text: 'name', value: 'itemCode' }"
                ></mt-select>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <div v-if="type == 'view'">
            <mt-row style="margin-bottom: 20px">
              <mt-col :span="12">
                <span>{{ $t('客户：') }}</span>
                <span>{{ newListInfo.orgName }}</span>
              </mt-col>
              <mt-col :span="12">
                <span>{{ $t('供应范围：') }}</span>
                <span>{{ newListInfo.supplierType }}</span>
              </mt-col>
            </mt-row>
            <mt-row style="margin-bottom: 20px">
              <mt-col :span="12">
                <span>{{ $t('供方联系人：') }}</span>
                <span>{{ newListInfo.username }}</span>
              </mt-col>
              <mt-col :span="12">
                <span>{{ $t('供方联系电话：') }}</span>
                <span>{{ newListInfo.mobile }}</span>
              </mt-col>
            </mt-row>
            <mt-row style="margin-bottom: 20px">
              <mt-col :span="12">
                <span>{{ $t('联系邮箱：') }}</span>
                <span>{{ newListInfo.email }}</span>
              </mt-col>
            </mt-row>
          </div>
          <div class="dialog-content-h4 fbox" v-show="!!ruleForm.companyId && isShow">
            <div class="flex1" v-show="type == 'edit' || type == 'archivEdit' || type == 'add'">
              <h4>{{ $t('待选项') }}</h4>
              <div class="input-icon-merge">
                <div class="checkbox-block">
                  <!-- v-model="checkboxValAll" -->
                  <!-- :checked="checkboxChecked" -->
                  <mt-checkbox
                    :label="$t('全选')"
                    :indeterminate="checkboxIndeterminate"
                    :checked="checkboxChecked"
                    :disabled="categoryTreeAllDisabled"
                    @change="handleAllChange"
                  ></mt-checkbox>
                </div>
                <input
                  v-model="inputSearchModel"
                  class="e-input"
                  type="text"
                  :placeholder="$t('请输入品类/品类编码进行搜索')"
                />
                <mt-button class="btn-merge" type="text" plain @click="inputSearchBtn">{{
                  $t('搜索')
                }}</mt-button>
                <mt-button class="btn-merge" type="text" plain @click="inputResetBtn">{{
                  $t('重置')
                }}</mt-button>
              </div>
              <div class="all-list-box">
                <div class="category-tree-box flex1">
                  <template v-if="categoryTree.dataSource.length > 0">
                    <div
                      class="category-content"
                      v-for="(item, index) in categoryTree.dataSource"
                      :key="item.id"
                    >
                      <mt-checkbox
                        :checked="item.selected"
                        :disabled="item.pldisabled"
                        @change="selectData($event, item, index)"
                      ></mt-checkbox>
                      <!-- <mt-tooltip :content="`${item.categoryName }：${item.categoryCode}`" target="#box">
                        <div id="container">
                      <div id="box" style="display: inline-block">-->
                      <div class="plname">{{ item.categoryCode }}：{{ item.categoryName }}</div>
                      <!-- </div>
                        </div>
                      </mt-tooltip> -->
                      <mt-select
                        v-model="item.categoryType"
                        :width="75"
                        :height="30"
                        :data-source="categoryDataArr"
                        :show-clear-button="false"
                        @change="changeCategory($event, item)"
                        :placeholder="$t('请选择')"
                      ></mt-select>
                    </div>
                  </template>
                  <p v-if="categoryTree.dataSource.length === 0" class="category-no-data">
                    {{ $t('暂无数据') }}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="dialog-content-h4 fbox" v-if="type == 'view'">
            <div>
              <h4>{{ $t('供应范围') }}</h4>
              <mt-DataGrid
                :data-source="newDataScource"
                :column-data="sampleColumns"
                ref="dataGrid"
              ></mt-DataGrid>
            </div>
          </div>
          <div class="dialog-content-h4 fbox" v-if="type != 'view'">
            <div>
              <h4>{{ $t('已选列表') }}</h4>
              <mt-DataGrid
                :data-source="addDataSource"
                :column-data="addColumnData"
                ref="dataGrid"
              ></mt-DataGrid>
            </div>
          </div>
        </mt-form>
      </div>
      <div class="slider-footer mt-flex" v-show="!inputState">
        <span @click="confirm">{{ $t('保存') }}</span>
        <span @click="saveCommit">{{ $t('保存并提交') }}</span>
        <span @click="cancel">{{ $t('取消') }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
import utils from '@/utils/utils'
import { addColumnData } from '../config/column'
import { i18n } from '@/main'

const sampleColumns = [
  {
    field: 'categoryName',
    headerText: i18n.t('品类'),
    textAlign: 'Center'
  },
  {
    field: 'categoryTypeName',
    headerText: i18n.t('供应类型'),
    textAlign: 'Center'
  }
]
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      newDataScource: [],
      newListInfo: {
        orgName: '',
        supplierType: '',
        mobile: '',
        username: '',
        email: ''
      },
      sampleColumns: sampleColumns,
      addDataSource: [],
      addColumnData,
      isShow: false,
      selectTreeKey: 1,
      nowSelectedFactory: {}, //目前正操作的工厂
      factoryDataSelected: [], //选中的工厂集合
      isDis: false,
      editId: '',
      sltTreeData: [],
      categoryData: [], //品类选中集合
      supplierTypeList: [],
      dataArr: [],
      companyArrList: [],
      companyArrListCopy: [],
      companyName: '',
      tenantId: 0,
      inputState: false,
      sltCompanyList: [],
      factoryArray: [],
      categoryTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      searchCategoryArr: [],
      selectTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      // 已勾选的ID

      checkedNodes: [],
      checkedNodesObj: [],

      parentNode: [],

      currentFactory: {},
      factoryId: -1,
      // 已选的公司对象 单选
      currentCompany: {
        id: ''
      },
      showCategoryTree: false, // treeview数据不更新。。
      showSelectTree: false, // treeview数据不更新。。
      ruleFormList: [], //选择后的数据
      ruleForm: {
        companyId: [],
        supplierType: ''
      },
      rules: {
        companyId: [{ required: true, message: this.$t('请选择客户'), trigger: 'blur' }],
        supplierType: [{ required: true, message: this.$t('请选择供应商类型'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
      ],
      supplierCode: '',
      categoryTypeCode: '',
      categoryTypeName: '',
      userInfo: {}, //得到供应商用户信息
      inputSearchModel: '', //模糊搜索品类==监听
      editDialogBoxGridData: null,
      // checkboxInfo:{
      //复选框信息
      checkboxValAll: false,
      checkboxIndeterminate: false,
      checkboxChecked: false,
      categoryTreeAllDisabled: false,
      // },

      // 供方：注册管理功能：公司需支持多选和全选
      hTemplateStr: function () {
        return {
          template: Vue.component('headerTemplate', {
            template: `<div class="head">
              <mt-button cssClass="e-flat" :isPrimary="true" @click="checkAll">{{ $t('全选') }}</mt-button>
              <mt-button cssClass="e-flat" :isPrimary="true" @click="clearAll">{{ $t('清除') }}</mt-button>
            </div>`,
            data() {
              return {
                data: {}
              }
            },
            methods: {
              checkAll() {
                this.$parent.$parent.ejsRef.selectAll(true)
              },
              clearAll() {
                this.$parent.$parent.ejsRef.selectAll(false)
              }
            }
          })
        }
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    type() {
      return this.modalData.type
    },
    record() {
      return this.modalData.record
    },
    categoryDataArr() {
      let arr = [
        { text: this.$t('原厂'), value: 1 },
        { text: this.$t('代理'), value: 2 }
      ]
      if (this.ruleForm.supplierType == 'commonPurchaseSupplier') {
        arr.push({ text: this.$t('现货商'), value: 3 })
      }
      return arr
    }
  },
  async created() {
    // 查询供应商类型
    await this.getUserInfoInterface()
  },
  async mounted() {
    await this.getSupplierType()
    this.tenantId = await this.purTenantIdQuery()
    this.getUserDetail()
    this.filteringCompany = utils.debounce(this.filteringCompany, 500)
    if (this.type == 'add') {
      this.editId = ''
      this.inputState = false
      this.isDis = false
      return
    }
    this.isShow = true
    //编辑赋值
    if (this.type == 'archivEdit') {
      this.editId = this.record[0].partnerArchiveId
    } else if (this.type == 'edit' || this.type == 'view') {
      this.editId = this.record[0].id
    }
    // 查询详情
    await this.findById(this.editId)
    if (this.type == 'edit' || this.type == 'archivEdit') {
      this.inputState = false
      this.isDis = true
    } else if (this.type == 'view') {
      this.inputState = true
    }
  },
  methods: {
    async getUserInfoInterface() {
      this.userInfo = {}
      if (sessionStorage.getItem('userInfo')) {
        this.userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      } else {
        await this.$API.masterData.getUserInfo().then((res) => {
          if (res.code == 200) {
            this.userInfo = res.data
          }
        })
      }
    },
    async selectSupplier(v) {
      this.categoryCode = []
      this.categoryName = []
      this.categoryData = []
      if (v.itemData) {
        let { itemData } = v
        this.supplierCode = itemData.itemCode
      } else {
        this.supplierCode = v.supplierType
      }
    },
    async changeSupplier() {
      if (this.header == this.$t('新增')) {
        this.addDataSource &&
          this.addDataSource.forEach((i) => {
            i.categoryName = ''
          })
        this.addDataSource = JSON.parse(JSON.stringify(this.addDataSource))
        if (this.supplierCode == 'logisticsProvider') {
          // 不需要选择工厂和品类
          this.isShow = false
          this.categoryTypeCode = ''
          this.categoryTypeName = ''
        } else if (this.supplierCode == 'commonPurchaseSupplier') {
          this.categoryTypeCode = 'product'
          this.categoryTypeName = this.$t('通采')
          this.isShow = true
        } else if (this.supplierCode == 'noBiddingPurchaseSupplier') {
          this.categoryTypeCode = 'common'
          this.categoryTypeName = this.$t('非采')
          this.isShow = true
        }
        // // 根据公司获取品类树 接口还没有 暂时用账户获取品类数 getProdcutTree
        this.categoryTree.dataSource = await this.getCateGoryTree()
        this.categoryTree.dataSource = JSON.parse(JSON.stringify(this.categoryTree.dataSource))
        this.categoryTree.dataSource = JSON.parse(JSON.stringify(this.categoryTree.dataSource))
      }
    },
    // 供应商类型==接口
    getSupplierType() {
      this.$API.supplierRegister['queryDict']({
        dictCode: 'supplierType'
      })
        .then((res) => {
          this.supplierTypeList = res.data
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    async findById(id) {
      let _this = this
      this.$loading()
      //当前行数据编辑/详情==接口
      await this.$API.supplierRegister
        .supplierInviteDetail(id)
        .then(async (res) => {
          _this.$hloading()
          _this.ruleForm.supplierType = res.data.supplierType
          _this.editDialogBoxGridData = res.data
          _this.categoryTree.dataSource = await _this.getCateGoryTree() //接口

          if (_this.editDialogBoxGridData.extList) {
            _this.editDialogBoxGridData.extList.forEach((iC) => {
              _this.categoryTree.dataSource.forEach((i) => {
                if (iC.customerCategoryName == i.categoryName) {
                  i.categoryType = Number(iC.categoryType)
                }
              })
            })
          }
          // 公司值显示，工厂，品类回显
          _this.ruleForm.companyId = [res.data.extList[0].customerOrgId]
          // 编辑：勾选数据
          _this.getDataTreeStyle(res.data)
          // await _this.selectCompany(res.data);
          await _this.selectSupplier(res.data)
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })

      let obj = []
      this.editDialogBoxGridData.extList.map((eitem) => {
        obj.push({
          id: eitem.customerCategoryId,
          name:
            eitem.customerCategoryName == ''
              ? ''
              : `${eitem.customerCategoryName}(${
                  eitem.categoryType == 1
                    ? this.$t('原厂')
                    : eitem.categoryType == 2
                    ? this.$t('代理')
                    : ''
                })`,
          categoryId: eitem.customerCategoryId,
          categoryCode: eitem.customerCategoryCode,
          categoryName: eitem.customerCategoryName,
          categoryType: eitem.categoryType,
          categoryTypeName:
            eitem.categoryType == 1
              ? this.$t('原厂')
              : eitem.categoryType == 2
              ? this.$t('代理')
              : '',
          factoryId: eitem.customerFactoryId,
          orgCode: eitem.customerOrgCode,
          orgid: eitem.customerOrgId,
          orgName: eitem.customerOrgName
        })
      })
      this.newListInfo.mobile = this.userInfo.mobile
      this.newListInfo.username = this.userInfo.username
      this.newListInfo.email = this.userInfo.mail
      this.newListInfo.orgName = obj[0].orgName

      this.$forceUpdate()
      setTimeout(() => {
        let supplierTypeNameList = this.supplierTypeList.filter((item) => {
          return item.itemCode == this.ruleForm.supplierType
        })
        this.newListInfo.supplierType = supplierTypeNameList[0].itemName
      }, 10)

      if (obj[0].categoryCode == '') {
        this.newDataScource = []
      } else {
        this.newDataScource = obj
      }

      this.categoryData = obj
      this.categoryCode = []
      this.categoryName = []
      this.categoryData.forEach((ic) => {
        this.categoryCode.push(ic.categoryCode)
        this.categoryName.push(ic.name)
      })
      this.addDataSource &&
        this.addDataSource.forEach((i) => {
          i['categoryCode'] = this.categoryCode.join('，')
          i['categoryName'] = this.categoryName.join('，')
          i['categoryData'] = this.categoryData
          i['mobile'] = this.userInfo.mobile
          i['username'] = this.userInfo.username
        })
      this.addDataSource = JSON.parse(JSON.stringify(this.addDataSource))
    },

    // 品类勾选已选中数据，已选显示所有树结构
    async getDataTreeStyle(data) {
      let checkP = []
      this.categoryTree.dataSource.forEach((i) => {
        data.extList.forEach((iC) => {
          if (iC.customerCategoryId == i.id) {
            i.selected = true
          }
        })
        checkP.push(i.selected)
      })
      let ck = Array.from(new Set(checkP)) //去重
      if (ck.length === 2) {
        //全选框==部分
        this.checkboxChecked = false
        this.checkboxIndeterminate = true
      } else if (ck.length === 1 && ck[0] == true) {
        //全选框==全部
        this.checkboxChecked = true
        this.checkboxIndeterminate = false
      } else if (ck.length === 1 && ck[0] == false) {
        //全选框=取消
        this.checkboxChecked = false
        this.checkboxIndeterminate = false
      }
      this.$set(this.selectTree, 'dataSource', this.categoryTree.dataSource)
    },
    cancel() {
      this.$emit('cancel-function')
    },
    purTenantIdQuery() {
      this.$loading()
      return this.$API.supplierRegister
        .purTenantIdQuery()
        .then((res) => {
          this.$hloading()
          return res.data
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 用户输入搜索
    async filteringCompany(e) {
      this.companyName = e.text
      await this.getUserDetail()
      e.updateData(this.companyArrListCopy)
    },
    // 以下是供应范围的接口
    // 获取当前用户信息
    async getUserDetail() {
      this.$loading()
      let obj = {
        fuzzyName: this.companyName,
        tenantId: this.tenantId
      }
      await this.$API.supplierRegister.findOrgsByFuzzyOrgName(obj).then((res) => {
        if (res.code === 200) {
          this.companyArrListCopy = res.data
          this.companyArrListCopy.forEach((item) => {
            let bol = this.companyArrList.some((e) => {
              return item.id == e.id
            })
            item['orgName'] = item['orgCode'] + '-' + item['orgName']
            if (!bol) {
              this.companyArrList.push(item)
            }
          })
          this.$forceUpdate()
          this.$hloading()
        }
      })
      // .catch((err) => {
      //   this.$hloading();
      //   this.$toast({
      //     content: err.msg,
      //     type: "error",
      //   });
      // });
    },
    // 更新公司
    async changeCompany(e) {
      let _this = this
      this.addDataSource = []
      let ruleFormNewList = this.companyArrList.filter((i) => {
        return e.value.includes(i.id)
      })
      ruleFormNewList.forEach((i) => {
        //表格数据赋值
        this.addDataSource.push({
          orgCode: i.orgCode,
          orgName: i.orgName,
          orgid: i.id,
          mobile: _this.userInfo.mobile,
          username: _this.userInfo.username,
          categoryName: _this.categoryName ? _this.categoryName.join('，') : '',
          categoryCode: _this.categoryName ? _this.categoryCode.join('，') : ''
        })
      })

      // 查询品类：可选品类值集根据供应商+公司排除已准入的品类
      this.searchCategory()
    },
    // 选择公司
    selectCompany(e) {
      this.userInfo.customerTenantId = e.itemData.tenantId //客户id
    },
    // 查询品类：可选品类值集根据供应商+公司排除已准入的品类
    // async searchCategory() {
    //   this.$loading()
    //   let obj = {
    //     orgIds: this.ruleForm.companyId
    //   }
    //   await this.$API.supplierRegister
    //     .searchCategory(obj)
    //     .then((res) => {
    //       if (res.code === 200) {
    //         this.$set(this, 'searchCategoryArr', res.data)
    //         this.$hloading()
    //       }
    //     })
    //     .catch((err) => {
    //       this.$hloading()
    //       this.$toast({
    //         content: err.msg,
    //         type: 'error'
    //       })
    //     })
    // },
    // 查询品类：可选品类值集根据供应商+公司排除已准入的品类
    async searchCategory() {
      this.$loading()
      let obj = {
        orgIds: this.ruleForm.companyId
      }
      await this.$API.supplierRegister
        .searchCategory(obj)
        .then((res) => {
          if (res.code === 200) {
            this.$set(this, 'searchCategoryArr', res.data)
            this.$hloading()
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    /**
     * 获取品类树 (假接口 传参未传)
     * @factoryObject 工厂信息
     * @isInit 是否是第一个 要展开
     */
    getCateGoryTree1() {
      if (!this.supplierCode) {
        //品类接口需要
        if (this.ruleForm.supplierType == 'logisticsProvider') {
          // 不需要选择工厂和品类
          // this.isShow = false;
          this.categoryTypeCode = ''
          this.categoryTypeName = ''
        } else if (this.ruleForm.supplierType == 'commonPurchaseSupplier') {
          this.categoryTypeCode = 'product'
          this.categoryTypeName = this.$t('通采')
          // this.isShow = true;
        } else if (this.ruleForm.supplierType == 'noBiddingPurchaseSupplier') {
          this.categoryTypeCode = 'common'
          this.categoryTypeName = this.$t('非采')
          // this.isShow = true;
        }
      }
      this.supplierCode = ''
      this.$loading()
      return this.$API.supplierRegister.categoryQuery({
        condition: 'and',
        tenantId: this.tenantId,
        page: {
          current: 1,
          size: 200
        },
        rules: [
          {
            field: 'statusId',
            type: 'int',
            operator: 'equal',
            value: 1
          },
          {
            field: 'categoryTypeCode',
            type: 'string',
            operator: 'equal',
            value: this.categoryTypeCode
          },
          {
            field: 'tree_level',
            type: 'int',
            operator: 'equal',
            value: 1
          },
          {
            condition: 'and',
            rules: [
              {
                field: 'categoryCode',
                type: 'string',
                operator: 'contains',
                value: this.inputSearchModel
              },
              {
                condition: 'or',
                field: 'categoryName',
                type: 'string',
                operator: 'contains',
                value: this.inputSearchModel
              }
            ]
          }
        ],
        defaultRules: []
      })
    },

    async getCateGoryTree() {
      let res = await this.getCateGoryTree1()
      this.$hloading()
      if (res.code == 200 && !utils.isEmpty(res?.data?.records)) {
        res.data.records.forEach((item) => {
          // item.categoryType = 1
          item.selected = false
          item.disabled = true //下拉框的禁用
          // item.pldisabled = true; //选择框的禁用：初始化因为没有选择工厂 品类禁止勾选
          if (
            this.searchCategoryArr.findIndex((cate) => cate.categoryCode === item.categoryCode) !=
            -1
          ) {
            item.pldisabled = true //选择框的禁用
          } else {
            item.pldisabled = false //选择框的禁用
          }
        })
        this.categoryTreeAllDisabled =
          res.data.records.filter((item) => item.pldisabled === true).length ===
          res.data.records.length
        return res.data.records
      } else {
        return []
      }
    },

    selectData(state, item) {
      // 品类
      if (state.checked) {
        item.selected = true
        // 选中时 下拉选项 禁用，反则不禁用
        // item.disabled = true;
        // 添加数据
        let str =
          item.categoryType == 1 ? this.$t('原厂') : item.categoryType == 2 ? this.$t('代理') : ''
        let obj = {
          id: item.id,
          name: item.categoryName + `(${str})`,
          categoryId: item.id,
          categoryCode: item.categoryCode,
          categoryName: item.categoryName,
          categoryType: item.categoryType
        }
        // this.categoryData.push(obj);
        if (this.categoryData.length == 0) {
          this.categoryData.push(obj)
        } else {
          let num = 0
          this.categoryData.forEach((item) => {
            if (item.id == obj.id) {
              num++
            }
          })
          if (num == 0) {
            this.categoryData.push(obj)
          }
        }
      } else {
        item.selected = false
        item.disabled = false
        this.categoryData.forEach((cm, cn) => {
          if (cm.id == item.id) {
            this.categoryData.splice(cn, 1)
          }
        })
      }
      this.categoryCode = []
      this.categoryName = []
      this.categoryData.forEach((ic) => {
        this.categoryCode.push(ic.categoryCode)
        this.categoryName.push(ic.name)
      })

      let checkP = []
      this.categoryTree.dataSource.forEach((i) => {
        checkP.push(i.selected)
      })
      let ck = Array.from(new Set(checkP)) //去重
      if (ck.length === 2) {
        //全选框==部分
        this.checkboxChecked = false
        this.checkboxIndeterminate = true
      } else if (ck.length === 1 && ck[0] == true) {
        //全选框==全部
        this.checkboxChecked = true
        this.checkboxIndeterminate = false
      } else if (ck.length === 1 && ck[0] == false) {
        //全选框=取消
        this.checkboxChecked = false
        this.checkboxIndeterminate = false
      }

      this.addDataSource &&
        this.addDataSource.forEach((i) => {
          i['categoryCode'] = this.categoryCode.join('，')
          i['categoryName'] = this.categoryName.join('，')
          i['categoryData'] = this.categoryData
          i['mobile'] = this.userInfo.mobile
          i['username'] = this.userInfo.username
        })
      this.addDataSource = JSON.parse(JSON.stringify(this.addDataSource))
    },

    // 选择后，值改变 已选项也要改变
    changeCategory(e, item) {
      item.categoryType = e.value
      let str = e.value == 1 ? this.$t('原厂') : e.value == 2 ? this.$t('代理') : ''

      this.categoryData.map((i) => {
        if (i.id == item.id) {
          ;(i.categoryType = item.categoryType), (i.name = item.categoryName + `(${str})`)
        }
      })

      this.factoryDataSelected.forEach((fitem) => {
        if (this.nowSelectedFactory.id == fitem.id) {
          fitem.children = []
          fitem.children = this.categoryData
        }
      })
      this.categoryCode = []
      this.categoryName = []
      this.categoryData.forEach((ic) => {
        this.categoryCode.push(ic.categoryCode)
        this.categoryName.push(ic.name)
      })
      this.addDataSource &&
        this.addDataSource.forEach((i) => {
          i['categoryCode'] = this.categoryCode.join('，')
          i['categoryName'] = this.categoryName.join('，')
          i['categoryData'] = this.categoryData
          i['mobile'] = this.userInfo.mobile
          i['username'] = this.userInfo.username
        })
      this.addDataSource = JSON.parse(JSON.stringify(this.addDataSource))
    },

    async confirm() {
      let _this = this
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let bol = this.addDataSource.every((item) => {
            return item?.categoryData?.length > 0 && item?.categoryData.every((v) => v.categoryType)
          })
          if (!bol && this.ruleForm.supplierType != 'logisticsProvider') {
            this.$toast({
              content: '请选择供应范围!',
              type: 'warning'
            })
            return
          }
          this.$loading()
          if (this.type == 'add') {
            // 新增：根据公司区分数据
            let inviteRequest = []
            this.addDataSource.forEach((item) => {
              //item下没有code
              let obj = {}
              obj['extList'] = []
              obj['businessType'] = 3
              obj['supplierType'] = this.ruleForm.supplierType || ''
              obj['customerBizName'] = item.orgName || ''
              obj['customerBizCode'] = item.orgCode || ''
              obj['customerTenantId'] = _this.userInfo.customerTenantId || ''
              delete item.mobile
              delete item.username
              if (item.categoryData) {
                item.categoryData.forEach((itemC) => {
                  // if (item.id == itemC.customerOrgId) {
                  obj.extList.push({
                    categoryType: itemC.categoryType || '',
                    customerCategoryCode: itemC.categoryCode || '',
                    customerCategoryId: itemC.categoryId || '',
                    customerCategoryName: itemC.categoryName || '',
                    customerFactoryCode: '',
                    customerFactoryId: '',
                    customerFactoryName: '',
                    customerOrgCode: item.orgCode || '',
                    customerOrgId: item.orgid || '',
                    customerOrgName: item.orgName || '',
                    relatedType: 1,
                    tenantId: _this.userInfo.customerTenantId || '',
                    categoryTypeCode: _this.categoryTypeCode || '',
                    categoryTypeName: _this.categoryTypeName || ''
                  })
                  // }
                })
              } else {
                obj.extList.push({
                  categoryType: '',
                  customerCategoryCode: '',
                  customerCategoryId: '',
                  customerCategoryName: '',
                  customerFactoryCode: '',
                  customerFactoryId: '',
                  customerFactoryName: '',
                  customerOrgCode: item.orgCode || '',
                  customerOrgId: item.orgid || '',
                  customerOrgName: item.orgName || '',
                  relatedType: 1,
                  tenantId: _this.userInfo.customerTenantId || '',
                  categoryTypeCode: _this.categoryTypeCode || '',
                  categoryTypeName: _this.categoryTypeName || ''
                })
              }
              inviteRequest.push(obj)
            })
            console.log(inviteRequest, 'inviteRequestinviteRequest')
            this.$API.supplierRegister
              .addSupplierInvite({ inviteRequest })
              .then((res) => {
                this.$hloading()
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
                this.$emit('confirm-function')
              })
              .catch((err) => {
                this.$hloading()
                this.$toast({
                  content: err.msg,
                  type: 'error'
                })
              })
          } else if (this.type == 'edit' || this.type == 'archivEdit') {
            let obj = {}
            this.addDataSource.forEach((item) => {
              //item下没有code
              obj['extList'] = []
              obj['businessType'] = 3
              obj['supplierType'] = this.ruleForm.supplierType || ''
              obj['customerBizName'] = item.orgName || ''
              // obj["customerBizCode"] = item.orgCode;
              obj['id'] = _this.editDialogBoxGridData.id || ''
              delete item.mobile
              delete item.username
              if (item.categoryData) {
                item.categoryData.forEach((itemC) => {
                  obj.extList.push({
                    categoryType: itemC.categoryType || '',
                    customerCategoryCode: itemC.categoryCode || '',
                    customerCategoryId: itemC.categoryId || '',
                    customerCategoryName: itemC.categoryName || '',
                    customerFactoryCode: '',
                    customerFactoryId: '',
                    customerFactoryName: '',
                    customerOrgCode: item.orgCode || '',
                    customerOrgId: item.orgid || '',
                    customerOrgName: item.orgName || '',
                    relatedType: 1,
                    categoryTypeCode: _this.categoryTypeCode || '',
                    categoryTypeName: _this.categoryTypeName || ''
                  })
                })
              } else {
                obj.extList.push({
                  categoryType: '',
                  customerCategoryCode: '',
                  customerCategoryId: '',
                  customerCategoryName: '',
                  customerFactoryCode: '',
                  customerFactoryId: '',
                  customerFactoryName: '',
                  customerOrgCode: item.orgCode || '',
                  customerOrgId: item.orgid || '',
                  customerOrgName: item.orgName || '',
                  relatedType: 1,
                  categoryTypeCode: _this.categoryTypeCode || '',
                  categoryTypeName: _this.categoryTypeName || ''
                })
              }
            })
            this.$API.supplierRegister
              .editSupplierInvite(obj)
              .then((res) => {
                this.$hloading()
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
                this.$emit('confirm-function')
              })
              .catch((err) => {
                this.$hloading()
                this.$toast({
                  content: err.msg,
                  type: 'error'
                })
              })
          }
        }
      })
    },

    // 保存并提交
    saveCommit() {
      let _this = this
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let bol = this.addDataSource.every((item) => {
            return item?.categoryData?.length > 0
          })
          if (!bol && this.ruleForm.supplierType != 'logisticsProvider') {
            this.$toast({
              content: '请选择供应范围!',
              type: 'warning'
            })
            return
          }
          this.$loading()
          if (this.type == 'add') {
            let inviteRequest = []
            this.addDataSource.forEach((item) => {
              //item下没有code
              let obj = {}
              obj['extList'] = []
              obj['businessType'] = 3
              obj['supplierType'] = this.ruleForm.supplierType || ''
              obj['customerBizName'] = item.orgName || ''
              obj['customerBizCode'] = item.orgCode || ''
              obj['customerTenantId'] = _this.userInfo.customerTenantId || ''
              delete item.mobile
              delete item.username
              if (item.categoryData) {
                item.categoryData.forEach((itemC) => {
                  // if (item.id == itemC.customerOrgId) {
                  obj.extList.push({
                    categoryType: itemC.categoryType || '',
                    customerCategoryCode: itemC.categoryCode || '',
                    customerCategoryId: itemC.categoryId || '',
                    customerCategoryName: itemC.categoryName || '',
                    customerFactoryCode: '',
                    customerFactoryId: '',
                    customerFactoryName: '',
                    customerOrgCode: item.orgCode || '',
                    customerOrgId: item.orgid || '',
                    customerOrgName: item.orgName || '',
                    relatedType: 1,
                    tenantId: _this.userInfo.customerTenantId || '',
                    categoryTypeCode: _this.categoryTypeCode || '',
                    categoryTypeName: _this.categoryTypeName || ''
                  })
                  // }
                })
              } else {
                obj.extList.push({
                  categoryType: '',
                  customerCategoryCode: '',
                  customerCategoryId: '',
                  customerCategoryName: '',
                  customerFactoryCode: '',
                  customerFactoryId: '',
                  customerFactoryName: '',
                  customerOrgCode: item.orgCode || '',
                  customerOrgId: item.orgid || '',
                  customerOrgName: item.orgName || '',
                  relatedType: 1,
                  tenantId: _this.userInfo.customerTenantId || '',
                  categoryTypeCode: _this.categoryTypeCode || '',
                  categoryTypeName: _this.categoryTypeName || ''
                })
              }
              inviteRequest.push(obj)
            })
            this.$API.supplierRegister
              .saveInvite({ inviteRequest })
              .then((res) => {
                this.$hloading()
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
                this.$emit('confirm-function')
              })
              .catch((err) => {
                this.$hloading()
                this.$toast({
                  content: err.msg,
                  type: 'error'
                })
              })
          } else if (this.type == 'edit' || this.type == 'archivEdit') {
            let obj = {}
            this.addDataSource.forEach((item) => {
              //item下没有code
              obj['extList'] = []
              obj['businessType'] = 3
              obj['supplierType'] = this.ruleForm.supplierType || ''
              obj['customerBizName'] = item.orgName || ''
              obj['customerBizCode'] = item.orgCode || ''
              obj['id'] = _this.editDialogBoxGridData.id || ''
              obj['customerTenantId'] = _this.editDialogBoxGridData.customerTenantId || ''
              delete item.mobile
              delete item.username
              if (item.categoryData) {
                item.categoryData.forEach((itemC) => {
                  obj.extList.push({
                    categoryType: itemC.categoryType || '',
                    customerCategoryCode: itemC.categoryCode || '',
                    customerCategoryId: itemC.categoryId || '',
                    customerCategoryName: itemC.categoryName || '',
                    customerFactoryCode: '',
                    customerFactoryId: '',
                    customerFactoryName: '',
                    customerOrgCode: item.orgCode || '',
                    customerOrgId: item.orgid || '',
                    customerOrgName: item.orgName || '',
                    relatedType: 1,
                    categoryTypeCode: _this.categoryTypeCode || '',
                    categoryTypeName: _this.categoryTypeName || ''
                  })
                })
              } else {
                obj.extList.push({
                  categoryType: '',
                  customerCategoryCode: '',
                  customerCategoryId: '',
                  customerCategoryName: '',
                  customerFactoryCode: '',
                  customerFactoryId: '',
                  customerFactoryName: '',
                  customerOrgCode: item.orgCode || '',
                  customerOrgId: item.orgid || '',
                  customerOrgName: item.orgName || '',
                  relatedType: 1,
                  categoryTypeCode: _this.categoryTypeCode || '',
                  categoryTypeName: _this.categoryTypeName || ''
                })
              }
            })
            this.$API.supplierRegister
              .editSaveInvite(obj)
              .then((res) => {
                this.$hloading()
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
                this.$emit('confirm-function')
              })
              .catch((err) => {
                this.$hloading()
                this.$toast({
                  content: err.msg,
                  type: 'error'
                })
              })
          }
        }
      })
    },
    // 模糊搜索品类查询==按钮
    async inputSearchBtn() {
      await this.inputCheckBox()
    },
    // 模糊搜索品类重置==按钮
    inputResetBtn() {
      this.inputSearchModel = ''
      this.inputCheckBox()
    },
    // 重置/搜索勾选
    async inputCheckBox() {
      this.categoryTree.dataSource = await this.getCateGoryTree()
      this.categoryTree.dataSource.forEach((i) => {
        this.categoryData.forEach((iC) => {
          if (iC.categoryId == i.id) {
            i.selected = true
          }
        })
      })
      this.categoryTree.dataSource = JSON.parse(JSON.stringify(this.categoryTree.dataSource))
    },
    handleAllChange(val) {
      //因为checkboxIndeterminate的影响, 会导致val永远为true, 需要手动调整
      if (this.checkboxIndeterminate && val.checked) {
        this.checkboxChecked = true
        this.checkboxIndeterminate = false
      } else {
        this.checkboxChecked = val.checked
      }

      this.categoryData = []
      this.categoryCode = []
      this.categoryName = []
      if (this.checkboxChecked) {
        this.categoryTree.dataSource.forEach((item) => {
          if (!item.pldisabled) {
            item.selected = true
            let str =
              item.categoryType == 1
                ? this.$t('原厂')
                : item.categoryType == 2
                ? this.$t('代理')
                : ''
            this.categoryData.push({
              id: item.id,
              name: item.categoryName + `(${str})`,
              categoryId: item.id,
              categoryCode: item.categoryCode,
              categoryName: item.categoryName,
              categoryType: item.categoryType
            })
          }
        })
        this.categoryData.forEach((ic) => {
          this.categoryCode.push(ic.categoryCode)
          this.categoryName.push(ic.name)
        })
        this.addDataSource &&
          this.addDataSource.forEach((i) => {
            i['categoryCode'] = this.categoryCode.join('，')
            i['categoryName'] = this.categoryName.join('，')
            i['categoryData'] = this.categoryData
            i['mobile'] = this.userInfo.mobile
            i['username'] = this.userInfo.username
          })
      } else {
        this.categoryTree.dataSource.forEach((item) => {
          item.selected = false
        })
        this.addDataSource &&
          this.addDataSource.forEach((i) => {
            i['categoryCode'] = ''
            i['categoryName'] = ''
            i['categoryData'] = ''
            i['mobile'] = this.userInfo.mobile
            i['username'] = this.userInfo.username
          })
      }
      this.addDataSource = JSON.parse(JSON.stringify(this.addDataSource))
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-col {
  padding: 0 10px 10px 10px;
}
/deep/ .select-container {
  height: 46px;
  padding: 5px;
}
/deep/ .e-input-group {
  padding-left: 5px;
}
.searchInput {
  /deep/ .e-input-group {
    border: none;
  }
}
.slider-panel-container {
  .slider-modal {
    width: 850px;
    border: none;
    .slider-content {
      padding: 12px 20px;
    }
    .slider-header {
      height: 58px;
      background: #00469c;
      color: #fff;
      font-size: 18px;

      .slider-title {
        color: #fff;
        font-size: 18px;
      }

      .slider-close {
        color: #fff;
      }
    }

    .add-rule-group {
      margin-top: 30px;
      height: 14px;
      font-size: 14px;
      font-weight: 600;
      color: rgba(0, 70, 156, 1);
      width: 100%;
      text-align: center;
      cursor: pointer;
    }

    .slider-footer {
      height: 56px;
      background: rgba(245, 245, 245, 1);
      color: #00469c;
      font-size: 14px;
      flex-direction: row;
      box-shadow: none;
    }
  }
}
.ruleForm {
  .fbox {
    display: flex;
  }
  .dialog-content-h4 {
    /deep/.mt-data-grid .e-grid .e-gridcontent {
      max-height: 30vh;
      overflow: auto;
    }
    h4 {
      font-size: 14px;
      color: #292929;
      font-weight: bold;
      margin: 15px 0 10px 0;
    }
    .all-list-box {
      display: flex;
      background: #fff;
      border: 1px solid #e8e8e8;
      height: 30vh;
      border-radius: 4px;
    }
    .company-tree-box {
      width: 200px;
      height: 100%;
      overflow-y: scroll;

      .factory-item {
        padding-left: 40px;
        width: 100%;
        height: 30px;
        line-height: 30px;
        cursor: pointer;
      }

      .category-content {
        display: flex;
        justify-content: left;
        height: 30px;
        line-height: 30px;
        padding: 0 16px;
        /deep/ .select-container {
          height: 30px;
          padding: 0;
        }
        .plname {
          width: 190px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          margin-left: 10px;
        }
      }

      .active {
        background: #f5f6f9;
      }
    }

    .category-tree-box {
      flex: 1;
      width: 200px;
      height: 100%;
      border-left: 1px solid #e8e8e8;
      overflow: scroll;
      .category-content {
        display: flex;
        justify-content: space-between;
        height: 30px;
        line-height: 30px;
        padding: 0 16px;
        /deep/ .select-container {
          height: 30px;
          padding: 0;
        }
        .plname {
          width: 60%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    .select-list-box {
      display: flex;
      flex-direction: column;
      flex: 1;
      margin-left: 20px;
      .select-list {
        padding: 10px 20px;
        height: 250px;
        overflow: auto;
        background: #fff;
        border: 1px solid #e8e8e8;
        width: 100%;
        border-radius: 4px;

        .select-tree {
          width: 100%;
        }
      }
    }
    .category-no-data {
      font-size: 14px;
      color: #9a9a9a;
      padding-top: 20px;
      text-align: center;
    }
    .category-delete-data {
      width: 100%;
      text-align: center;
      font-size: 14px;

      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      margin-top: 38px;
      cursor: pointer;
    }
  }
  #container {
    display: inline-block;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 75px;
  }
  // .e-input-calendar:before {
  //   content: '\e901';
  //   font-family: e-icons;
  //   font-size: 13px;
  // }
}

.input-icon-merge {
  .checkbox-block {
    width: 100px;
    margin: 0 10px;
  }
  // position: relative;
  display: flex;
  align-items: center;
  .icon-merge {
    position: absolute;
  }
  .btn-merge {
    width: 70px;
    margin-left: 10px;
    /deep/ .e-btn.e-outline {
      width: 100%;
    }
  }
}
</style>
