<template>
  <div class="lifeCycle-container">
    <!-- 顶部信息 start -->
    <div class="lifeCycle-top">
      <div class="lifeCycle-left">
        <div style="margin-left: 16px">
          <p class="leftCode">{{ $t('单据编号：') }}{{ headerInfo.applyCode || '--' }}</p>
          <p class="leftTitle">
            <span>{{ $t('创建人：') }}{{ headerInfo.createUserName }}</span>
            <span style="margin-left: 20px">{{ checkDate(headerInfo.createTime) }}</span>
          </p>
        </div>
      </div>
      <div style="display: flex">
        <div class="btnClass" @click="onBack">{{ $t('返回') }}</div>
        <div class="btnClass" v-show="isShow" @click="saveAndCommit('save')">
          {{ $t('保存') }}
        </div>
        <div class="btnClass" v-show="isShow" @click="saveAndCommit('commit')">
          {{ $t('保存并提交') }}
        </div>
        <div class="btnClass" v-show="isSubmit" @click="committed">
          {{ $t('提交') }}
        </div>
        <div class="btnClass" v-show="isSAPbtnShow" @click="reissueSAP">
          {{ $t('重新下发SAP') }}
        </div>
      </div>
    </div>

    <div style="margin-top: 16px">
      <mt-form ref="ruleForm" :model="buyerInfoChangeHead" :rules="rules">
        <mt-row type="flex" justify="space-around">
          <mt-col :span="7">
            <mt-form-item :label="$t('公司')" prop="orgId">
              <!-- <mt-select
                :disabled="isDisabled"
                v-model="buyerInfoChangeHead.orgId"
                :data-source="orgList"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择')"
                @change="orgChange"
              ></mt-select> -->
              <RemoteAutocomplete
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                v-model="buyerInfoChangeHead.orgCode"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                @change="orgChange"
                :disabled="isDisabled"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="administrativeCompany"
              ></RemoteAutocomplete>
            </mt-form-item>
          </mt-col>
          <mt-col :span="7">
            <mt-form-item :label="$t('供应商')" prop="supplierEnterpriseId">
              <mt-select
                :disabled="isDisabled"
                v-model="buyerInfoChangeHead.supplierEnterpriseId"
                :allow-filtering="true"
                :filtering="(e) => filteringResource(e, supplierList, 'supplierEnterpriseName')"
                :data-source="supplierList"
                :fields="{
                  text: 'supplierEnterpriseName',
                  value: 'supplierEnterpriseId'
                }"
                :placeholder="$t('请先选择公司')"
                @change="supplierEnterpriseChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="7">
            <mt-form-item :label="$t('佐证材料')">
              <mt-common-uploader
                :save-url="saveUrl"
                :download-url="downloadUrl"
                :is-single-file="false"
                type="line"
                v-model="buyerInfoChangeHead.supportingMaterials"
              ></mt-common-uploader>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row type="flex" justify="space-around">
          <mt-col :span="23">
            <mt-form-item :label="$t('备注')">
              <mt-input
                type="text"
                :multiline="true"
                v-model="buyerInfoChangeHead.remark"
                max-length="200"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
      <mt-tabs
        :e-tab="false"
        overflow-mode="Popup"
        :data-source="dataSource"
        :selected-item="selectIndex"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
      <div v-if="selectIndex == 0">
        <base-info
          ref="baseInfoRef"
          :key="baseKey"
          :router-type="routerType"
          :partner-archive-id="partnerArchiveId"
          :org-id="orgId"
          :supplier-enterprise-id="supplierEnterpriseId"
          :partner-relation-code="partnerRelationCode"
          :apply-code="headerInfo.applyCode"
        ></base-info>
      </div>
      <div v-if="selectIndex == 1">
        <business-info
          ref="businessRef"
          :key="businessKey"
          :router-type="routerType"
          :partner-archive-id="partnerArchiveId"
          :org-id="orgId"
          :supplier-enterprise-id="supplierEnterpriseId"
          :partner-relation-code="partnerRelationCode"
          :apply-code="headerInfo.applyCode"
        ></business-info>
      </div>
      <div v-if="selectIndex == 2">
        <relevant-certificate
          ref="certificateRef"
          :key="certificateKey"
          :router-type="routerType"
          :partner-archive-id="partnerArchiveId"
          :org-id="orgId"
          :supplier-enterprise-id="supplierEnterpriseId"
          :partner-relation-code="partnerRelationCode"
          :apply-code="headerInfo.applyCode"
        ></relevant-certificate>
      </div>
      <div v-if="selectIndex == 3">
        <personnel-equipment
          ref="equipRef"
          :key="equipKey"
          :router-type="routerType"
          :partner-archive-id="partnerArchiveId"
          :org-id="orgId"
          :supplier-enterprise-id="supplierEnterpriseId"
          :partner-relation-code="partnerRelationCode"
          :apply-code="headerInfo.applyCode"
        ></personnel-equipment>
      </div>
      <div v-if="selectIndex == 4">
        <delivery-cycle
          ref="cycleRef"
          :key="cycleKey"
          :router-type="routerType"
          :partner-archive-id="partnerArchiveId"
          :org-id="orgId"
          :supplier-enterprise-id="supplierEnterpriseId"
          :partner-relation-code="partnerRelationCode"
          :apply-code="headerInfo.applyCode"
        ></delivery-cycle>
      </div>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import baseInfo from '../components/baseInfo.vue'
import businessInfo from '../components/businessInfo.vue'
import relevantCertificate from '../components/relevantCertificate.vue'
import personnelEquipment from '../components/personnelEquipment.vue'
import deliveryCycle from '../components/deliveryCycle.vue'
import commonData from '@/utils/constant'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    baseInfo, //基本信息
    businessInfo, //财务信息
    relevantCertificate, //相关证书
    personnelEquipment, //人员设备
    deliveryCycle, //交货周期
    RemoteAutocomplete
  },
  data() {
    return {
      saveUrl: commonData.privateFileUrl,
      downloadUrl: commonData.downloadUrl,
      baseKey: 1,
      businessKey: 1,
      certificateKey: 1,
      equipKey: 1,
      cycleKey: 1,
      // orgList: [], //公司
      supplierList: [], //供应商
      templateConfig: {
        grid: {
          columnData: []
        }
      },
      dataSource: [
        { title: this.$t('基本信息') },
        { title: this.$t('财务信息') },
        { title: this.$t('相关证书') },
        { title: this.$t('人员及设备') },
        { title: this.$t('交货周期') }
        // { title: this.$t("调查表信息") },
        // { title: this.$t("历史版本") },
      ],
      routerType: '',
      partnerArchiveId: '',
      orgId: '',
      supplierEnterpriseId: '',
      partnerRelationCode: '',
      selectIndex: 0,
      headerInfo: {
        applyCode: '',
        createUserName: '',
        createTime: ''
      },
      organizationId: 0,
      dataArr: [],
      ruleForm: {},
      rules: {
        orgId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        supplierEnterpriseId: [
          { required: true, message: this.$t('请选择供应商'), trigger: 'blur' }
        ]
      },
      buyerInfoChangeHead: {
        applyCode: '',
        orgId: '',
        orgName: '',
        orgCode: '',
        remark: '',
        supplierEnterpriseId: '',
        supplierEnterpriseCode: '',
        supplierEnterpriseName: '',
        supportingMaterials: []
      },
      asyncSettings: [],
      isShow: true,
      isSubmit: false,
      isDisabled: false,
      isSAPbtnShow: false,

      isSave: false // 是否未保存
    }
  },
  async created() {
    // 公司
    // const userDetail = await this.getUserDetail();

    // if (userDetail && userDetail.id) {
    //   this.getChildrenCompanyOrganization(userDetail.id);
    // }

    this.routerType = this.$route.query.type
    if (this.routerType == 'add') {
      this.isSubmit = false
      // 详情
      this.getAccessDetail()
    } else if (this.routerType == 'edit' || this.routerType == 'view') {
      this.isDisabled = true //编辑进来头部信息禁用
      let id = this.$route.query.id
      let columnData = await this.getDetailById(id)
      this.headerInfo = columnData
      // 获取参数
      let partnerRelationCode = columnData.partnerRelationCode || ''
      let partnerArchiveId = columnData.id
      let orgId = columnData.orgId
      let supplierEnterpriseId = columnData.supplierEnterpriseId
      let organizationId = columnData.organizationId || ''
      //  supplierEnterpriseId + code 或者supplierEnterpriseId + orgId

      this.partnerRelationCode = partnerRelationCode || ''
      this.partnerArchiveId = partnerArchiveId || ''
      this.orgId = orgId
      this.supplierEnterpriseId = supplierEnterpriseId || ''
      this.organizationId = organizationId // 供应商资源库跳转带公司id
      // 给公司下拉框赋值，供应商下拉框赋值
      this.buyerInfoChangeHead.orgId = orgId
      this.buyerInfoChangeHead.supplierEnterpriseId = supplierEnterpriseId
      this.buyerInfoChangeHead.applyCode = columnData.applyCode
      this.buyerInfoChangeHead.remark = columnData.supplierRemark
      this.buyerInfoChangeHead.supportingMaterials = columnData.supportingMaterials
      // 组件下载用的是id   需要把fileId赋值给id
      if (Array.isArray(this.buyerInfoChangeHead?.supportingMaterials)) {
        this.buyerInfoChangeHead?.supportingMaterials.forEach((item) => {
          if (item.fileId) {
            item.id = item.fileId
          }
        })
      }
      this.baseKey++
      if (this.routerType == 'view') {
        this.isShow = false
        this.isSubmit = false
      } else {
        let status = columnData.applyStatus
        if (status == 90) {
          this.isSAPbtnShow = true
        }
        let documentSender = columnData.documentSender
        if (documentSender == 1) {
          this.isShow = false
          if (status == 10) {
            this.isSubmit = true
          } else {
            this.isSubmit = false
          }
        } else if (documentSender == 0) {
          if (status == 10 || status == 30) {
            this.isShow = true
            // this.isSubmit = true
          } else {
            this.isShow = false
            // this.isSubmit = false
          }
        }
      }
    } else if (this.routerType == 'archive') {
      this.isDisabled = true
      // 从供应商档案跳转来的数据
      let partnerRelationCode = this.$route.query.partnerRelationCode
      let partnerArchiveId = this.$route.query.partnerArchiveId
      let orgId = this.$route.query.orgId
      let supplierEnterpriseId = this.$route.query.supplierEnterpriseId
      let status = this.$route.query.status

      this.partnerRelationCode = partnerRelationCode || ''
      this.partnerArchiveId = partnerArchiveId || ''
      this.orgId = orgId
      this.supplierEnterpriseId = supplierEnterpriseId || ''
      this.status = status || ''
      this.isSubmit = false
      if (status == 10 || status == 1 || status == 2) {
        this.isShow = true
      } else {
        this.isShow = false
      }
      this.getDataFromArchive()
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('infoChangeSupplierCode')
  },
  methods: {
    // 重新下发sap
    reissueSAP() {
      console.log('this.$refs.baseInfoRef', this.$refs.baseInfoRef.baseInfoInsideDTO.id)
      const params = {
        applyCode: this.headerInfo.applyCode,
        newBaseInfoInsideId: this.$refs.baseInfoRef.baseInfoInsideDTO.id
      }
      this.$API.infoChange.reissueSAP(params).then((res) => {
        this.$toast({
          content: res.msg,
          type: 'success'
        })
      })
    },
    // 处理公司接口响应数据（处理返回的接口数据，会把接口请求的数据返回，然后数据处理完以后请用return返回）
    handleCompanyData(resData = []) {
      resData = resData.filter((item) => {
        return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
      })
      return resData
    },
    checkDate(e) {
      if (e) {
        if (e == 0) {
          return (e = '')
        } else if (typeof e == 'object') {
          return this.$utils.formateTime(e, 'yyyy-MM-dd')
        } else if (typeof e == 'string') {
          if (e.indexOf('T') != -1) {
            return e.substr(0, 10)
          } else {
            let val = parseInt(e)
            return this.$utils.formateTime(val, 'yyyy-MM-dd')
          }
        } else if (typeof e == 'number') {
          return this.$utils.formateTime(e, 'yyyy-MM-dd')
        } else {
          return e
        }
      } else {
        return e
      }
    },
    // 根据id查询详情
    getDetailById(id) {
      this.$loading()
      return this.$API.infoChange
        .getDetailById(id)
        .then((res) => {
          this.$hloading()
          let { data } = res
          return data
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // getUserDetail() {
    //   this.$loading();
    //   return this.$API.supplierPurchaseData.queryUserDetail().then((res) => {
    //     this.$hloading();
    //     let { data } = res;
    //     let { companyOrg } = data;
    //     return companyOrg;
    //   }).catch(err=>{
    //     this.$hloading();
    //     this.$toast({
    //       content:err.msg,
    //       type:"error"
    //     })
    //   });;
    // },
    // getChildrenCompanyOrganization(userId) {
    //   // 临时接口修改 上面为正常版本
    //   // this.$API.supplierInvitation["getChildrenCompanyOrganization"]({
    //   //   organizationId: userId,
    //   // }).then((result) => {
    //   this.$API.supplierInvitation["getChildrenCompanyOrganization2"]({
    //     organizationLevelCodes: ["ORG02", "ORG01"],
    //     orgType: "ORG001PRO",
    //     includeItself: true,
    //   }).then((result) => {
    //     if (result.code === 200 && !this.$utils.isEmpty(result.data)) {
    //       this.orgList = result.data.filter((item) => {
    //         return item.orgLevelTypeCode === "ORG02" ||
    //           item.orgLevelTypeCode === "ORG01";
    //       });
    //     } else {
    //       this.orgList = [];
    //     }
    //   });
    // },
    getDataFromArchive() {
      // this.$loading();
      let obj = {
        type: 'archive',
        partnerArchiveId: this.partnerArchiveId,
        orgId: this.orgId,
        supplierEnterpriseId: this.supplierEnterpriseId,
        partnerRelationCode: this.partnerRelationCode,
        status: this.status
      }
      this.$API.infoChange
        .getDataFromArchive(obj)
        .then((res) => {
          // this.$hloading();
          let { data } = res
          this.headerInfo = data
          this.buyerInfoChangeHead.orgId = data.orgId
          this.buyerInfoChangeHead.supplierEnterpriseId = data.supplierEnterpriseId
          this.buyerInfoChangeHead.applyCode = data.applyCode
        })
        .catch((err) => {
          // this.$hloading();
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    orgChange(item) {
      // 有级联
      let { itemData } = item
      this.buyerInfoChangeHead.orgId = itemData.id
      this.buyerInfoChangeHead.orgName = itemData.orgName
      this.buyerInfoChangeHead.orgCode = itemData.orgCode
      if (itemData?.id) {
        this.getOrgPartnerRelations(itemData.id)
      } else {
        // 清空供应商绑定值 和 dataSource
        this.buyerInfoChangeHead.supplierEnterpriseId = ''
        this.supplierList = []
      }
    },
    getOrgPartnerRelations(orgId) {
      // this.$loading();
      this.$API.supplierEffective
        .getOrgPartnerRelations({
          orgId
        })
        .then((res) => {
          // this.$hloading();
          this.supplierList = res.data
        })
        .catch((err) => {
          // this.$hloading();
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 选中供应商，编辑状态下不需要赋值，新增情况下重新赋值
    supplierEnterpriseChange(item) {
      let { itemData } = item
      this.buyerInfoChangeHead.supplierEnterpriseId = itemData.supplierEnterpriseId
      this.buyerInfoChangeHead.supplierEnterpriseCode = itemData.supplierEnterpriseCode
      this.buyerInfoChangeHead.supplierEnterpriseName = itemData.supplierEnterpriseName
      sessionStorage.setItem('infoChangeSupplierCode', itemData.supplierCode)
      // 编辑状态禁用不需要再次调用
      if (this.routerType == 'edit' || this.routerType == 'archive') {
        return
      }
      // 供应商选择完后，拿到参数，重新查询下面tab页面的接口
      if (this.routerType == 'add' || this.routerType == 'archive') {
        this.partnerRelationCode = itemData.partnerRelationCode
        this.partnerArchiveId = itemData.partnerArchiveId
        this.orgId = itemData.orgId
        this.supplierEnterpriseId = itemData.supplierEnterpriseId
      }
      // 默认加载第一个
      this.selectIndex = 0
      //把基本信息的缓存清掉  不然会有问题
      sessionStorage.setItem('changeBaseInfo', null)
      sessionStorage.setItem('changeBusinessInfo', null)
      sessionStorage.setItem('changeCycleInfo', null)
      sessionStorage.setItem('changeEquipInfo', null)
      sessionStorage.setItem('changeCertificateInfo', null)

      if (this.selectIndex == 0) {
        this.baseKey++
      } else if (this.selectIndex == 1) {
        this.businessKey++
      } else if (this.selectIndex == 2) {
        this.certificateKey++
      } else if (this.selectIndex == 3) {
        this.equipKey++
      } else if (this.selectIndex == 4) {
        this.cycleKey++
      }
    },
    // 模糊搜索，不区分大小写模糊搜索
    filteringResource(e, dataSource, key) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          dataSource?.filter((f) => f[key]?.toUpperCase().includes(e?.text?.toUpperCase()))
        )
      } else {
        e.updateData(dataSource)
      }
    },

    // 返回
    onBack() {
      this.$router.go(-1)
    },

    // 提交接口
    committed() {
      let applyCodeList = []
      applyCodeList.push(this.buyerInfoChangeHead.applyCode)
      this.$loading()
      this.$API.infoChange
        .infoChangeCommit({ applyCodeList })
        .then(() => {
          this.$hloading()
          this.$router.replace({ name: 'info-change-apply-pur' })
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    preservation() {
      if (this.selectIndex == 0) {
        let baseInfo = this.$refs.baseInfoRef
        baseInfo.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        baseInfo.$refs.templateRefShipEdit.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      } else if (this.selectIndex == 1) {
        let businessInfo = this.$refs.businessRef
        businessInfo.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      } else if (this.selectIndex == 2) {
        let certificateInfo = this.$refs.certificateRef
        certificateInfo.$refs.certificateInfoEditRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        certificateInfo.$refs.patentInfoEditRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        certificateInfo.$refs.InnovationAwardsEditRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        certificateInfo.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      } else if (this.selectIndex == 3) {
        let equipInfo = this.$refs.equipRef
        equipInfo.$refs.patentInfoEditRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        equipInfo.$refs.testRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        equipInfo.$refs.InnovationAwardsEditRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        equipInfo.$refs.certificateInfoEditRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      } else if (this.selectIndex == 4) {
        let cycleInfo = this.$refs.cycleRef
        cycleInfo.$refs.templateBaseRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        cycleInfo.$refs.templateProcureRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        cycleInfo.$refs.templatePhaseRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        cycleInfo.$refs.templateSubconRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        cycleInfo.$refs.templateTimeRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
      return new Promise((r) => {
        setTimeout(() => {
          r()
        }, 300)
      })
    },
    saveAndCommit(type, cb) {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          this.$loading()
          await this.preservation()
          let saveObj = {}
          let baseObj = {}
          let businessObj = {}
          let baseExtObj = {}
          let certObj = {}
          // 如果页面停留在当前tab页上，能获取到组件的ref,就取当前tab页里最新的数据上传，否则从sessionstory 里取tab的数据
          // 基本信息
          let baseInfo = this.$refs.baseInfoRef
          if (baseInfo && (!baseInfo.isValid || !baseInfo.baseInfoEdit.supplierNameEn)) {
            this.$hloading()
            !baseInfo.baseInfoEdit.supplierNameEn
              ? this.$toast({ content: this.$t('供应商英文名称不能为空'), type: 'warning' })
              : this.$toast({
                  content: this.$t('供应商英文名称不合法，请输入合法的英文名称'),
                  type: 'warning'
                })
            return
          }
          if (baseInfo) {
            saveObj.baseInfoDTO = baseInfo.baseInfoEdit
            baseExtObj = baseInfo.baseInfoExtDTOEdit
            baseObj = baseInfo.baseInfoInsideDTOEdit
            baseInfo.shipAllData.map((item) => {
              if (
                typeof item.capitalContributionDate == 'object' ||
                (item.capitalContributionDate.length && item.capitalContributionDate.length > 13)
              ) {
                item.capitalContributionDate = item.capitalContributionDate
                  ? new Date(item.capitalContributionDate).getTime()
                  : 0
              }
            })
            saveObj.ownershipStructureDTOList = baseInfo.shipAllData //股权
            baseInfo.allData.map((item, index) => {
              if (item?.through) {
                baseInfo.allData.splice(index, 1)
              }
            })
            saveObj.contactInsideDTOList = baseInfo.allData //列表信息
            if (baseInfo?.factoryAddressData.length !== 0) {
              saveObj.baseInfoDTO.factoryAddress = JSON.stringify(
                baseInfo.factoryAddressData.map((item) => {
                  return JSON.stringify({
                    address: item?.address
                  })
                })
              )
            }
            saveObj.baseInfoDTO.supplyAddress =
              baseInfo.baseInfoEdit.area?.length !== 0
                ? JSON.stringify(baseInfo.baseInfoEdit.area)
                : ''
          } else {
            // 从sessionStory里拿出存的值 如果没有存则不上传
            let changeBaseInfo = JSON.parse(sessionStorage.getItem('changeBaseInfo'))
            if (changeBaseInfo) {
              saveObj.baseInfoDTO = changeBaseInfo.baseInfoEdit
              baseExtObj = changeBaseInfo.baseInfoExtDTOEdit
              baseObj = changeBaseInfo.baseInfoInsideDTOEdit
              let shipAllData = JSON.parse(changeBaseInfo.shipAllData)
              shipAllData.map((item) => {
                if (
                  typeof item.capitalContributionDate == 'object' ||
                  (item.capitalContributionDate.length && item.capitalContributionDate.length > 13)
                ) {
                  item.capitalContributionDate = item.capitalContributionDate
                    ? new Date(item.capitalContributionDate).getTime()
                    : 0
                }
              })
              saveObj.ownershipStructureDTOList = shipAllData
              let allData = JSON.parse(changeBaseInfo.allData)
              allData.map((item, index) => {
                if (item?.through) {
                  baseInfo.allData.splice(index, 1)
                }
              })
              saveObj.contactInsideDTOList = allData
              if (changeBaseInfo?.factoryAddressData.length !== 0) {
                saveObj.baseInfoDTO.factoryAddress = JSON.stringify(
                  JSON.parse(changeBaseInfo.factoryAddressData).map((item) => {
                    return JSON.stringify({
                      address: item?.address
                    })
                  })
                )
              }
              saveObj.baseInfoDTO.supplyAddress =
                changeBaseInfo.baseInfoEdit.area?.length !== 0
                  ? JSON.stringify(changeBaseInfo.baseInfoEdit.area)
                  : ''
            }
          }
          // 财务
          let businessInfo = this.$refs.businessRef
          if (businessInfo) {
            saveObj.financeInfoDTO = businessInfo.financialBaseInfoEdit
            businessObj = businessInfo.baseInfoInsideDTOEdit
            saveObj.bankInsideDTOList = businessInfo.shipAllData
          } else {
            let changeBusinessInfo = JSON.parse(sessionStorage.getItem('changeBusinessInfo'))
            if (changeBusinessInfo) {
              saveObj.financeInfoDTO = changeBusinessInfo.financialBaseInfoEdit
              businessObj = changeBusinessInfo.businessInfoInsideDTOEdit
              let businessData = JSON.parse(changeBusinessInfo.businessData)
              saveObj.bankInsideDTOList = businessData
            }
          }
          // 相关证书
          let certificateInfo = this.$refs.certificateRef
          if (certificateInfo) {
            let certBaseInfoEdit = cloneDeep(certificateInfo.certBaseInfoEdit)
            if (certBaseInfoEdit.expireDate) {
              if (
                typeof certBaseInfoEdit.expireDate == 'object' ||
                (certBaseInfoEdit.expireDate.length && certBaseInfoEdit.expireDate.length > 13)
              ) {
                certBaseInfoEdit.expireDate = certBaseInfoEdit.expireDate
                  ? new Date(certBaseInfoEdit.expireDate).getTime()
                  : 0
              }
            } else {
              certBaseInfoEdit.expireDate = ''
            }

            certObj = certBaseInfoEdit
            certificateInfo.certificateAllData.map((item) => {
              if (
                typeof item.annualReviewDate == 'object' ||
                (item.annualReviewDate.length && item.annualReviewDate.length > 13)
              ) {
                item.annualReviewDate = item.annualReviewDate
                  ? new Date(item.annualReviewDate).getTime()
                  : 0
              }
              if (
                typeof item.effectiveDate == 'object' ||
                (item.effectiveDate.length && item.effectiveDate.length > 13)
              ) {
                item.effectiveDate = item.effectiveDate ? new Date(item.effectiveDate).getTime() : 0
              }
              if (
                typeof item.expireDate == 'object' ||
                (item.expireDate.length && item.expireDate.length > 13)
              ) {
                item.expireDate = item.expireDate ? new Date(item.expireDate).getTime() : 0
              }
            })
            certificateInfo.patentAllData.map((item) => {
              if (
                typeof item.effectiveDate == 'object' ||
                (item.effectiveDate.length && item.effectiveDate.length > 13)
              ) {
                item.effectiveDate = item.effectiveDate ? new Date(item.effectiveDate).getTime() : 0
              }
              if (
                typeof item.expireDate == 'object' ||
                (item.expireDate.length && item.expireDate.length > 13)
              ) {
                item.expireDate = item.expireDate ? new Date(item.expireDate).getTime() : 0
              }
              item.attachment = item?.attachment || []
              item.agentCategoryInfo =
                item.agentCategoryInfo?.length !== 0
                  ? JSON.stringify(item.agentCategoryInfo)
                  : JSON.stringify([])
              item.originAcceptMsg =
                item.originAcceptMsg?.length !== 0
                  ? JSON.stringify(item.originAcceptMsg)
                  : JSON.stringify([])
            })
            certificateInfo.AwardsAllData.map((item) => {
              if (
                typeof item.awardDate == 'object' ||
                (item.awardDate.length && item.awardDate.length > 13)
              ) {
                item.awardDate = item.awardDate ? new Date(item.awardDate).getTime() : 0
              }
            })
            certificateInfo.templateData.map((item) => {
              if (
                typeof item.awardDate == 'object' ||
                (item.awardDate.length && item.awardDate.length > 13)
              ) {
                item.awardDate = item.awardDate ? new Date(item.awardDate).getTime() : 0
              }
            })
            saveObj.certificateDTOList = []
              .concat(certificateInfo.certificateAllData)
              .concat(certificateInfo.patentAllData)
              .concat(certificateInfo.AwardsAllData)
              .concat(certificateInfo.templateData)
          } else {
            let changeCertificateInfo = JSON.parse(sessionStorage.getItem('changeCertificateInfo'))
            if (changeCertificateInfo) {
              if (changeCertificateInfo.certBaseInfoEdit.expireDate) {
                if (typeof changeCertificateInfo.certBaseInfoEdit.expireDate == 'object') {
                  changeCertificateInfo.certBaseInfoEdit.expireDate = new Date(
                    changeCertificateInfo.certBaseInfoEdit.expireDate
                  ).getTime()
                }
              } else {
                changeCertificateInfo.certBaseInfoEdit.expireDate = 0
              }

              certObj = changeCertificateInfo.certBaseInfoEdit
              let certificateAllData = JSON.parse(changeCertificateInfo.certificateAllData)
              let patentAllData = JSON.parse(changeCertificateInfo.patentAllData)
              let AwardsAllData = JSON.parse(changeCertificateInfo.AwardsAllData)
              let templateData = JSON.parse(changeCertificateInfo.templateData)
              certificateAllData.map((item) => {
                if (
                  typeof item.annualReviewDate == 'object' ||
                  (item.annualReviewDate.length && item.annualReviewDate.length > 13)
                ) {
                  item.annualReviewDate = item.annualReviewDate
                    ? new Date(item.annualReviewDate).getTime()
                    : 0
                }
                if (
                  typeof item.effectiveDate == 'object' ||
                  (item.effectiveDate.length && item.effectiveDate.length > 13)
                ) {
                  item.effectiveDate = item.effectiveDate
                    ? new Date(item.effectiveDate).getTime()
                    : 0
                }
                if (
                  typeof item.expireDate == 'object' ||
                  (item.expireDate.length && item.expireDate.length > 13)
                ) {
                  item.expireDate = item.expireDate ? new Date(item.expireDate).getTime() : 0
                }
              })
              patentAllData.map((item) => {
                if (
                  typeof item.effectiveDate == 'object' ||
                  (item.effectiveDate.length && item.effectiveDate.length > 13)
                ) {
                  item.effectiveDate = item.effectiveDate
                    ? new Date(item.effectiveDate).getTime()
                    : 0
                }
                if (
                  typeof item.expireDate == 'object' ||
                  (item.expireDate.length && item.expireDate.length > 13)
                ) {
                  item.expireDate = item.expireDate ? new Date(item.expireDate).getTime() : 0
                }
              })
              AwardsAllData.map((item) => {
                if (
                  typeof item.awardDate == 'object' ||
                  (item.awardDate.length && item.awardDate.length > 13)
                ) {
                  item.awardDate = item.awardDate ? new Date(item.awardDate).getTime() : 0
                }
              })
              templateData.map((item) => {
                if (
                  typeof item.awardDate == 'object' ||
                  (item.awardDate.length && item.awardDate.length > 13)
                ) {
                  item.awardDate = item.awardDate ? new Date(item.awardDate).getTime() : 0
                }
              })
              saveObj.certificateDTOList = []
                .concat(certificateAllData)
                .concat(patentAllData)
                .concat(AwardsAllData)
                .concat(templateData)
            }
          }
          // 人员及设备
          let equipInfo = this.$refs.equipRef
          if (equipInfo) {
            saveObj.staffInfoDTO = equipInfo.staffBaseInfoEdit
            equipInfo.AwardsAllData.map((item) => {
              if (
                typeof item.cooperationStartDate == 'object' ||
                (item.cooperationStartDate.length && item.cooperationStartDate.length > 13)
              ) {
                item.cooperationStartDate = item.cooperationStartDate
                  ? new Date(item.cooperationStartDate).getTime()
                  : 0
              }
            })
            saveObj.cooperatorDTOList = equipInfo.AwardsAllData //主要客户
            equipInfo.certificateAllData.map((item) => {
              if (
                typeof item.launchDate == 'object' ||
                (item.launchDate.length && item.launchDate.length > 13)
              ) {
                item.launchDate = item.launchDate ? new Date(item.launchDate).getTime() : 0
              }
            })
            equipInfo.patentAllData.map((item) => {
              if (
                typeof item.launchDate == 'object' ||
                (item.launchDate.length && item.launchDate.length > 13)
              ) {
                item.launchDate = item.launchDate ? new Date(item.launchDate).getTime() : 0
              }
            })
            equipInfo.testAllData.map((item) => {
              if (
                typeof item.launchDate == 'object' ||
                (item.launchDate.length && item.launchDate.length > 13)
              ) {
                item.launchDate = item.launchDate ? new Date(item.launchDate).getTime() : 0
              }
            })
            saveObj.deviceDTOList = []
              .concat(equipInfo.certificateAllData)
              .concat(equipInfo.patentAllData)
              .concat(equipInfo.testAllData)
          } else {
            let changeEquipInfo = JSON.parse(sessionStorage.getItem('changeEquipInfo'))
            if (changeEquipInfo) {
              saveObj.staffInfoDTO = changeEquipInfo.staffBaseInfoEdit
              let equipAwardsAllData = JSON.parse(changeEquipInfo.equipAwardsAllData)
              equipAwardsAllData.map((item) => {
                if (
                  typeof item.cooperationStartDate == 'object' ||
                  (item.cooperationStartDate.length && item.cooperationStartDate.length > 13)
                ) {
                  item.cooperationStartDate = item.cooperationStartDate
                    ? new Date(item.cooperationStartDate).getTime()
                    : 0
                }
              })
              saveObj.cooperatorDTOList = equipAwardsAllData
              let equipAllData = JSON.parse(changeEquipInfo.equipAllData) //管理
              let equipPatentAllData = JSON.parse(changeEquipInfo.equipPatentAllData) //生产设备
              let testAllData = JSON.parse(changeEquipInfo.testAllData)
              equipAllData.map((item) => {
                if (
                  typeof item.launchDate == 'object' ||
                  (item.launchDate.length && item.launchDate.length > 13)
                ) {
                  item.launchDate = item.launchDate ? new Date(item.launchDate).getTime() : 0
                }
              })
              equipPatentAllData.map((item) => {
                if (
                  typeof item.launchDate == 'object' ||
                  (item.launchDate.length && item.launchDate.length > 13)
                ) {
                  item.launchDate = item.launchDate ? new Date(item.launchDate).getTime() : 0
                }
              })
              testAllData.map((item) => {
                if (
                  typeof item.launchDate == 'object' ||
                  (item.launchDate.length && item.launchDate.length > 13)
                ) {
                  item.launchDate = item.launchDate ? new Date(item.launchDate).getTime() : 0
                }
              })
              saveObj.deviceDTOList = []
                .concat(equipAllData)
                .concat(equipPatentAllData)
                .concat(testAllData)
            }
          }
          // 交货周期
          let cycleInfo = this.$refs.cycleRef
          if (cycleInfo) {
            // 当前页面主表选中的数据
            let categoryRelationCode = cycleInfo.categoryRelationCode
            let mainIdEdit = cycleInfo.mainIdEdit
            cycleInfo.analyseEdit.id = mainIdEdit
            // 当前页面正展示的数据；
            let nowData = []
              .concat(cycleInfo.procureAllData)
              .concat(cycleInfo.productionAllData)
              .concat(cycleInfo.subconAllData)
              .concat(cycleInfo.hauAllData)
            // 从childrenDataSource里取出数据
            let analyseEditData = []
            let cycleInsideDTOList = []
            cycleInfo.baseAllData.forEach((item) => {
              // 当前页面正展示的数据替换掉存储的数据，因为当前展示的数据会有修改
              if (categoryRelationCode == item.categoryRelationCode) {
                item.childrenDataSource = {
                  cycleInsideDTOList: nowData,
                  deliveryInfoInsideDTO: cycleInfo.analyseEdit
                }
              }
              if (item.childrenDataSource.cycleInsideDTOList) {
                cycleInsideDTOList = cycleInsideDTOList.concat(
                  item.childrenDataSource.cycleInsideDTOList
                )
              }
              // 后台传参有变更，子表-分析的内容放到了主表里面
              let obj = item.childrenDataSource.deliveryInfoInsideDTO
              if (obj) {
                item.rawMaterialsAspect = obj.rawMaterialsAspect
                item.produceCycleAspect = obj.produceCycleAspect
                item.transportTimeAspect = obj.transportTimeAspect
                analyseEditData.push(obj)
              }
            })
            saveObj.deliveryInfoInsideDTOList = cycleInfo.baseAllData //基本信息
            saveObj.deliveryInfoInsideDTO = analyseEditData //子表-分析
            saveObj.cycleInsideDTOList = cycleInsideDTOList
          } else {
            let changeCycleInfo = JSON.parse(sessionStorage.getItem('changeCycleInfo'))
            // 从childrenDataSource里取出数据
            let analyseEditData = []
            let cycleInsideDTOList = []
            if (changeCycleInfo) {
              changeCycleInfo.forEach((item) => {
                if (item.childrenDataSource.cycleInsideDTOList) {
                  cycleInsideDTOList = cycleInsideDTOList.concat(
                    item.childrenDataSource.cycleInsideDTOList
                  )
                }
                let obj = item.childrenDataSource.deliveryInfoInsideDTO
                if (obj) {
                  analyseEditData.push(obj)
                  item.rawMaterialsAspect = obj.rawMaterialsAspect
                  item.produceCycleAspect = obj.produceCycleAspect
                  item.transportTimeAspect = obj.transportTimeAspect
                }
              })
              saveObj.deliveryInfoInsideDTOList = changeCycleInfo //主表
              //分析结论
              saveObj.deliveryInfoInsideDTO = analyseEditData
              //子表
              saveObj.cycleInsideDTOList = cycleInsideDTOList
            }
          }
          saveObj.baseInfoExtDTO = Object.assign(baseExtObj, certObj) //基本资料和财务资料的一些信息放到了一起
          saveObj.baseInfoInsideDTO = Object.assign(baseObj, businessObj)
          saveObj.buyerInfoChangeHeadDTO = this.buyerInfoChangeHead //详情字段
          if (this.routerType == 'edit') {
            saveObj.buyerInfoChangeHeadDTO.id = this.partnerArchiveId
          }

          if (type == 'save') {
            this.$API.infoChange
              .infoChangeSave(saveObj)
              .then((res) => {
                this.isSave = true
                this.$hloading()
                this.$toast({
                  content: this.$t('保存成功'),
                  type: 'success'
                })
                // 清楚缓存，刷新一下
                sessionStorage.removeItem('changeBaseInfo')
                sessionStorage.removeItem('baseStyle')
                sessionStorage.removeItem('changeBusinessInfo')
                sessionStorage.removeItem('businessStyle')
                sessionStorage.removeItem('changeCertificateInfo')
                sessionStorage.removeItem('certifiStyle')
                sessionStorage.removeItem('changeEquipInfo')
                sessionStorage.removeItem('equipStyle')
                sessionStorage.removeItem('changeCycleInfo')
                sessionStorage.removeItem('cycleStyle')
                this.selectIndex = 0
                this.handleSelectTab(0, 'refresh')
                // 不需要跳转页面
                // this.$router.replace({ name: "info-change-apply-pur" });

                // cyj：“刚才那个先点击保存后点击保存提交后报错的问题找到了，前端在点击保存并提交的时候，串行的先调用保存接口，再调用提交接口就好了。”
                if (typeof cb == 'function') {
                  cb(res)
                }
              })
              .catch((err) => {
                this.$hloading()
                this.$toast({
                  content: err.msg,
                  type: 'error'
                })
              })
          } else if (type == 'commit') {
            // cyj：“刚才那个先点击保存后点击保存提交后报错的问题找到了，前端在点击保存并提交的时候，串行的先调用保存接口，再调用提交接口就好了。”
            this.saveAndCommit('save', () => {
              this.$API.infoChange
                .infoChangeSaveAndCommit(saveObj)
                .then(() => {
                  this.$hloading()
                  this.$toast({
                    content: this.$t('保存成功'),
                    type: 'success'
                  })
                  this.$router.replace({ name: 'info-change-apply-pur' })
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'error'
                  })
                })
            })
          }
        }
      })
    },

    // tab页点击
    handleSelectTab(index, type) {
      if (this.selectIndex === index && type !== 'refresh') {
        return
      }
      // tab页点击，重新刷新tab页
      if (index == 0) {
        this.baseKey++
      } else if (index == 1) {
        this.businessKey++
      } else if (index == 2) {
        this.certificateKey++
      } else if (index == 3) {
        this.equipKey++
      } else if (index == 4) {
        this.cycleKey++
      }
      if (type != 'refresh') {
        // selectIndex 默认为0 ，当切换时，保存信息，然后再赋值selectIndex
        // 通过各个子组件的ref拿到编辑后的数据
        if (this.selectIndex == 0) {
          // 基本信息
          this.$nextTick(() => {
            let baseInfo = this.$refs.baseInfoRef
            let baseObj = {}
            baseObj.baseInfoEdit = baseInfo.baseInfoEdit
            baseObj.baseInfoExtDTOEdit = baseInfo.baseInfoExtDTOEdit
            baseObj.baseInfoInsideDTOEdit = baseInfo.baseInfoInsideDTOEdit
            baseObj.shipAllData = JSON.stringify(baseInfo.shipAllData) //股权
            baseObj.allData = JSON.stringify(baseInfo.allData) //信息表格
            baseObj.factoryAddressData = JSON.stringify(baseInfo.factoryAddressData) //工厂生产地址

            sessionStorage.setItem('changeBaseInfo', JSON.stringify(baseObj))
            sessionStorage.setItem('baseStyle', JSON.stringify(baseInfo.styleObj))
          })
        } else if (this.selectIndex == 1) {
          // 财务信息
          let businessInfo = this.$refs.businessRef
          let businessObj = {}
          businessObj.financialBaseInfoEdit = businessInfo.financialBaseInfoEdit
          businessObj.businessInfoInsideDTOEdit = businessInfo.baseInfoInsideDTOEdit
          businessObj.businessData = JSON.stringify(businessInfo.shipAllData)

          sessionStorage.setItem('changeBusinessInfo', JSON.stringify(businessObj))
          sessionStorage.setItem('businessStyle', JSON.stringify(businessInfo.styleObj))
        } else if (this.selectIndex == 2) {
          // 相关证书
          let certificateInfo = this.$refs.certificateRef
          let certificateObj = {}
          if (
            typeof certificateInfo.certBaseInfoEdit.expireDate == 'object' ||
            (certificateInfo.certBaseInfoEdit.expireDate.length &&
              certificateInfo.certBaseInfoEdit.expireDate.length > 13)
          ) {
            certificateInfo.certBaseInfoEdit.expireDate = certificateInfo.certBaseInfoEdit
              .expireDate
              ? new Date(certificateInfo.certBaseInfoEdit.expireDate).getTime()
              : 0
          }
          certificateObj.certBaseInfoEdit = certificateInfo.certBaseInfoEdit
          certificateObj.certificateAllData = JSON.stringify(certificateInfo.certificateAllData) //质量
          certificateObj.patentAllData = JSON.stringify(certificateInfo.patentAllData) //代理商
          certificateObj.AwardsAllData = JSON.stringify(certificateInfo.AwardsAllData) //专利
          certificateObj.templateData = JSON.stringify(certificateInfo.templateData) //创新
          // 本地存储先不concat 因为需要JSON 最后提交的时候再concat

          sessionStorage.setItem('changeCertificateInfo', JSON.stringify(certificateObj))
          sessionStorage.setItem('certifiStyle', JSON.stringify(certificateInfo.styleObj))
        } else if (this.selectIndex == 3) {
          // 人员及设备
          let equipInfo = this.$refs.equipRef
          let equipObj = {}
          equipObj.staffBaseInfoEdit = equipInfo.staffBaseInfoEdit
          equipObj.equipAwardsAllData = JSON.stringify(equipInfo.AwardsAllData) //主要客户
          equipObj.equipAllData = JSON.stringify(equipInfo.certificateAllData) //管理
          equipObj.equipPatentAllData = JSON.stringify(equipInfo.patentAllData) //生产设备
          equipObj.testAllData = JSON.stringify(equipInfo.testAllData) //生产设备

          sessionStorage.setItem('changeEquipInfo', JSON.stringify(equipObj))
          sessionStorage.setItem('equipStyle', JSON.stringify(equipInfo.styleObj))
        } else if (this.selectIndex == 4) {
          // 交货周期
          let cycleInfo = this.$refs.cycleRef
          // 右侧--当前页面主表选中的数据
          let mainIdEdit = cycleInfo.mainIdEdit
          cycleInfo.analyseEdit.id = mainIdEdit
          // 当前页面正展示的数据；
          let nowData = []
            .concat(cycleInfo.procureAllData)
            .concat(cycleInfo.productionAllData)
            .concat(cycleInfo.subconAllData)
            .concat(cycleInfo.hauAllData)
          // 把当前正展示的数据存到childrenDataSource
          cycleInfo.baseAllData.forEach((item) => {
            // 当前页面正展示的数据替换掉存储的数据，因为当前展示的数据会有修改
            if (cycleInfo.categoryRelationCode == item.categoryRelationCode) {
              item.childrenDataSource = {
                cycleInsideDTOList: nowData,
                deliveryInfoInsideDTO: cycleInfo.analyseEdit
              }
            }
          })
          sessionStorage.setItem('changeCycleInfo', JSON.stringify(cycleInfo.baseAllData))
          sessionStorage.setItem('cycleStyle', JSON.stringify(cycleInfo.styleObj))
        }
      }

      this.selectIndex = index
    },

    // 获取任务详情
    getAccessDetail() {
      let obj = {}
      this.$loading()
      this.$API.infoChange
        .applyAdd(obj)
        .then((res) => {
          this.$hloading()
          this.headerInfo = res.data
          this.buyerInfoChangeHead.applyCode = res.data.applyCode
          this.buyerInfoChangeHead.remark = res.data.remark
          this.buyerInfoChangeHead.supportingMaterials = res.data.supportingMaterials
          if (Array.isArray(this.buyerInfoChangeHead?.supportingMaterials)) {
            this.buyerInfoChangeHead?.supportingMaterials.forEach((item) => {
              if (item.fileId) {
                item.id = item.fileId
              }
            })
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    }
  },
  destroyed() {
    sessionStorage.removeItem('changeBaseInfo')
    sessionStorage.removeItem('baseStyle')
    sessionStorage.removeItem('changeBusinessInfo')
    sessionStorage.removeItem('businessStyle')
    sessionStorage.removeItem('changeCertificateInfo')
    sessionStorage.removeItem('certifiStyle')
    sessionStorage.removeItem('changeEquipInfo')
    sessionStorage.removeItem('equipStyle')
    sessionStorage.removeItem('changeCycleInfo')
    sessionStorage.removeItem('cycleStyle')
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../../node_modules/@mtech/mtech-common-uploader/build/esm/bundle.css';
/deep/.bg-blue {
  background: #e8f0fe !important;
  td {
    color: red !important;
  }
}
/deep/.uploaderRed {
  color: red !important;
  .mt-common-uploader-main {
    .mt-input {
      input {
        color: red !important;
        width: 0 !important;
      }
    }
    .cell-upload {
      .cell-operable-title {
        color: red !important;
      }
    }
  }
}
/deep/.mt-common-uploader {
  .mt-common-uploader-main {
    .mt-input {
      input {
        width: 0 !important;
      }
    }
  }
}
/deep/ .mt-common-uploader {
  .main {
    margin: 0;
    min-height: 60px;
  }
}
.fbox {
  display: flex;
  align-items: stretch;
}

.flex1 {
  flex: 1;
}

.epls {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.lifeCycle-container {
  min-width: 1200px;
  overflow-x: scroll;
  background: white;

  .lifeCycle-top {
    width: 100%;
    height: 60px;
    background: rgba(99, 134, 193, 0.08);
    display: flex;
    justify-content: space-between;
    height: 138px;
    padding: 30px;
    .lifeCycle-left {
      display: flex;
      color: rgba(41, 41, 41, 1);
      .leftTitle {
        font-size: 14px;
        margin-top: 10px;
      }
      .leftCode {
        font-size: 18px;
        margin-top: 10px;
        font-weight: 600;
      }
      img {
        width: 80px;
        height: 80px;
      }
      i {
        font-size: 60px;
        color: #00469c;
      }
    }
    .btnClass {
      height: 30px;
      line-height: 30px;
      padding: 0 10px;
      font-size: 14px;
      font-weight: 600;
      color: rgba(0, 70, 156, 1);
      cursor: pointer;
    }
  }
  .header-status {
    padding: 30px 40px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .cp-name-line {
      white-space: nowrap;

      .cp-name {
        img {
          display: inline-block;
          width: 30px;
          height: 30px;
          margin-right: 10px;
          vertical-align: middle;
        }

        i {
          display: inline-block;
          width: 30px;
          height: 30px;
          font-size: 30px;
          margin-right: 10px;
          color: #00469c;
          vertical-align: middle;
        }

        span {
          display: inline-block;
          height: 30px;
          line-height: 30px;
          font-size: 24px;
          font-weight: 600;
          color: rgba(41, 41, 41, 1);
          vertical-align: middle;
        }
      }

      .cp-tips {
        margin-left: 40px;
        flex-wrap: wrap;

        .tip-item {
          height: 30px;
          line-height: 30px;
          padding: 0 10px;
          font-size: 16px;
          font-weight: 500;
          color: rgba(51, 166, 23, 1);
          background: rgba(51, 166, 23, 0.12);
          border-radius: 2px;
          margin-right: 10px;
          position: relative;
          user-select: none;
          max-width: 100px;
          margin-bottom: 10px;

          .txt-wrap {
            max-width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .close-btn {
            display: none;
          }

          &:hover .close-btn {
            display: block;
            width: 12px;
            height: 12px;
            font-size: 12px;
            position: absolute;
            right: -6px;
            top: -6px;
            color: #9baac1;
            line-height: 12px;
            cursor: pointer;
          }

          i.mt-icon-plus {
            line-height: 30px;
            font-size: 14px;
            cursor: pointer;
          }
        }

        .edit-tips {
          line-height: 30px;
          font-size: 12px;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
      }

      .cp-select {
        width: 410px;
        height: 50px;

        .right-btn {
          font-size: 14px;
          line-height: 40px;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
          font-weight: 600;
        }

        .right-btn:nth-child(2) {
          margin-left: 50px;
        }

        .right-btn:nth-child(3) {
          margin-left: 40px;
        }
      }
    }

    .rate-line {
      margin-top: -8px;

      .rate-item {
        margin-right: 30px;

        .rate-name {
          font-size: 12px;
          line-height: 12px;
          color: rgba(41, 41, 41, 1);
        }

        .rate-score {
          margin-left: 5px;
          width: 70px;
          height: 12px;
          position: relative;

          .bg-width {
            width: 100%;
            height: 12px;
            position: absolute;
            left: 0;
            top: 0;
            background: #00469c;
          }

          .yellow-bg {
            background: #eda133;
          }

          .star-bg {
            width: 14px;
            background: rgba(203, 203, 203, 0.4) url(../../../../assets/star.png) 0 0 no-repeat;
            background-size: 12px 12px;
            position: relative;
            z-index: 1;

            &::after {
              content: ' ';
              width: 2px;
              height: 12px;
              position: absolute;
              right: 0;
              top: 0;
              background: #fff;
            }
          }
        }

        .rate-num {
          margin-left: 5px;
          font-size: 12px;
          font-weight: bold;
          color: rgba(0, 70, 156, 1);
        }

        .yellow-num {
          color: #eda133;
        }
      }
    }

    .supplier-info {
      padding: 0 5%;
      height: 100px;
      margin-top: 20px;
      text-align: center;

      .info-item {
        position: relative;
        padding: 20px 0px;
        display: flex;
        justify-content: space-between;
        flex-direction: column;

        .mian-title {
          height: 24px;
          line-height: 24px;
          font-size: 24px;
          font-weight: 600;
          color: rgba(138, 204, 64, 1);
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          position: relative;

          span {
            font-size: 24px;
          }
        }

        .green {
          color: #8acc40;
        }

        .orange {
          color: #eda133;
        }

        .blue {
          color: #00469c;
        }

        .red {
          color: #ed5633;
        }

        .sub-title {
          height: 16px;
          font-size: 16px;
          font-weight: 600;
          color: rgba(41, 41, 41, 1);
        }

        .risk {
          justify-content: center;

          i {
            font-size: 18px;
            color: #4d5b6f;
            margin-left: 10px;
            cursor: pointer;
          }

          .pop-icon {
            position: relative;

            .pop-box {
              box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
              display: none;
              position: absolute;
              z-index: 9;
              width: 500px;
              height: 72px;
              background: rgba(255, 255, 255, 1);
              border-radius: 2px;
              padding: 14px 18px 10px;
              top: 16px;
              left: -250px;
              transform: translate3d(10px, 0, 0);

              .it-list {
                width: 30px;
                text-align: center;
                margin-right: 1px;

                .tp-txt {
                  height: 12px;
                  font-size: 12px;
                  color: rgba(41, 41, 41, 1);
                  margin-bottom: 4px;
                }

                .bt-color {
                  width: 30px;
                  height: 10px;
                  border-radius: 2px;
                }
              }

              .tip-lf {
                height: 12px;
                justify-content: space-between;
                font-size: 12px;
                color: rgba(41, 41, 41, 1);
                margin-top: 10px;
              }
            }
          }

          .pop-icon:hover {
            .pop-box {
              display: block;
            }
          }
        }

        .bt-tips {
          width: 100%;
          text-align: center;
          font-size: 12px;
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;

          span:nth-child(1) {
            color: #9a9a9a;
          }

          span:nth-child(2) {
            color: #00469c;
            display: inline-block;
            margin-left: 10px;
            cursor: pointer;
          }
        }

        &::after {
          content: ' ';
          display: inline-block;
          position: absolute;
          width: 1px;
          height: 60px;
          background: #dddddd;
          right: 0;
          top: 20px;
        }
      }

      .info-item:last-child {
        &::after {
          display: none;
        }
      }
    }
  }
}
</style>
