<template>
  <div class="full-height pt20 vertical-flex-box">
    <div class="flex-keep">
      <div class="header-box">
        <div class="main-bottom">
          <div class="toggle-tag" @click="isExpended = !isExpended">
            <span>{{ isExpended ? $t('收起') : $t('展开') }}</span>
            <i
              class="mt-icons mt-icon-icon_Sort_up"
              :style="isExpended ? '' : 'transform: rotate(180deg) scale(0.4)'"
            />
            <i
              class="mt-icons mt-icon-icon_Sort_up"
              :style="isExpended ? '' : 'transform: rotate(180deg) scale(0.4)'"
            />
          </div>
          <mt-form ref="ruleForm" :model="forecastTemplate">
            <mt-form-item prop="orgId" :label="$t('所属公司')">
              <mt-select
                v-model="forecastTemplate.orgId"
                :data-source="companyList"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择公司')"
                show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="templateCode" :label="$t('模板编号：')">
              <mt-input
                :show-clear-button="true"
                v-model="forecastTemplate.templateCode"
                type="text"
                :placeholder="$t('请输入模板编号')"
              >
              </mt-input>
            </mt-form-item>
            <mt-form-item prop="templateName" :label="$t('模板名称：')">
              <mt-input
                v-model="forecastTemplate.templateName"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入模板名称')"
              >
              </mt-input>
            </mt-form-item>
            <vxe-button
              class="e-flat1"
              status="info"
              icon="vxe-icon-search"
              size="mini"
              @click="queryEvent"
              >{{ $t('搜索') }}</vxe-button
            >
            <mt-form-item prop="templateStatus" :label="$t('模板状态:')" v-show="isExpended">
              <mt-select
                ref="indexRef"
                v-model="forecastTemplate.templateStatus"
                :data-source="indexList"
                :fields="{ text: 'text', value: 'value' }"
                :placeholder="$t('请选择状态')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              class="form-item"
              :label="$t('生效日期:')"
              label-style="top"
              prop="effectuateDate"
              v-show="isExpended"
            >
              <mt-date-picker
                v-model="forecastTemplate.effectiveDate"
                format="yyyy-MM-dd"
                :placeholder="$t('选择生效日期')"
              ></mt-date-picker>
            </mt-form-item>
          </mt-form>
          <div class="detail-content">
            <vxe-toolbar class="btn-tools">
              <template #buttons>
                <vxe-button
                  @click="handleAddEvent"
                  icon="vxe-icon-add"
                  size="mini"
                  status="primary"
                  >{{ $t('新建') }}</vxe-button
                >
                <vxe-button
                  @click="handleEffectuateEvent"
                  status="primary"
                  icon="vxe-icon-check"
                  size="mini"
                  >{{ $t('生效') }}</vxe-button
                >
                <vxe-button
                  @click="handleInvalidEvent"
                  status="primary"
                  icon="vxe-icon-close"
                  size="mini"
                  >{{ $t('失效') }}</vxe-button
                >
                <vxe-button
                  @click="handleDeleteEvent"
                  status="primary"
                  icon="vxe-icon-delete"
                  size="mini"
                  >{{ $t('删除') }}</vxe-button
                >
                <vxe-button
                  @click="handleCopyEvent"
                  status="primary"
                  icon="vxe-icon-copy"
                  size="mini"
                  >{{ $t('复制') }}</vxe-button
                >
              </template>
            </vxe-toolbar>
            <ScTable
              :loading="loading"
              :loading-config="{ icon: 'vxe-icon-indicator roll', text: '正在拼命加载中...' }"
              ref="xTable"
              max-height="100%"
              :row-config="{ height: 48 }"
              :columns="columns"
              :table-data="tableData"
              :tree-config="{}"
              header-align="center"
              align="center"
              @checkbox-change="selectChangeEvent"
              @checkbox-all="checkboxChangeEvent"
            >
            </ScTable>
            <vxe-pager
              background
              :current-page="pageConfig.current"
              :page-size="pageConfig.size"
              :total="totalNum"
              @page-change="handlePageChange"
              :layouts="[
                'Total',
                'PrevJump',
                'PrevPage',
                'JumpNumber',
                'NextPage',
                'NextJump',
                'FullJump',
                'Sizes'
              ]"
            >
            </vxe-pager>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import utils from '@/utils/utils'

export default {
  components: {
    ScTable
  },
  data() {
    return {
      pageConfig: {
        current: 1,
        size: 20
      },
      loading: false,
      totalNum: 0,
      forecastTemplate: {
        orgId: '',
        templateCode: '',
        templateName: '',
        templateStatus: '',
        effectiveDate: ''
      },
      isExpended: false,
      companyList: [],
      tableData: [],
      selectedRow: [],
      indexList: [
        {
          text: this.$t('新增'),
          value: '0'
        },
        {
          text: this.$t('生效'),
          value: '1'
        },
        {
          text: this.$t('失效'),
          value: '2'
        }
      ],
      columns: [
        {
          type: 'checkbox',
          width: 70
        },
        {
          field: 'templateCode',
          title: this.$t('模板编号'),
          width: 215,
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <vxe-button
                  status='primary'
                  type='text'
                  size='mini'
                  onClick={() => {
                    this.codeClickEvent(row)
                  }}>
                  {row.templateCode}
                </vxe-button>
              ]
            }
          }
        },
        {
          field: 'templateName',
          title: this.$t('模板名称')
        },
        {
          field: 'orgNames',
          title: this.$t('所属公司'),
          width: 250
        },
        {
          field: 'templateStatus',
          title: this.$t('模板状态'),
          width: 120,
          formatter: (column) => {
            if (column.cellValue == '1') {
              return this.$t('新增')
            } else if (column.cellValue == '2') {
              return this.$t('生效')
            } else {
              return this.$t('失效')
            }
          }
        },
        {
          field: 'effectiveDate',
          title: this.$t('生效日期'),
          width: 150
        },
        {
          field: 'expiryDate',
          title: this.$t('失效日期'),
          width: 150
        },
        {
          field: 'createUserName',
          title: this.$t('创建人'),
          width: 150
        },
        {
          field: 'option',
          title: this.$t('操作'),
          width: 150,
          slots: {
            default: ({ row }) => {
              return [
                <div style='width:100%;flex-direction: column; display: inline-flex;justify-content: space-between;'>
                  <div style='width:100%; display: flex;'>
                    <vxe-button
                      status='primary'
                      icon='vxe-icon-check'
                      type='text'
                      size='mini'
                      onClick={() => {
                        this.handleEffectuate(row)
                      }}>
                      生效
                    </vxe-button>
                    <vxe-button
                      status='primary'
                      icon='vxe-icon-close'
                      type='text'
                      size='mini'
                      onClick={() => {
                        this.handleInvalid(row)
                      }}>
                      失效
                    </vxe-button>
                  </div>
                </div>
              ]
            }
          }
        }
      ]
    }
  },
  watch: {},
  mounted() {
    this.getCompanyList()
    this.getTableList()
  },
  activated() {
    this.getTableList()
  },
  methods: {
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.pageConfig.current = currentPage
      this.pageConfig.size = pageSize
      this.getTableList()
    },
    // checkbox
    selectChangeEvent(row) {
      this.selectedRow = row.records
    },
    // 全选
    checkboxChangeEvent(row) {
      this.selectedRow = row.records
    },
    // 获取公司列表
    getCompanyList() {
      const params = {
        orgCode: 'KT01',
        treeType: 0
      }
      this.$API.PPAPConfig.getCompanyList(params).then((res) => {
        this.companyList = res.data
      })
    },
    //获取数据
    getTableList() {
      this.loading = true
      const params = {
        page: this.pageConfig,
        orgId: this.forecastTemplate.orgId ? this.forecastTemplate.orgId : '',
        templateCode: this.forecastTemplate.templateCode,
        templateName: this.forecastTemplate.templateName,
        templateStatus: this.forecastTemplate.templateStatus,
        effectiveDate: this.forecastTemplate.effectiveDate
          ? utils.formateTime(this.forecastTemplate.effectiveDate, 'yyyy-MM-dd')
          : ''
      }
      this.$API.PPAPConfig.queryInfo(params).then((res) => {
        this.tableData = res.data.records
        this.totalNum = Number(res.data.total)
        this.loading = false
      })
    },
    // 点击单据号
    codeClickEvent(row) {
      const id = row.id
      this.$router.push({
        path: '/supplier/pur/ppap-detail-template',
        query: { id: id }
      })
    },
    // 单行生效
    handleEffectuate(row) {
      if (row.templateStatus == 2) {
        this.$toast({
          content: this.$t('模板状态不能生效'),
          type: 'warning'
        })
        return
      }
      let params = {
        templateIds: [row.id],
        templateStatus: 2
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认生效数据？')
        },
        success: () => {
          this.$API.PPAPConfig.moudleOperation(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('生效成功'),
                type: 'success'
              })
              this.getTableList()
            }
          })
        }
      })
    },
    // 单行失效
    handleInvalid(row) {
      if (row.templateStatus == 0) {
        this.$toast({
          content: this.$t('模板状态不能失效'),
          type: 'warning'
        })
        return
      }
      let params = {
        templateIds: [row.id],
        templateStatus: 0
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认失效数据？')
        },
        success: () => {
          this.$API.PPAPConfig.moudleOperation(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('失效成功'),
                type: 'success'
              })
              this.getTableList()
            }
          })
        }
      })
    },
    // 新增按钮
    handleAddEvent() {
      this.$router.push({
        path: '/supplier/pur/ppap-detail-template'
      })
    },
    // 生效按钮
    handleEffectuateEvent() {
      if (this.selectedRow.length == 0) {
        this.$toast({
          content: this.$t('请先选择'),
          type: 'warning'
        })
        return
      }
      for (let item of this.selectedRow) {
        if (item.templateStatus == 2) {
          this.$toast({
            content: this.$t('模板状态不能生效'),
            type: 'warning'
          })
          return
        }
      }
      let templateIds = []
      this.selectedRow.forEach((item) => {
        templateIds.push(item.id)
      })
      let params = {
        templateIds: templateIds,
        templateStatus: 2
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认生效数据？')
        },
        success: () => {
          this.$API.PPAPConfig.moudleOperation(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('生效成功'),
                type: 'success'
              })
              this.getTableList()
            }
          })
        }
      })
    },
    // 失效按钮
    handleInvalidEvent() {
      if (this.selectedRow.length == 0) {
        this.$toast({
          content: this.$t('请先选择'),
          type: 'warning'
        })
        return
      }
      for (let item of this.selectedRow) {
        if (item.templateStatus == 0) {
          this.$toast({
            content: this.$t('模板状态不能失效'),
            type: 'warning'
          })
          return
        }
      }
      let templateIds = []
      this.selectedRow.forEach((item) => {
        templateIds.push(item.id)
      })
      let params = {
        templateIds: templateIds,
        templateStatus: 0
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认失效数据？')
        },
        success: () => {
          this.$API.PPAPConfig.moudleOperation(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('失效成功'),
                type: 'success'
              })
              this.getTableList()
            }
          })
        }
      })
    },
    // 删除按钮
    handleDeleteEvent() {
      if (this.selectedRow.length == 0) {
        this.$toast({
          content: this.$t('请先选择'),
          type: 'warning'
        })
        return
      }
      for (let item of this.selectedRow) {
        if (item.templateStatus == 2) {
          this.$toast({
            content: this.$t('存在不能删除的单据'),
            type: 'warning'
          })
          return
        }
      }
      let templateIds = []
      this.selectedRow.forEach((item) => {
        if (item.isQuote == 0) {
          templateIds.push(item.id)
        }
      })
      if (this.selectedRow.length != templateIds.length) {
        this.$toast({
          content: this.$t('存在不能删除的单据'),
          type: 'warning'
        })
        return
      }
      let params = {
        templateIds: templateIds
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.PPAPConfig.moudleDelete(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.getTableList()
            }
          })
        }
      })
    },
    // 复制
    handleCopyEvent() {
      if (this.selectedRow.length !== 1) {
        this.$toast({
          content: this.$t('请先选择一行'),
          type: 'warning'
        })
        return
      }
      let templateIds = []
      this.selectedRow.forEach((item) => {
        templateIds.push(item.id)
      })
      let params = {
        templateIds: templateIds,
        templateStatus: 1
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认复制数据？')
        },
        success: () => {
          this.$API.PPAPConfig.moudleCopy(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('复制成功'),
                type: 'success'
              })
              this.getTableList()
            }
          })
        }
      })
    },
    // 查询
    queryEvent() {
      this.getTableList()
    }
  }
}
</script>
<style lang="scss" scoped>
.toggle-tag {
  padding: 0 15px 8px 0;
  color: #2783fe;
  display: inline-block;
  font-size: 14px;
  position: relative;
  cursor: pointer;
  user-select: none;
  .mt-icons {
    font-size: 12px;
    position: absolute;
    transform: scale(0.4);
    top: -2px;
    left: 26px;
    &:nth-child(2) {
      top: 2px;
    }
  }
}
/deep/ .vxe-header--column {
  .vxe-resizable.is--line:before {
    width: 0px;
  }
}
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
.full-height .flex-keep {
  display: flex;
}
.main-bottom {
  padding-top: 10px;
  width: 100%;
  /deep/ .mt-form-item {
    width: calc(30% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
    margin-left: 20px;

    // &.more-width {
    //   // width: 450px;
    // }

    .full-width {
      width: calc(100% - 20px) !important;
    }
  }
  /deep/ .e-flat {
    position: relative;
    top: 21px;
  }
  /deep/ .btn-tools {
    padding: 8px 0 0 8px;
  }
}
.header-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  /deep/ .e-flat1 {
    position: absolute;
    top: 43px;
    right: 1%px;
  }
  .sort-box {
    position: relative;
    cursor: pointer;
    margin-right: 20px;
    .mt-icons {
      font-size: 12px;
      transform: scale(0.5);
      color: rgba(0, 70, 156, 1);
      margin-top: -10px;
      position: absolute;
      top: 0;

      &:nth-child(2) {
        top: 6px;
      }
    }
  }
}
</style>
