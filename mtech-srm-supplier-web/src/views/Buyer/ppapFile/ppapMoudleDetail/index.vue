<template>
  <div class="full-height pt20 vertical-flex-box">
    <div class="flex-keep">
      <div class="header-box">
        <div class="main-bottom">
          <div style="position: relative; float: right">
            <vxe-toolbar>
              <template #buttons>
                <vxe-button
                  @click="backTo"
                  status="primary"
                  icon="vxe-icon-arrow-left"
                  size="mini"
                  >{{ $t('返回') }}</vxe-button
                >
                <vxe-button
                  @click="saveEvent"
                  status="primary"
                  icon="vxe-icon-save"
                  size="mini"
                  :disabled="forecastTemplate.templateStatus == 2"
                  >{{ $t('保存') }}</vxe-button
                >
                <vxe-button
                  @click="saveSubmitEvent"
                  status="primary"
                  icon="vxe-icon-send"
                  size="mini"
                  :disabled="forecastTemplate.templateStatus == 2"
                  >{{ $t('保存并提交') }}</vxe-button
                >
              </template>
            </vxe-toolbar>
          </div>
          <mt-form ref="ruleForm" :model="forecastTemplate" :rules="rules">
            <mt-form-item prop="templateCode" :label="$t('模板编号：')" v-show="editShow">
              <mt-input
                v-model="forecastTemplate.templateCode"
                type="text"
                :disabled="true"
                :placeholder="$t('请输入模板编号')"
              >
              </mt-input>
            </mt-form-item>
            <mt-form-item prop="templateStatus" :label="$t('模板状态')" v-show="editShow">
              <mt-select
                ref="indexRef"
                v-model="forecastTemplate.templateStatus"
                :disabled="true"
                :data-source="indexList"
                :fields="{ text: 'text', value: 'value' }"
                :placeholder="$t('请选择状态')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="templateName" :label="$t('模板名称：')">
              <mt-input
                v-model="forecastTemplate.templateName"
                :disabled="forecastTemplate.templateStatus == 2"
                type="text"
                maxlength="30"
                :placeholder="$t('请输入模板名称，限制30字符')"
              >
              </mt-input>
            </mt-form-item>
            <mt-form-item
              class="applyOrganization"
              prop="applyOrganization"
              :label="$t('适用组织')"
            >
              <mt-input
                v-model="forecastTemplate.applyOrganization"
                float-label-type="Never"
                :disabled="true"
                :placeholder="$t('选择组织')"
              ></mt-input>
              <div class="hover" v-if="forecastTemplate.applyOrganization">
                <span
                  style="margin: 0 5px; padding: 2px 0; box-sizing: border-box"
                  v-for="(item, index) in forecastTemplate.rowOrganization"
                  :key="item.id"
                  >{{ index + 1 }}.{{ $t(item.orgName) }}</span
                >
              </div>
              <mt-button @click="clickApply" :disabled="editShow">{{ $t('选择组织') }}</mt-button>
            </mt-form-item>
          </mt-form>
          <div class="detail-content">
            <vxe-toolbar>
              <template #buttons>
                <vxe-button
                  @click="handleAddEvent"
                  status="primary"
                  icon="vxe-icon-add"
                  size="mini"
                  :disabled="forecastTemplate.templateStatus == 2"
                  >{{ $t('新增') }}</vxe-button
                >
                <vxe-button
                  @click="handleDeleteEvent"
                  status="primary"
                  icon="vxe-icon-delete"
                  size="mini"
                  :disabled="forecastTemplate.templateStatus == 2"
                  >{{ $t('删除') }}</vxe-button
                >
              </template>
            </vxe-toolbar>
            <ScTable
              :loading="loading"
              :loading-config="{ icon: 'vxe-icon-indicator roll', text: '正在拼命加载中...' }"
              ref="xTable"
              max-height="100%"
              :tree-config="{}"
              :row-config="{ height: 48 }"
              :columns="columns"
              :table-data="tableData"
              header-align="center"
              align="center"
              @checkbox-change="selectChangeEvent"
              @checkbox-all="checkboxChangeEvent"
            >
            </ScTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main'
import ScTable from '@/components/ScTable/src/index'
import { download } from '@/utils/utils'

export default {
  components: {
    ScTable
  },
  data() {
    return {
      pageConfig: {
        current: 1,
        size: 20
      },
      loading: false,
      editShow: false,
      totalNum: 0,
      selectedRow: [],
      applyOrganization: '', //适用组织
      rowOrganization: [], //组织arr
      organization: [],
      deleteData: [],
      forecastTemplate: {
        templateName: '',
        templateStatus: '',
        templateCode: '',
        rowOrganization: []
      },
      formData: {},
      tableData: [],
      indexList: [
        {
          text: this.$t('新建'),
          value: 1
        },
        {
          text: this.$t('生效'),
          value: 2
        },
        {
          text: this.$t('失效'),
          value: 0
        }
      ],
      columns: [
        {
          type: 'checkbox',
          width: 80
        },
        {
          type: 'seq',
          title: i18n.t('序号'),
          width: 80
        },
        {
          field: 'subTemplateName',
          title: i18n.t('子模板'),
          width: 1000,
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <vxe-button
                  status='primary'
                  type='text'
                  size='mini'
                  onClick={() => {
                    this.handleDownloadFile(row)
                  }}>
                  {row.subTemplateName}
                </vxe-button>
              ]
            }
          }
        },
        {
          field: 'description',
          title: i18n.t('描述'),
          width: 120
        },
        {
          field: 'option',
          title: i18n.t('操作'),
          width: 110,
          slots: {
            default: ({ row }) => {
              return [
                <div style='width:100%;flex-direction: column; display: inline-flex;justify-content: space-between;'>
                  <div style='width:100%; display: flex;justify-content: center;'>
                    <vxe-switch
                      v-model={row.subTemplateStatus}
                      open-label={i18n.t('启用')}
                      open-value={1}
                      close-label={i18n.t('禁用')}
                      close-value={2}></vxe-switch>
                  </div>
                </div>
              ]
            }
          }
        },
        {
          field: 'uploadType',
          title: i18n.t('是否必须上传附件'),
          slots: {
            default: ({ row }) => {
              return [
                <div style='width:100%; display: inline-flex;justify-content: center;'>
                  <div style='width:100%; display: flex;justify-content: center;'>
                    <vxe-checkbox v-model={row.uploadType}></vxe-checkbox>
                  </div>
                </div>
              ]
            }
          }
        }
      ],
      rules: {
        applyOrganization: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        templateName: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      }
    }
  },

  mounted() {
    if (this.$route.query.id) {
      this.editShow = true
      this.getFormDetail()
    }
    this.getCompanyList()
  },
  methods: {
    // 获取公司列表
    getCompanyList() {
      const params = {
        orgCode: 'KT01',
        treeType: 1
      }
      this.$API.PPAPConfig.getCompanyList(params).then((res) => {
        this.organization = res.data
      })
    },
    //选择组织
    clickApply() {
      this.$dialog({
        modal: () => import('./components/organization.vue'),
        data: {
          title: this.$t('选择组织'),
          organization: this.organization
        },
        success: (val) => {
          let text = '',
            _val = []
          val.map((item) => {
            if (item.orgType == 'company') {
              text += `${item.orgName},`
              _val.push(item)
            }
          })
          this.forecastTemplate.applyOrganization = text
          this.forecastTemplate.rowOrganization = _val
          this.$forceUpdate()
          this.$emit('clickTopInfoApply', _val)
        }
      })
    },
    // 下载文件
    handleDownloadFile(data) {
      this.$API.fileService
        .downloadPublicFile({
          id: data.subTemplateId
        })
        .then((res) => {
          download({
            fileName: data.subTemplateName,
            blob: res.data
          })
        })
    },
    getFormDetail() {
      this.loading = true
      const params = {
        templateId: this.$route.query.id
      }
      this.$API.PPAPConfig.SonMoudleQuery(params).then((res) => {
        let modelData = res.data.detailsResponseList
        modelData.forEach((item) => {
          if (item.uploadType == 1) {
            item.uploadType = true
          } else if (item.uploadType == 0) {
            item.uploadType = false
          }
        })
        this.tableData = modelData
        this.forecastTemplate.templateName = res.data.templateName
        this.forecastTemplate.templateStatus = res.data.templateStatus
        this.forecastTemplate.templateCode = res.data.templateCode
        let text = '',
          _val = []
        res.data.organizationRelList.map((item) => {
          text += `${item.orgName},`
          _val.push(item)
        })
        this.forecastTemplate.applyOrganization = text
        this.forecastTemplate.rowOrganization = _val
        this.loading = false
      })
    },
    // 选择行
    selectChangeEvent(row) {
      this.selectedRow = row.records
    },
    // 全选
    checkboxChangeEvent(row) {
      this.selectedRow = row.records
    },
    // 新增附件
    handleAddEvent() {
      this.$dialog({
        modal: () => import('./components/uploadDialog.vue'),
        data: {
          fileData: [],
          isView: false, // 是否为预览
          required: false, // 是否必须
          title: this.$t('新增子模板')
        },
        success: (res) => {
          this.handleUploadFiles(res)
        }
      })
    },
    // 删除
    handleDeleteEvent() {
      if (this.selectedRow.length == 0) {
        this.$toast({
          content: this.$t('请先选择'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          let database = this.tableData
          database.map((item, index) => {
            this.selectedRow.forEach((items) => {
              if (item.id === items.id) {
                item.isDelete = 1
                this.deleteData.push(database.splice(index, 1))
              } else {
                item.isDelete = 0
              }
            })
          })
          this.tableData = database
        }
      })
    },
    // 上传文件
    handleUploadFiles(data) {
      let templetData = {
        subTemplateId: data.id,
        subTemplateName: data.fileName,
        attachmentSize: data.fileSize,
        subTemplateUrl: data.url,
        description: data.remark,
        subTemplateStatus: 1,
        uploadType: true
      }
      this.tableData.push(templetData)
    },
    // 返回
    backTo() {
      this.$router.push({
        path: '/supplier/pur/ppap-template'
      })
    },
    // 保存
    saveEvent() {
      if (!this.forecastTemplate.applyOrganization) {
        this.$toast({
          content: this.$t('请选择组织'),
          type: 'warning'
        })
        return
      }
      if (!this.forecastTemplate.templateName) {
        this.$toast({
          content: this.$t('请输入模板名称'),
          type: 'warning'
        })
        return
      }
      let orgList = []
      if (this.forecastTemplate.rowOrganization) {
        for (let i = 0; i < this.forecastTemplate.rowOrganization.length; i++) {
          let temp = {}
          temp.orgCode = this.forecastTemplate.rowOrganization[i].orgCode
          temp.orgId = this.forecastTemplate.rowOrganization[i].id
            ? this.forecastTemplate.rowOrganization[i].id
            : this.forecastTemplate.rowOrganization[i].orgId
          temp.orgName = this.forecastTemplate.rowOrganization[i].orgName
          orgList.push(temp)
        }
      }
      let detailsRequestList = this.tableData
      detailsRequestList.concat(this.deleteData)
      detailsRequestList.forEach((item) => {
        if (!item.isDelete) {
          item.isDelete = 0
        }
        if (item.uploadType == true) {
          item.uploadType = 1
        } else if (item.uploadType == false) {
          item.uploadType = 0
        }
        if (item.id && item.id.search('row') != -1) {
          delete item.id
        }
      })
      let params = {
        templateId: this.$route.query.id ? this.$route.query.id : '',
        detailsRequestList: detailsRequestList,
        orgRequestList: orgList,
        templateName: this.forecastTemplate.templateName,
        templateCode: this.forecastTemplate.templateCode,
        templateStatus: this.forecastTemplate.templateStatus
      }
      this.$API.PPAPConfig.saveSonMoudle(params).then(() => {
        this.$toast({
          content: this.$t('保存成功'),
          type: 'success'
        })
        this.$router.push({
          path: '/supplier/pur/ppap-template'
        })
      })
    },
    // 保存并提交
    saveSubmitEvent() {
      if (!this.forecastTemplate.applyOrganization) {
        this.$toast({
          content: this.$t('请选择组织'),
          type: 'warning'
        })
        return
      }
      if (!this.forecastTemplate.templateName) {
        this.$toast({
          content: this.$t('请输入模板名称'),
          type: 'warning'
        })
        return
      }
      let orgList = []
      if (this.forecastTemplate.rowOrganization) {
        for (let i = 0; i < this.forecastTemplate.rowOrganization.length; i++) {
          let temp = {}
          temp.orgCode = this.forecastTemplate.rowOrganization[i].orgCode
          temp.orgId = this.forecastTemplate.rowOrganization[i].id
          temp.orgName = this.forecastTemplate.rowOrganization[i].orgName
          orgList.push(temp)
        }
      }
      let detailsRequestList = this.tableData
      detailsRequestList.concat(this.deleteData)
      detailsRequestList.forEach((item) => {
        if (!item.isDelete) {
          item.isDelete = 0
        }
        if (item.uploadType == true) {
          item.uploadType = 1
        } else if (item.uploadType == false) {
          item.uploadType = 0
        }
        if (item.id && item.id.search('row') != -1) {
          delete item.id
        }
      })
      let params = {
        templateId: this.$route.query.id ? this.$route.query.id : '',
        detailsRequestList: detailsRequestList,
        orgRequestList: orgList,
        templateName: this.forecastTemplate.templateName,
        isEffect: 1
      }
      this.$API.PPAPConfig.saveSonMoudle(params).then(() => {
        this.$toast({
          content: this.$t('保存并提交成功'),
          type: 'success'
        })
        this.$router.push({
          path: '/supplier/pur/ppap-template'
        })
      })
    }
  },
  watch: {}
}
</script>
<style lang="scss" scoped>
/deep/ .e-ddl.e-input-group.e-control-wrapper {
  .e-ddl-icon::before {
    content: '\e969';
    font-size: 16px;
    margin-right: 10px;
  }
}
/deep/ .ant-select-selection {
  background: #fafafa !important;
}
/deep/ .vxe-header--column {
  .vxe-resizable.is--line:before {
    width: 0px;
  }
}
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
.full-height .flex-keep {
  display: flex;
}
.main-bottom {
  padding-top: 10px;
  width: 100%;
  /deep/ .mt-form-item {
    width: calc(30% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;

    // &.more-width {
    //   // width: 450px;
    // }
    .full-width {
      width: calc(100% - 20px) !important;
    }
  }
  /deep/ .e-flat {
    position: relative;
    top: 21px;
  }
  .detail-content {
    .vxe-toolbar {
      padding: 8px 0 0 8px;
    }
  }
}
.header-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/deep/ .applyOrganization {
  background: red($color: #000000);
  .mt-input:hover + .hover {
    display: block;
  }
}
.hover {
  display: none;
  position: absolute;
  top: 80px;
  left: 0;
  z-index: 1;
  width: 400px;
  height: 75px;
  background: #fff;
  border: 1px solid #fee;
  border-radius: 5px;
}
</style>
