<template>
  <div class="full-height pt20 vertical-flex-box">
    <div class="flex-keep">
      <div class="header-box">
        <div class="main-bottom">
          <div class="toggle-tag" @click="isExpended = !isExpended">
            <span>{{ isExpended ? $t('收起') : $t('展开') }}</span>
            <i
              class="mt-icons mt-icon-icon_Sort_up"
              :style="isExpended ? '' : 'transform: rotate(180deg) scale(0.4)'"
            />
            <i
              class="mt-icons mt-icon-icon_Sort_up"
              :style="isExpended ? '' : 'transform: rotate(180deg) scale(0.4)'"
            />
          </div>
          <mt-form ref="ruleForm" :model="forecastTemplate">
            <mt-form-item prop="billCode" :label="$t('PPAP单号:')">
              <mt-input
                v-model="forecastTemplate.billCode"
                type="text"
                :placeholder="$t('请输入PPAP单号')"
                :show-clear-button="true"
              >
              </mt-input>
            </mt-form-item>
            <mt-form-item prop="billType" :label="$t('单据类型:')">
              <mt-select
                ref="indexRef"
                v-model="forecastTemplate.billType"
                :data-source="typeList"
                :fields="{ text: 'text', value: 'value' }"
                :placeholder="$t('请选择单据类型')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="orgId" :label="$t('所属公司:')">
              <mt-select
                v-model="forecastTemplate.orgId"
                :data-source="companyList"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择所属公司')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <vxe-button
              class="e-flat1"
              status="info"
              icon="vxe-icon-search"
              size="mini"
              @click="submitEvent"
              >{{ $t('搜索') }}
            </vxe-button>
            <mt-form-item prop="supplierName" :label="$t('供应商名称:')" v-show="isExpended">
              <mt-input
                v-model="forecastTemplate.supplierName"
                type="text"
                :placeholder="$t('请输入供应商名称')"
                :show-clear-button="true"
              >
              </mt-input>
            </mt-form-item>
            <mt-form-item prop="categoryName" :label="$t('品类:')" v-show="isExpended">
              <mt-input
                v-model="forecastTemplate.categoryName"
                type="text"
                :placeholder="$t('请输入品类')"
                :show-clear-button="true"
              >
              </mt-input>
            </mt-form-item>
            <mt-form-item prop="billStatus" :label="$t('单据状态:')" v-show="isExpended">
              <mt-select
                ref="indexRef"
                v-model="forecastTemplate.billStatus"
                :data-source="billList"
                :fields="{ text: 'text', value: 'value' }"
                :placeholder="$t('请选择单据状态')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              v-show="isExpended"
              class="form-item"
              :label="$t('创建日期:')"
              label-style="top"
              prop="createDate"
            >
              <mt-date-picker
                v-model="forecastTemplate.createDate"
                :placeholder="$t('选择创建日期')"
                :show-clear-button="true"
              ></mt-date-picker>
            </mt-form-item>
          </mt-form>
          <div class="detail-content">
            <vxe-toolbar>
              <template #buttons>
                <vxe-button
                  @click="handleAddEvent"
                  status="primary"
                  icon="vxe-icon-add"
                  size="mini"
                  >{{ $t('新增') }}</vxe-button
                >
                <vxe-button
                  @click="handleEditEvent"
                  status="primary"
                  icon="vxe-icon-edit"
                  size="mini"
                  >{{ $t('编辑') }}</vxe-button
                >
                <vxe-button
                  @click="handleSubmitEvent"
                  status="primary"
                  icon="vxe-icon-file-txt"
                  size="mini"
                  >{{ $t('提交') }}</vxe-button
                >
                <vxe-button
                  @click="handleDeleteEvent"
                  status="primary"
                  icon="vxe-icon-delete"
                  size="mini"
                  >{{ $t('删除') }}</vxe-button
                >
                <vxe-button
                  @click="handleSeeOaEvent"
                  status="primary"
                  icon="vxe-icon-delete"
                  size="mini"
                  >{{ $t('查看OA审批') }}</vxe-button
                >
              </template>
            </vxe-toolbar>
            <ScTable
              :loading="loading"
              :loading-config="{ icon: 'vxe-icon-indicator roll', text: '正在拼命加载中...' }"
              ref="xTable"
              max-height="100%"
              :row-config="{ height: 38 }"
              :columns="columns"
              :tree-config="{}"
              :table-data="tableData"
              header-align="center"
              align="center"
              @checkbox-change="selectChangeEvent"
              @checkbox-all="checkboxChangeEvent"
            >
            </ScTable>
            <vxe-pager
              background
              :current-page.sync="pageConfig.current"
              :page-size.sync="pageConfig.size"
              :total="totalNum"
              @page-change="handlePageChange"
              :layouts="[
                'Total',
                'PrevJump',
                'PrevPage',
                'JumpNumber',
                'NextPage',
                'NextJump',
                'FullJump',
                'Sizes'
              ]"
            >
            </vxe-pager>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import utils from '@/utils/utils'

export default {
  components: {
    ScTable
  },
  data() {
    return {
      pageConfig: {
        current: 1,
        size: 20
      },
      loading: false,
      totalNum: 0,
      isExpended: false,
      selectedRow: [],
      forecastTemplate: {
        billCode: '',
        billStatus: null,
        billType: null,
        categoryName: '',
        createDate: '',
        orgId: '',
        supplierName: ''
      },
      companyList: [],
      formData: {},
      tableData: [],
      billList: [
        {
          text: this.$t('新建'),
          value: 10
        },
        {
          text: this.$t('待反馈'),
          value: 20
        },
        {
          text: this.$t('待审批'),
          value: 30
        },
        {
          text: this.$t('已驳回'),
          value: 40
        },
        {
          text: this.$t('已通过'),
          value: 50
        },
        {
          text: this.$t('已废弃'),
          value: 60
        }
      ],
      typeList: [
        {
          text: this.$t('全新供应商'),
          value: 1
        },
        {
          text: this.$t('三新物料'),
          value: 2
        }
      ],
      columns: [
        {
          type: 'checkbox',
          width: 70
        },
        {
          field: 'billCode',
          width: 210,
          title: this.$t('PPAP单号'),
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <vxe-button
                  status='primary'
                  type='text'
                  size='mini'
                  onClick={() => {
                    this.codeClickEvent(row)
                  }}>
                  {row.billCode}
                </vxe-button>
              ]
            }
          }
        },
        {
          field: 'templateName',
          title: this.$t('PPAP模板')
        },
        {
          field: 'billType',
          title: this.$t('单据类型'),
          width: 200,
          formatter: (data) => {
            if (data.row.billType == 1) {
              return this.$t('全新供应商')
            } else if (data.row.billType == 2) {
              return this.$t('三新物料')
            }
          }
        },
        {
          field: 'orgName',
          title: this.$t('公司'),
          width: 260
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          width: 150
        },
        {
          field: 'categoryName',
          title: this.$t('品类'),
          width: 150
        },
        {
          field: 'createDate',
          title: this.$t('建立日期'),
          width: 150
        },
        {
          field: 'createUserName',
          title: this.$t('创建人'),
          width: 150
        },
        {
          field: 'billStatus',
          title: this.$t('单据状态'),
          width: 150,
          formatter: (data) => {
            if (data.row.billStatus == 10) {
              return this.$t('新建')
            } else if (data.row.billStatus == 20) {
              return this.$t('待反馈')
            } else if (data.row.billStatus == 30) {
              return this.$t('待审批')
            } else if (data.row.billStatus == 40) {
              return this.$t('已驳回')
            } else if (data.row.billStatus == 50) {
              return this.$t('已通过')
            } else {
              return this.$t('已废弃')
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.getCompanyList()
    this.getQueryList()
  },
  activated() {
    this.getQueryList()
  },
  methods: {
    // 查看OA审批
    handleSeeOaEvent() {
      if (this.selectedRow.length !== 1) {
        this.$toast({
          content: this.$t('请先选择一行数据'),
          type: 'warning'
        })
        return
      }
      if (this.selectedRow[0].billStatus == 10 || this.selectedRow[0].billStatus == 20) {
        this.$toast({
          content: this.$t('单据未提交'),
          type: 'warning'
        })
        return
      }
      let params = {
        applyId: this.selectedRow[0].id,
        businessType: 'ppapBill'
      }
      this.$API.PPAPConfig.getBillOA(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.pageConfig.current = currentPage
      this.pageConfig.size = pageSize
      this.getQueryList()
    },
    // 获取公司列表
    getCompanyList() {
      const params = {
        orgCode: 'KT01',
        treeType: 0
      }
      this.$API.PPAPConfig.getCompanyList(params).then((res) => {
        this.companyList = res.data
      })
    },
    selectChangeEvent(row) {
      this.selectedRow = row.records
    },
    // 全选
    checkboxChangeEvent(row) {
      this.selectedRow = row.records
    },
    // 点击单据号
    codeClickEvent(row) {
      let query = {
        id: row.id
      }
      this.$router.push({
        path: '/supplier/pur/ppap-doc-manage-detail',
        query: query
      })
    },
    getQueryList() {
      this.loading = true
      const params = {
        page: this.pageConfig,
        billCode: this.forecastTemplate.billCode,
        billStatus: this.forecastTemplate.billStatus,
        billType: this.forecastTemplate.billType,
        companyCode: this.forecastTemplate.companyCode,
        categoryName: this.forecastTemplate.categoryName,
        createDate: this.forecastTemplate.createDate
          ? utils.formateTime(this.forecastTemplate.createDate, 'yyyy-MM-dd')
          : '',
        orgId: this.forecastTemplate.orgId,
        supplierName: this.forecastTemplate.supplierName
      }
      this.$API.PPAPConfig.billInfoQuery(params).then((res) => {
        this.totalNum = Number(res.data.total)
        this.tableData = res.data.records
        this.loading = false
      })
    },
    // 新增
    handleAddEvent() {
      this.$router.push({
        path: '/supplier/pur/ppap-doc-manage-detail'
      })
    },
    // 编辑
    handleEditEvent() {
      if (this.selectedRow.length !== 1) {
        this.$toast({
          content: this.$t('请先选择一行数据'),
          type: 'warning'
        })
        return
      }
      if (this.selectedRow[0].billStatus !== 10) {
        this.$toast({
          content: this.$t('单据状态为新建才可编辑'),
          type: 'warning'
        })
        return
      }
      this.$router.push({
        path: '/supplier/pur/ppap-doc-manage-detail',
        query: {
          id: this.selectedRow[0].id
        }
      })
    },
    // 提交
    handleSubmitEvent() {
      if (this.selectedRow.length == 0) {
        this.$toast({
          content: this.$t('请先选择数据'),
          type: 'warning'
        })
        return
      }
      let billIds = []
      for (let item of this.selectedRow) {
        if (item.billStatus !== 10) {
          this.$toast({
            content: this.$t('单据状态为新建才可提交'),
            type: 'warning'
          })
          return
        }
      }
      this.selectedRow.forEach((item) => {
        billIds.push(item.id)
      })

      let params = {
        billIds: billIds
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交数据？')
        },
        success: () => {
          this.loading = true
          this.$API.PPAPConfig.billInfoSubmit(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('提交成功'),
                type: 'success'
              })
              this.getQueryList()
              this.loading = false
            }
          })
        }
      })
    },
    // 删除
    handleDeleteEvent() {
      if (this.selectedRow.length == 0) {
        this.$toast({
          content: this.$t('请先选择数据'),
          type: 'warning'
        })
        return
      }
      let billIds = []
      this.selectedRow.forEach((item) => {
        billIds.push(item.id)
      })
      let params = {
        billIds: billIds
      }

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.PPAPConfig.billDelete(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.getQueryList()
              this.loading = false
            }
          })
        }
      })
    },
    // 查询
    submitEvent() {
      this.getQueryList()
    }
  },
  watch: {}
}
</script>
<style lang="scss" scoped>
.toggle-tag {
  padding: 0 15px 8px 0;
  color: #2783fe;
  display: inline-block;
  font-size: 14px;
  position: relative;
  cursor: pointer;
  user-select: none;
  .mt-icons {
    font-size: 12px;
    position: absolute;
    transform: scale(0.4);
    top: -2px;
    left: 26px;
    &:nth-child(2) {
      top: 2px;
    }
  }
}
/deep/ .vxe-header--column {
  .vxe-resizable.is--line:before {
    width: 0px;
  }
}
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
.full-height .flex-keep {
  display: flex;
}
.main-bottom {
  padding-top: 10px;
  width: 100%;
  /deep/ .mt-form-item {
    width: calc(30% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
    margin-left: 20px;

    // &.more-width {
    //   // width: 450px;
    // }

    .full-width {
      width: calc(100% - 20px) !important;
    }
  }
  /deep/ .e-flat {
    position: relative;
    top: 21px;
  }
  /deep/ .e-flat1 {
    position: absolute;
    top: 43px;
    right: 1%;
  }
  /deep/ .btn-tools {
    padding: 8px 0 0 8px;
  }
  .detail-content {
    .vxe-toolbar {
      padding: 8px 0 0 8px;
    }
  }
}
.header-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .itemcon {
    display: flex;
    align-items: center;
  }
  .item {
    margin-right: 20px;
  }
  .middle-blank {
    flex: 1;
  }
  .status {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(99, 134, 193, 1);
    padding: 4px;
    background: rgba(238, 242, 249, 1);
    border-radius: 2px;
  }

  .infos {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
  }

  .sort-box {
    position: relative;
    cursor: pointer;
    margin-right: 20px;
    .mt-icons {
      font-size: 12px;
      transform: scale(0.5);
      color: rgba(0, 70, 156, 1);
      margin-top: -10px;
      position: absolute;
      top: 0;

      &:nth-child(2) {
        top: 6px;
      }
    }
  }
}
</style>
