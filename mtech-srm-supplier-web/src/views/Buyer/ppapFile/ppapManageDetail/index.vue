<template>
  <div class="full-height pt20 vertical-flex-box">
    <div class="flex-keep">
      <div class="header-box">
        <div class="main-bottom">
          <div style="position: relative; float: right">
            <vxe-toolbar>
              <template #buttons>
                <vxe-button
                  @click="backTo"
                  status="primary"
                  icon="vxe-icon-arrow-left"
                  size="mini"
                  >{{ $t('返回') }}</vxe-button
                >
                <vxe-button
                  @click="submitEvent"
                  status="primary"
                  icon="vxe-icon-save"
                  size="mini"
                  :disabled="forecastTemplate.billStatus !== 10 && editShow"
                  >{{ $t('保存') }}</vxe-button
                >
                <vxe-button
                  @click="saveAndSubmit"
                  status="primary"
                  icon="vxe-icon-send"
                  size="mini"
                  :disabled="forecastTemplate.billStatus !== 10 && editShow"
                  >{{ $t('保存并提交') }}</vxe-button
                >
              </template>
            </vxe-toolbar>
          </div>
          <mt-form ref="ruleForm" :model="forecastTemplate" :rules="rules">
            <mt-form-item prop="billCode" :label="$t('PPAP单号：')" v-show="editShow">
              <mt-input :disabled="true" v-model="forecastTemplate.billCode" type="text">
              </mt-input>
            </mt-form-item>
            <mt-form-item prop="billType" :label="$t('单据类型')">
              <mt-select
                v-model="forecastTemplate.billType"
                :data-source="typeList"
                :fields="{ text: 'text', value: 'value' }"
                :placeholder="$t('请选择单据类型')"
                :disabled="forecastTemplate.billStatus !== 10 && editShow"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="templateId" :label="$t('PPAP模板')">
              <mt-select
                v-model="forecastTemplate.templateId"
                :data-source="moudleList"
                :fields="{ text: 'templateName', value: 'id' }"
                :placeholder="$t('请选择模板')"
                :show-clear-button="true"
                :disabled="editShow"
                :open-dispatch-change="false"
                @change="templateSelectChange"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="orgCode" :label="$t('公司名称')">
              <mt-select
                v-model="forecastTemplate.orgCode"
                :data-source="companyList"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                :placeholder="$t('请选择公司')"
                :allow-filtering="true"
                filter-type="Contains"
                :open-dispatch-change="false"
                :disabled="forecastTemplate.billStatus !== 10 && editShow"
                :show-clear-button="true"
                @change="companySelectChange"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商名称')">
              <mt-select
                v-model="forecastTemplate.supplierCode"
                :data-source="supplierList"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :placeholder="$t('请选择供应商')"
                :allow-filtering="true"
                filter-type="Contains"
                :disabled="forecastTemplate.billStatus !== 10 && editShow"
                :open-dispatch-change="false"
                :show-clear-button="true"
                @change="supplierSelectChange"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="categoryCode" :label="$t('品类')">
              <mt-select
                v-model="forecastTemplate.categoryCode"
                :data-source="categoryList"
                :fields="{ text: 'categoryName', value: 'categoryCode' }"
                :placeholder="$t('请选择品类')"
                :allow-filtering="true"
                filter-type="Contains"
                :disabled="forecastTemplate.billStatus !== 10 && editShow"
                :open-dispatch-change="false"
                :show-clear-button="true"
                @change="categorySelectChange"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              class="form-item"
              :label="$t('反馈截止时间')"
              label-style="top"
              prop="feedbackEndDate"
            >
              <mt-date-time-picker
                v-model="forecastTemplate.feedbackEndDate"
                :placeholder="$t('选择反馈截止日期')"
                :show-clear-button="true"
                :disabled="forecastTemplate.billStatus !== 10 && editShow"
              ></mt-date-time-picker>
            </mt-form-item>
            <mt-form-item prop="billStatus" :label="$t('单据状态')" v-show="editShow">
              <mt-select
                :disabled="true"
                v-model="forecastTemplate.billStatus"
                :data-source="billList"
                :fields="{ text: 'text', value: 'value' }"
                :placeholder="$t('请选择状态')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" v-show="editShow">
              <mt-input
                v-model="forecastTemplate.createUserName"
                type="text"
                :disabled="true"
                :placeholder="$t('请输入模板名称')"
              >
              </mt-input>
            </mt-form-item>
            <mt-form-item
              prop="oaApprovalComments"
              :label="$t('OA审批意见')"
              v-show="forecastTemplate.billStatus == 40 || forecastTemplate.billStatus == 60"
            >
              <mt-input
                :disabled="true"
                v-model="forecastTemplate.oaApprovalComments"
                type="textarea"
                :placeholder="$t('无')"
              >
              </mt-input>
            </mt-form-item>
          </mt-form>
          <div class="detail-content">
            <ScTable
              :loading="loading"
              :loading-config="{ icon: 'vxe-icon-indicator roll', text: '正在拼命加载中...' }"
              ref="xTable"
              max-height="100%"
              :row-config="{ height: 38 }"
              :columns="columns"
              :table-data="tableData"
              header-align="center"
              :tree-config="{}"
              align="center"
            >
            </ScTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main'
import ScTable from '@/components/ScTable/src/index'
import { download } from '@/utils/utils'
import utils from '@/utils/utils'
export default {
  components: {
    ScTable
  },
  data() {
    return {
      pageConfig: {
        current: 1,
        size: 20
      },
      totalNum: 0,
      editShow: false,
      loading: false,
      moudleList: [],
      companyList: [],
      supplierList: [],
      categoryList: [],
      templeData: [],
      forecastTemplate: {
        billCode: '', //单据编号
        billStatus: 0, //单据状态
        billType: 0, //单据类型
        categoryCode: '', //品类编码
        categoryId: '',
        categoryName: '',
        feedbackEndDate: '', //反馈截止时间
        oaApprovalComments: '', //OA审批意见
        orgCode: '', //组织编码
        orgId: '',
        orgName: '',
        supplierId: '',
        supplierName: '',
        supplierTeantId: '',
        supplierCode: '' //供应商编码
      },
      formData: {},
      tableData: [],
      billList: [
        {
          text: this.$t('新增'),
          value: 10
        },
        {
          text: this.$t('待反馈'),
          value: 20
        },
        {
          text: this.$t('待审批'),
          value: 30
        },
        {
          text: this.$t('已驳回'),
          value: 40
        },
        {
          text: this.$t('已通过'),
          value: 50
        },
        {
          text: this.$t('已废弃'),
          value: 60
        }
      ],
      typeList: [
        {
          text: this.$t('全新供应商'),
          value: 1
        },
        {
          text: this.$t('三新物料'),
          value: 2
        }
      ],
      columns: [
        {
          type: 'checkbox',
          width: 90
        },
        {
          type: 'seq',
          title: i18n.t('序号'),
          width: 90
        },
        {
          field: 'subTemplateName',
          title: i18n.t('子模板'),
          width: 800,
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <vxe-button
                  status='primary'
                  type='text'
                  size='mini'
                  onClick={() => {
                    this.codeClickEvent(row)
                  }}>
                  {row.subTemplateName}
                </vxe-button>
              ]
            }
          }
        },
        {
          field: 'narrative',
          title: i18n.t('描述'),
          width: 250
        },
        {
          field: 'uploadType',
          title: i18n.t('是否必须上传附件'),
          width: 180,
          slots: {
            default: ({ row }) => {
              return [
                <div style='width:100%; display: inline-flex;justify-content: center;'>
                  <div style='width:100%; display: flex;justify-content: center;'>
                    <vxe-checkbox v-model={row.uploadType}></vxe-checkbox>
                  </div>
                </div>
              ]
            }
          }
        },
        {
          field: 'fileName',
          title: i18n.t('附件'),
          width: 300,
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <vxe-button
                  status='primary'
                  type='text'
                  size='mini'
                  onClick={() => {
                    this.fileClickEvent(row)
                  }}>
                  {row.fileName}
                </vxe-button>
              ]
            }
          }
        },
        {
          field: 'remark',
          title: i18n.t('备注')
        }
      ],
      rules: {
        templateId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        billCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        billType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        feedbackEndDate: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        orgCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.getMoudleList()
    if (this.$route.query.id) {
      this.editShow = true
      this.initialization = true
      this.getFormDetail(this.$route.query.id)
    }
  },
  methods: {
    // 获取ppap模板列表
    getMoudleList() {
      this.$API.PPAPConfig.queryInfo({ page: this.pageConfig }).then((res) => {
        res.data.records.forEach((item) => {
          this.moudleList.push(item)
        })
      })
    },
    // 模板选择
    templateSelectChange(data) {
      this.getCompany(data.itemData.id)
      this.forecastTemplate.orgId = ''
      this.forecastTemplate.orgName = ''
      this.forecastTemplate.orgCode = ''
      this.forecastTemplate.supplierCode = ''
      this.forecastTemplate.supplierId = ''
      this.forecastTemplate.supplierName = ''
      this.forecastTemplate.categoryCode = ''
      this.forecastTemplate.categoryId = ''
      this.forecastTemplate.categoryName = ''
      this.forecastTemplate.supplierTenantId = ''
      this.getTemplateDetail(data.itemData.id)
    },
    // 获取公司
    getCompany(param) {
      this.companyList = []
      let params = {
        templateId: param
      }
      this.$API.PPAPConfig.moudleFindCompany(params).then((res) => {
        this.companyList = res.data
      })
    },
    // 公司选择
    companySelectChange(data) {
      this.forecastTemplate.supplierCode = ''
      this.forecastTemplate.supplierId = ''
      this.forecastTemplate.supplierName = ''
      this.forecastTemplate.categoryCode = ''
      this.forecastTemplate.categoryId = ''
      this.forecastTemplate.categoryName = ''
      this.forecastTemplate.supplierTenantId = ''
      this.forecastTemplate.orgId = data.itemData.orgId
      this.forecastTemplate.orgName = data.itemData.orgName
      this.getSupplier(data.itemData.orgCode)
    },
    // 获取供应商
    getSupplier(param) {
      this.supplierList = []
      let params = {
        orgCode: param
      }
      this.$API.PPAPConfig.companyFindSupplier(params).then((res) => {
        this.supplierList = res.data
      })
    },
    // 供应商选择
    supplierSelectChange(data) {
      this.forecastTemplate.categoryCode = ''
      this.forecastTemplate.categoryId = ''
      this.forecastTemplate.categoryName = ''
      this.getCatagoryList(data.itemData)
      this.forecastTemplate.supplierTenantId = data.itemData.supplierTenantId
      this.forecastTemplate.supplierId = data.itemData.id
      this.forecastTemplate.supplierName = data.itemData.supplierName
    },
    // 获取品类
    getCatagoryList(param) {
      this.categoryList = []
      let params = {
        organizationCode: param?.organizationCode ? param.organizationCode : param.orgCode,
        supplierCode: param.supplierCode
      }
      this.$API.PPAPConfig.companyAndSupplierFindCategory(params).then((res) => {
        this.categoryList = res.data
      })
    },
    // 选取品类
    categorySelectChange(data) {
      this.forecastTemplate.categoryId = data.itemData?.id
      this.forecastTemplate.categoryName = data.itemData?.categoryName
    },
    // 下载子模板
    codeClickEvent(data) {
      this.$API.fileService
        .downloadPublicFile({
          id: data.subTemplateId
        })
        .then((res) => {
          download({
            fileName: data.subTemplateName,
            blob: res.data
          })
        })
    },
    // 下载子附件
    fileClickEvent(data) {
      this.$API.fileService
        .downloadPublicFile({
          id: data.fileId
        })
        .then((res) => {
          download({
            fileName: data.fileName,
            blob: res.data
          })
        })
    },
    //获取模板详情数据
    getTemplateDetail(id) {
      const params = {
        page: this.pageConfig,
        templateId: id
      }
      this.$API.PPAPConfig.templateFindDetail(params).then((res) => {
        this.tableData = res.data
        this.tableData.forEach((item) => {
          if (item.uploadType == 1) {
            item.uploadType = true
          } else if (item.uploadType == 0) {
            item.uploadType = false
          }
        })
      })
    },
    //获取详情数据
    getFormDetail(id) {
      this.loading = true
      const params = {
        page: this.pageConfig,
        billId: id
      }
      this.$API.PPAPConfig.billDetailQuery(params).then((res) => {
        this.getCompany(res.data.templateId)
        this.getSupplier(res.data.orgCode)
        this.getCatagoryList(res.data)
        res.data.detailsResponseList.forEach((item) => {
          if (item.uploadType == 1) {
            item.uploadType = true
          } else if (item.uploadType == 0) {
            item.uploadType = false
          }
        })
        this.tableData = res.data.detailsResponseList
        this.forecastTemplate.templateId = res.data.templateId
        this.forecastTemplate.orgCode = res.data.orgCode
        this.forecastTemplate.supplierCode = res.data.supplierCode
        this.forecastTemplate.supplierTenantId = res.data.supplierTenantId
        this.forecastTemplate.billCode = res.data.billCode
        this.forecastTemplate.billType = res.data.billType
        this.forecastTemplate.billStatus = res.data.billStatus
        this.forecastTemplate.feedbackEndDate = res.data.feedbackEndDate
        this.forecastTemplate.oaApprovalComments = res.data.oaApprovalComments
        this.forecastTemplate.createUserName = res.data.createUserName
        this.forecastTemplate.categoryCode = res.data.categoryCode
        this.loading = false
      })
    },
    // 返回
    backTo() {
      this.$router.push({
        path: '/supplier/pur/ppap-doc-manage'
      })
    },
    // 保存
    submitEvent() {
      if (
        !this.forecastTemplate.orgCode ||
        !this.forecastTemplate.supplierCode ||
        !this.forecastTemplate.billType ||
        !this.forecastTemplate.feedbackEndDate
      ) {
        this.$toast({
          content: this.$t('请填写必填内容'),
          type: 'warning'
        })
        return
      }
      let detailsRequestList = this.tableData
      detailsRequestList.forEach((item) => {
        if (item.uploadType == true) {
          item.uploadType = 1
        } else if (item.uploadType == false) {
          item.uploadType = 0
        }
        item.narrative = item.description
      })
      let params = {
        detailsRequestList: detailsRequestList,
        billId: this.$route.query.id ? this.$route.query.id : '',
        billStatus: this.$route.query.id ? this.forecastTemplate.billStatus : 10,
        billType: this.forecastTemplate.billType,
        categoryCode: this.forecastTemplate.categoryCode,
        categoryId: this.forecastTemplate.categoryId,
        categoryName: this.forecastTemplate.categoryName,
        feedbackEndDate: utils.formateTime(
          this.forecastTemplate.feedbackEndDate,
          'yyyy-MM-dd hh:mm:ss'
        ),
        orgCode: this.forecastTemplate.orgCode,
        orgId: this.forecastTemplate.orgId,
        orgName: this.forecastTemplate.orgName,
        supplierCode: this.forecastTemplate.supplierCode,
        supplierId: this.forecastTemplate.supplierId,
        supplierName: this.forecastTemplate.supplierName,
        supplierTenantId: this.forecastTemplate.supplierTenantId,
        templateId: this.forecastTemplate.templateId
      }
      this.$API.PPAPConfig.billSave(params).then(() => {
        this.$toast({
          content: this.$t('保存成功'),
          type: 'success'
        })
        this.$router.push({
          path: '/supplier/pur/ppap-doc-manage'
        })
      })
    },
    // 提交并保存
    saveAndSubmit() {
      if (
        !this.forecastTemplate.orgCode ||
        !this.forecastTemplate.supplierCode ||
        !this.forecastTemplate.billType ||
        !this.forecastTemplate.feedbackEndDate
      ) {
        this.$toast({
          content: this.$t('请填写必填内容'),
          type: 'warning'
        })
        return
      }
      let detailsRequestList = this.tableData
      detailsRequestList.forEach((item) => {
        if (item.uploadType == true) {
          item.uploadType = 1
        } else if (item.uploadType == false) {
          item.uploadType = 0
        }
        item.narrative = item.description
      })
      let params = {
        detailsRequestList: detailsRequestList,
        billId: this.$route.query.id ? this.$route.query.id : '',
        billStatus: this.$route.query.id ? this.forecastTemplate.billStatus : 10,
        billType: this.forecastTemplate.billType,
        categoryCode: this.forecastTemplate.categoryCode,
        categoryId: this.forecastTemplate.categoryId,
        categoryName: this.forecastTemplate.categoryName,
        feedbackEndDate: utils.formateTime(
          this.forecastTemplate.feedbackEndDate,
          'yyyy-MM-dd hh:mm:ss'
        ),
        orgCode: this.forecastTemplate.orgCode,
        orgId: this.forecastTemplate.orgId,
        orgName: this.forecastTemplate.orgName,
        supplierCode: this.forecastTemplate.supplierCode,
        supplierId: this.forecastTemplate.supplierId,
        supplierName: this.forecastTemplate.supplierName,
        supplierTenantId: this.forecastTemplate.supplierTenantId,
        templateId: this.forecastTemplate.templateId
      }
      this.$API.PPAPConfig.billSubmit(params).then(() => {
        this.$toast({
          content: this.$t('保存并提交成功'),
          type: 'success'
        })
        this.$router.push({
          path: '/supplier/pur/ppap-doc-manage'
        })
      })
    }
  },
  watch: {}
}
</script>
<style lang="scss" scoped>
/deep/ .j-icon[data-v-122890a8] svg path {
  fill: #737373;
}

/deep/ .e-ddl.e-input-group.e-control-wrapper {
  .e-ddl-icon::before {
    content: '\e969';
    font-size: 16px;
    margin-right: 10px;
  }
}
/deep/ .ant-select-selection {
  background: #fafafa !important;
}
/deep/ .vxe-header--column {
  .vxe-resizable.is--line:before {
    width: 0px;
  }
}
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
.full-height .flex-keep {
  display: flex;
}
.main-bottom {
  padding-top: 10px;
  width: 100%;
  /deep/ .mt-form {
    margin-top: 50px;
    margin-left: 20px;
  }
  /deep/ .mt-form-item {
    width: calc(30% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;

    // &.more-width {
    //   // width: 450px;
    // }

    .full-width {
      width: calc(100% - 20px) !important;
    }
  }
  /deep/ .e-flat {
    position: relative;
    top: 21px;
  }
}
.header-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .itemcon {
    display: flex;
    align-items: center;
  }
  .item {
    margin-right: 20px;
  }
  .middle-blank {
    flex: 1;
  }
  .status {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(99, 134, 193, 1);
    padding: 4px;
    background: rgba(238, 242, 249, 1);
    border-radius: 2px;
  }

  .infos {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
  }

  .sort-box {
    position: relative;
    cursor: pointer;
    margin-right: 20px;
    .mt-icons {
      font-size: 12px;
      transform: scale(0.5);
      color: rgba(0, 70, 156, 1);
      margin-top: -10px;
      position: absolute;
      top: 0;

      &:nth-child(2) {
        top: 6px;
      }
    }
  }
}
</style>
