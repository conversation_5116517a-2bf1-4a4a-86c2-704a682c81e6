<template>
  <div class="freezeBox">
    <mt-template-page
      ref="templateRef"
      :template-config="exitConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>
<script>
import { freezeColumn } from './config/column'
import utils from '@/utils/utils'
import * as XLSX from 'xlsx'
export default {
  data() {
    return {
      sampleData: [],
      punishObjectMap: {
        1: this.$t('品类'),
        2: this.$t('公司')
      },
      punishRangeMap: {
        1: this.$t('公司级'),
        2: this.$t('采购组织级'),
        3: this.$t('集团级')
      },
      applyStatusMap: {
        10: this.$t('待提交'),
        20: this.$t('待审批'),
        30: this.$t('已驳回'),
        40: this.$t('已完成'),
        50: this.$t('已关闭'),
        60: this.$t('待处理'),
        70: this.$t('中止'),
        80: this.$t('已下发'),
        90: this.$t('下发失败'),
        100: this.$t('下发成功'),
        110: this.$t('已发布'),
        '-4': this.$t('废弃')
      },
      exitConfig: [
        {
          gridId: '96577ce9-0259-45fd-b7da-4f9decc6dfce',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_table_new', title: this.$t('新增') },
                { id: 'edit', icon: 'icon_table_edit', title: this.$t('编辑') },
                {
                  id: 'delete',
                  icon: 'icon_table_delete',
                  title: this.$t('删除')
                },
                {
                  id: 'updata',
                  icon: 'icon_solid_submit',
                  title: this.$t('提交')
                },
                // {
                //   id: 'import',
                //   title: this.$t('批量退出'),
                //   icon: ''
                // },
                {
                  id: 'issue',
                  icon: 'icon_solid_submit',
                  title: this.$t('重新下发')
                },
                { id: 'export1', icon: 'icon_table_new', title: this.$t('导出') },
                {
                  id: 'audit',
                  title: this.$t('查看OA审批'),
                  icon: 'icon_solid_editsvg'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          buttonQuantity: 6,
          grid: {
            columnData: freezeColumn,
            asyncConfig: {
              // url: '/supplier/tenant/buyer/apply/punish/pageQuery',
              url: '/supplier/tenant/buyer/apply/punish/pageBatchQuery',
              params: {
                applyTypeList: ['punish'],
                businessTypeList: ['disuse']
              },
              serializeList: (list) => {
                this.sampleData = list
                return list
              }
              // defaultRules: [
              //   {
              //     label: this.$t("状态"),
              //     field: "businessType",
              //     type: "number",
              //     operator: "equal",
              //     value: "disuse",
              //   },
              // ],
            },
            frozenColumns: 3
          }
        }
      ]
    }
  },
  activated() {
    this.$refs.templateRef.refreshCurrentGridData()
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      const sltList = gridRef.getMtechGridRecords()
      if (toolbar.id === 'add') {
        this.addDialog()
      }
      if (toolbar.id === 'edit') {
        this.edit(sltList)
      }
      if (toolbar.id === 'delete') {
        this.delete(sltList)
      }
      if (toolbar.id === 'updata') {
        this.updata(sltList)
      }
      if (toolbar.id === 'issue') {
        this.issue(sltList)
      }
      if (e.toolbar.id == 'export1') {
        let data = []
        if (sltList.length == 0) {
          data = this.sampleData
        } else {
          data = sltList
        }

        let tableData = [
          [
            this.$t('申请单编码'),
            this.$t('申请单名称'),
            this.$t('对象维度'),
            this.$t('组织范围'),
            this.$t('供应商编号-SAP'),
            this.$t('供应商编号-SRM'),
            this.$t('供应商'),
            this.$t('状态'),
            this.$t('创建人'),
            this.$t('创建日期')
          ]
        ]
        data.forEach((item) => {
          let arr = []
          arr.push(
            item.applyCode,
            item.applyName,
            this.punishObjectMap[item.punishObject],
            this.punishRangeMap[item.punishRange],
            item?.relationDTOList[0]?.supplierCode,
            item?.relationDTOList[0]?.supplierInternalCode,
            item?.relationDTOList[0]?.supplierEnterpriseName,
            this.applyStatusMap[item.applyStatus],
            item.createUserName,
            utils.formateTime(Number(item.createDate))
          )
          tableData.push(arr)
        })
        let ws = XLSX.utils.aoa_to_sheet(tableData)
        let wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')
        XLSX.writeFile(wb, this.$t(`供应商退出清单.xlsx`))
      }
      if (e.toolbar.id === 'audit') {
        this.audit(sltList)
      }
      if (toolbar.id === 'import') {
        this.handleImport()
      }
    },
    audit(sltList) {
      if (sltList.length < 1) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (sltList.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      let params = {
        applyId: sltList[0].id,
        businessType: sltList[0].businessType
      }
      this.$API.assessManage.infoGetOALink(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    issue(sltList) {
      if (!sltList || sltList.length !== 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (sltList[0].applyStatus != 90) {
        this.$toast({ content: this.$t('请先选择下发失败的数据'), type: 'warning' })
        return
      }
      this.$API.SupplierPunishment.punishIssue({
        applyId: sltList[0].id
      }).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('下发成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleClickCellTitle(e) {
      console.log(e)
      const { data } = e
      if (data?.batchFlag === 1) {
        this.$router.push({
          path: 'exit-batch-detail',
          query: {
            id: data.id
          }
        })
      } else {
        this.$router.push({
          path: 'punish-detail-exit',
          query: {
            id: data.id
          }
        })
      }
    },

    addDialog() {
      let _this = this
      this.$dialog({
        modal: () => import('./components/addDialog.vue'),
        data: {
          title: this.$t('新建退出单'),
          isEdit: false
        },
        success: (data) => {
          console.log(data)
          if (data === 'reload') {
            _this.$refs.templateRef.refreshCurrentGridData()
          } else if (data.type === 'jump') {
            this.$refs.templateRef.refreshCurrentGridData()
            this.$router.push({
              path: 'punish-detail-exit',
              query: {
                id: data.id
              }
            })
          }
        }
      })
    },

    delete(sltList) {
      const _this = this
      if (!sltList || sltList.length === 0) {
        this.$toast({ content: this.$t('请选择删除的栏目'), type: 'warning' })
        return
      }
      let ids = sltList.map((item) => item.id)

      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选申请单？'),
          confirm: () => _this.$API.SupplierPunishment.deleteNew({ applyIdList: ids })
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    edit(sltList) {
      const _this = this
      if (sltList.some((item) => item.applyStatus === 20 || item.applyStatus === 40)) {
        this.$toast({
          content: this.$t('待审批、已完成状态不能编辑！'),
          type: 'warning'
        })
        return
      }

      if (!sltList || sltList.length !== 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      this.$dialog({
        modal: () => import('./components/addDialog.vue'),
        data: {
          title: this.$t('编辑退出单'),
          isEdit: true,
          info: sltList
        },
        success: (data) => {
          console.log(data)
          if (data === 'reload') {
            _this.$refs.templateRef.refreshCurrentGridData()
          } else if (data.type === 'jump') {
            this.$refs.templateRef.refreshCurrentGridData()
            this.$router.push({
              path: 'punish-detail-exit',
              query: {
                id: data.id
              }
            })
          }
        }
      })
    },

    async updata(sltList) {
      try {
        // 参数校验
        if (!this.validateParams(sltList)) {
          return
        }

        // 构建请求参数
        const params = this.buildRequestParams(sltList)

        // 弹窗确认
        await this.showConfirmDialog(params)
      } catch (error) {
        this.$toast({
          content: this.$t('提交失败'),
          type: 'error'
        })
        console.error('提交失败:', error)
      }
    },

    // 参数校验
    validateParams(sltList) {
      if (!sltList.length) {
        this.$toast({
          content: this.$t('请选择一条数据'),
          type: 'warning'
        })
        return false
      }

      if (sltList.some((item) => item.applyStatus !== 10 && item.applyStatus !== 30)) {
        this.$toast({
          content: this.$t('待提交和驳回的单据可以提交！'),
          type: 'warning'
        })
        return false
      }

      return true
    },

    // 构建请求参数
    buildRequestParams(sltList) {
      return sltList.map((item) => ({
        id: item.id,
        batchFlag: item.batchFlag
      }))
    },

    // 显示确认弹窗
    async showConfirmDialog(params) {
      await this.$dialog({
        data: {
          title: this.$t('提交'),
          message: this.$t('是否确认提交所选申请单？'),
          confirm: () => this.$API.SupplierPunishment.batchSubmitApi(params)
        },
        success: () => {
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('批量退出'),
          paramsKey: 'multipartFile',
          importApi: this.$API.SupplierPunishment.importDisuseApi,
          downloadTemplateApi: this.$API.SupplierPunishment.downloadDisuseApi
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.freezeBox {
  width: 100%;
  height: 100%;
}
</style>
