<template>
  <div class="full-height">
    <div class="detail-header--wrap">
      <p class="detail-header-name">
        {{ detail.surveyTaskName }}

        <span class="detail-header-button--wrap">
          <mt-button type="text" class="detail-header-button" @click="backDetail">{{
            $t('返回')
          }}</mt-button>
          <mt-button
            v-if="isEditForm"
            type="text"
            class="detail-header-button"
            @click="saveDetail"
            >{{ $t('保存') }}</mt-button
          >
          <mt-button
            v-if="isEditForm"
            type="text"
            class="detail-header-button"
            @click="saveAndSubmitDetail"
            >{{ $t('保存并提交') }}</mt-button
          >
        </span>
      </p>
      <p class="detail-header-category detail-header-items">
        <span class="detail-header-item"> {{ $t('表单编码：') }}{{ detail.surveyTaskNo }}</span>
        <span class="detail-header-item"> {{ $t('来源：') }}{{ detail.surveySource }}</span>
        <span class="detail-header-item">
          {{ $t('调查表类型：') }}{{ detail.surveyTemplateType }}</span
        >
        <span class="detail-header-item"> {{ $t('创建人：') }}{{ detail.createUserName }}</span>
      </p>
      <p class="detail-header-items">
        <span class="detail-header-item"
          >{{ $t('公司：')
          }}{{
            isPur
              ? detail.orgSupplierRelationDTO && detail.orgSupplierRelationDTO.orgName
              : detail.supplierInfoDTO && detail.supplierInfoDTO.customerEnterpriseName
          }}</span
        >
        <span class="detail-header-item"
          >{{ $t('供应商：')
          }}{{
            isPur
              ? detail.orgSupplierRelationDTO &&
                detail.orgSupplierRelationDTO.supplierEnterpriseName
              : detail.supplierInfoDTO && detail.supplierInfoDTO.supplierEnterpriseNames
          }}</span
        >
      </p>
    </div>

    <div class="form-content flex1" ref="formContent">
      <template v-if="formInstanceResponseList.length > 0">
        <div
          v-for="item of formInstanceResponseList"
          :key="item.id"
          class="display-item"
          :ref="'formItem_' + item.id"
          :data-id="item.id"
        >
          <div class="parse-title">{{ item.formName }}</div>
          <div class="box">
            <div class="mark" v-if="!isEditForm"></div>
            <mt-parser
              :ref="`parser_${item.id}`"
              :form-conf="item.formDefineResponse ? item.formDefineResponse.template : {}"
            />
          </div>

          <!-- <information-parser
            v-else
            :ref="`information_${item.id}`"
            :form-conf="
              item.formDefineResponse ? item.formDefineResponse.template : {}
            "
            :value-data="
              item.formDataResponse ? item.formDataResponse.data : {}
            "
            label-class="parser-label"
            value-class="parser-value"
          /> -->
        </div>
      </template>
      <template v-else>
        <div class="empty-container">
          <!-- <img src="../../../../assets/emptyData.png" /> -->
          <div class="empty-txt">{{ emptyMsg }}</div>
        </div>
      </template>
    </div>
  </div>
</template>
<script>
import Parser from '@mtech-form-design/form-parser'
// import InformationParser from '@mtech-form-design/information-parser'
import utils from '@/utils/utils'
import { checkIsPur } from './config/utils'

export default {
  components: {
    'mt-parser': Parser
    // 'information-parser': InformationParser
  },
  data() {
    return {
      detail: {},
      formInstanceResponseList: [],
      emptyMsg: '',
      //公司准入关系
      buyerPartnerFactoryRelationList: {},
      //表单详情
      buyerFormInstanceList: {},
      // 表单模板
      formTemplateArr: [],
      isPur: checkIsPur()
    }
  },
  computed: {
    infoId() {
      return this.$route.query.id
    },
    isEditForm() {
      return !this.isPur && [1, 4].includes(this.detail.status)
    }
  },
  created() {
    this.detailSurveySupplier(this.infoId)
  },
  methods: {
    detailSurveySupplier(id) {
      this.$API.supplierInfoSurvey.detailSurveySupplier(id).then((res) => {
        if (res.code === 200 && !utils.isEmpty(res.data)) {
          this.detail = res.data
          this.formInstanceResponseList = res.data?.formInstanceResponseList || []
          // if (this.isEditForm) {
          this.$nextTick(() => {
            this.formInstanceResponseList.forEach((item) => {
              this.$refs[`parser_${item.id}`][0].setFormData(item.formDataResponse.data)
            })
          })
          // }
        } else {
          this.detail = {}
          this.formInstanceResponseList = []
        }
      })
    },
    saveData() {},

    backDetail() {
      if (this.isPur) {
        this.$router.push({
          path: '/supplier/pur/info-survey'
        })
      } else {
        this.$router.push({
          path: '/supplier/sup/info-survey'
        })
      }
    },

    saveDetail() {
      // todo: 接入动态表单的校验和获取数据
      const param = this.formatParams()
      this.$API.supplierInfoSurvey.saveForm(param).then((result) => {
        if (result.code === 200 && !utils.isEmpty(result.data)) {
          this.$toast({ content: result.msg, type: 'success' })
          this.backDetail()
        } else {
          this.$toast({ content: result.msg, type: 'error' })
        }
      })
    },

    saveAndSubmitDetail() {
      // todo: 接入动态表单的校验和获取数据

      const param = this.formatParams()
      param.status = 2
      this.$API.supplierInfoSurvey.saveAndPublish(param).then((result) => {
        if (result.code === 200 && !utils.isEmpty(result.data)) {
          this.$toast({ content: result.msg, type: 'success' })
          this.backDetail()
        } else {
          this.$toast({ content: result.msg, type: 'error' })
        }
      })
    },

    formatParams() {
      const formDataList = this.formInstanceResponseList.map((item) => {
        return {
          ...item,
          // formTemplateKey: "",
          // formTemplateType: "",
          formDataResponse: this.$refs[`parser_${item.id}`][0].getFormData(),
          formTemplateVersion: item.formDefineResponse.version
        }
      })

      return {
        formInstanceRequestList: formDataList,
        surveyId: this.detail.id
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.mark {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 999;
}
.box {
  position: relative;
}
.full-height {
  height: 100%;
  width: 100%;
  background-color: #fff;
  .operateButton {
    right: 18px;
    top: 18px;
  }
}
.detail-header--wrap {
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 20px 30px;
  margin-bottom: 16px;

  .detail-header-name {
    font-size: 20px;
    line-height: 32px;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
  }
  .detail-header-category {
    font-size: 12px;
    line-height: 16px;
    color: rgba(41, 41, 41, 1);
  }
  .detail-header-items {
    font-size: 14px;
    font-weight: 600;
    margin-top: 20px;
    color: rgba(41, 41, 41, 1);
    .detail-header-item {
      margin-right: 24px;
    }
  }

  .detail-header-button--wrap {
    float: right;
    .detail-header-button {
      margin-right: 24px;
    }
  }
}
.form-content {
  height: calc(100% - 120px);
  overflow: auto;
}
.parse-title {
  color: #292929;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 20px;
  border-left: 2px solid #00469c;
  padding-left: 10px;
}
</style>
