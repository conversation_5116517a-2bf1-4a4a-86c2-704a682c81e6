<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @open="onOpen" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules">
        <mt-form-item prop="orgIdArr" :label="$t('组织：')">
          <mt-DropDownTree
            :key="fieldsarr.dataSource.length"
            v-model="formObject.orgIdArr"
            :placeholder="$t('请选择组织：')"
            :popup-height="500"
            :fields="fieldsarr"
            @input="selectOrg"
            :allow-filtering="true"
            filter-type="Contains"
            id="baseTreeSelect"
            :filter-bar-placeholder="filterPlaceholder"
            :readonly="modalData.flag === 1"
          ></mt-DropDownTree>
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('原因：')">
          <mt-input
            :max-length="100"
            type="text"
            :placeholder="$t('请输入原因')"
            v-model="formObject.remark"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierEnterpriseId" :label="$t('供应商：')">
          <mt-select
            v-if="modalData.flag !== 1"
            :allow-filtering="true"
            v-model="formObject.supplierEnterpriseId"
            :data-source="planeArrList"
            :fields="{
              text: 'codeAndName',
              value: 'supplierEnterpriseId'
            }"
            filter-type="Contains"
            :show-clear-button="true"
            @change="selectPlan"
            :placeholder="$t('请选择供应商')"
          ></mt-select>
          <mt-input
            v-else
            :disabled="modalData.flag === 1"
            :max-length="100"
            type="text"
            v-model="formObject.supplierName"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="effectiveStartMonth" :label="$t('生效月份')">
          <mt-date-picker
            :disabled="modalData.flag === 1"
            v-model="formObject.effectiveStartMonth"
            float-label-type="Never"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :allow-edit="false"
            :open-on-focus="true"
            :show-today-button="false"
            :placeholder="$t('请选择月份')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="effectiveEndMonth" :label="$t('失效月份')">
          <mt-date-picker
            v-model="formObject.effectiveEndMonth"
            float-label-type="Never"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :allow-edit="false"
            :open-on-focus="true"
            :show-today-button="false"
            :show-clear-button="false"
            :placeholder="$t('请选择月份')"
          ></mt-date-picker>
        </mt-form-item>
        <!-- <mt-form-item prop="category" :label="$t('品类')">
          <mt-input
            v-model="formObject.category"
            type="text"
            :placeholder="$t('请输入品类')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item prop="categoryId" class="form-item positive" :label="$t('品类')">
          <mt-DropDownTree
            :fields="categoryListArrList"
            v-model="formObject.categoryId"
            :allow-multi-selection="true"
            :auto-check="true"
            :show-check-box="true"
            :allow-filtering="true"
            filter-type="Contains"
            id="checkboxTreeSelect"
            :placeholder="$t('请选择品类')"
            @input="selectCategoryas"
            :key="categoryListArrList.key"
          ></mt-DropDownTree>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils.js'
// import dayjs from 'dayjs'
// import { cloneDeep } from 'lodash'
export default {
  data() {
    return {
      filterPlaceholder: 'Search',
      // 表单验证
      rules: {
        orgIdArr: [
          {
            required: true,
            message: this.$t('请选择组织'),
            trigger: 'blur'
          }
        ],
        supplierEnterpriseId: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        effectiveStartMonth: [
          {
            required: true,
            message: this.$t('请选择生效月份'),
            trigger: 'blur'
          }
        ],
        effectiveEndMonth: [
          {
            required: true,
            message: this.$t('请选择失效月份'),
            trigger: 'blur'
          }
        ]
      },
      //组织树下拉数组
      fieldsarr: {
        dataSource: [],
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      planeArrList: [], //供应商下拉数组
      //品类下拉
      categoryListArrList: {
        dataSource: [], //品类树下拉数组
        value: 'id',
        text: 'codeAndName',
        child: 'childrens',
        key: 'E6BPdKfEj5Ky2NwbH2F6Nkrhj6W8MamD'
      },
      // 弹窗确认事件
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      formObject: {
        orgCode: '', //组织机构编码
        orgId: '', //组织机构id
        orgLevel: '', //组织等级
        orgName: '', //组织机构名称

        remark: '', //原因 - 备注

        partnerArchiveId: '', //供应商档案id
        partnerRelationId: '', //	供应商关系id
        supplierEnterpriseCode: '', //供应商企业编码
        supplierEnterpriseId: '', //	供应商企业id
        supplierEnterpriseName: '', //供应商企业名称
        supplierCode: '', //供应商编码
        supplierId: '', //供应商id
        supplierName: '', //供应商名称

        effectiveStartMonth: null, //生效月份
        effectiveEndMonth: null, //失效月份
        categoryId: [], //品类ID
        orgIdArr: []
      },
      editStatus: false,
      orgObj: {}
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    console.log('is-disabled', this.modalData)
    this.$refs['dialog'].ejsRef.show()
    this.getOrgList() //获取组织树 - 下拉数据
    const categoryList = this.getCategoryList()
    this.getSupplierList() //获取供应商 - 下拉数据
    //编辑模式
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.$nextTick(() => {
        this.buttons = [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('保存') }
          }
        ]
      })
      // let _data = { ...this.modalData.data }
    }
    Promise.all([categoryList]).then((result) => {
      const list = result[0].map((i) => {
        return {
          ...i,
          codeAndName: `${i.categoryName}-${i.categoryCode}`
        }
      })
      this.$set(this.categoryListArrList, 'dataSource', list)
      this.$set(this.categoryListArrList, 'key', this.randomString())
      if (this.modalData.flag === 1) {
        // 编辑模式删除必填
        delete this.rules.orgIdArr
        delete this.rules.supplierEnterpriseId

        let _data = { ...this.modalData.rowData }
        this.formObject = {
          ..._data,
          orgIdArr: [_data.orgId],
          categoryId: _data?.categoryIds?.split(',')
        }
      }
    })
  },
  created() {},
  watch: {
    'formObject.orgIdArr': {
      handler() {
        this.$refs.dialogRef.validateField('orgIdArr')
      }
    }
  },
  methods: {
    //品类
    selectCategoryas(e) {
      console.log(e)
    },
    //获取组织树
    getOrgList() {
      this.$API.performanceManage.getListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.fieldsarr.dataSource = res.data
        }
      })
    },
    //选择组织
    selectOrg(e) {
      //清空供应商选中和品类选中
      // this.formObject.supplierCode = '' //供应商编码
      // this.formObject.supplierId = '' //供应商id
      // this.formObject.supplierName = '' //供应商名称
      // this.planeArrList = []
      //品类
      // this.$set(this.categoryListArrList, 'dataSource', [])
      // this.$set(this.categoryListArrList, 'key', this.randomString())
      // this.formObject.categoryId = [] //清空 品类的选中
      if (e.length > 0) {
        //匹配当前选中的组织 设置到formObject
        this.fn(this.fieldsarr.dataSource, e[0])
      }
    },
    // 递归 获取当前选中组织  设置到formObject
    fn(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.formObject.orgId = ele.id
          this.formObject.orgCode = ele.orgCode
          this.formObject.orgLevel = ele.orgLevel
          this.formObject.orgName = ele.orgName

          // this.getSupplierList({
          //   orgCode: ele.orgCode,
          //   orgId: ele.id,
          //   orgName: ele.orgName,
          //   orgType: ele.orgType,
          //   parentOrgId: ele.parentOrgId
          // }) //获取供应商 - 下拉数据
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.fn(ele.childrenList, id)
        }
      })
    },
    // 获取供应商列表
    getSupplierList() {
      let pamrsform = {
        // organizationCode: form.orgCode,
        // organizationId: form.orgId,
        // organizationName: form.orgName,
        // organizationType: form.orgType,
        // organizationParentId: form.parentOrgId
      }
      // this.orgObj = { orgId: form.orgId, parentOrgId: form.parentOrgId, orgType: form.orgType }
      this.$API.supplierExcept.getFuzzy(pamrsform).then((result) => {
        if (result.code == 200) {
          this.planeArrList = result.data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.supplierName}-${i.supplierCode}`
            }
          })
        }
      })
    },
    // 选择供应商点击事件
    selectPlan(e) {
      let { itemData } = e
      if (itemData) {
        // this.formObject.partnerArchiveId = itemData. //供应商档案id 后端说暂时未用到 先不传
        // this.formObject.partnerRelationId = itemData. //	供应商关系id 后端说暂时未用到 先不传
        // this.formObject.supplierEnterpriseCode = itemData. //供应商企业编码 后端说暂时未用到 先不传
        // this.formObject.supplierEnterpriseName = itemData. //供应商企业名称 后端说暂时未用到 先不传
        // this.formObject.supplierEnterpriseId = itemData.supplierEnterpriseId //	供应商企业id 后端说暂时未用到 先不传

        this.formObject.supplierCode = itemData.supplierCode //供应商编码
        this.formObject.supplierId = itemData.id //供应商id
        this.formObject.supplierName = itemData.supplierName //供应商名称
        // this.categoryListArrList.dataSource = [] //清空 品类的下拉数据
        // this.$nextTick(() => {
        //   this.getCategoryList(itemData) //请求品类的下拉数据
        // })
      } else {
        // this.$set(this.categoryListArrList, 'dataSource', [])
        // this.$set(this.categoryListArrList, 'key', this.randomString())
        // this.formObject.categoryId = [] //清空 品类的选中
      }
    },

    // 获取品类列表
    getCategoryList() {
      return new Promise((resolve) => {
        this.$API.supplierExcept
          .getCategoryList({
            fuzzyNameOrCode: ''
          })
          .then((result) => {
            if (result.code === 200 && !utils.isEmpty(result.data)) {
              resolve(result.data)
              //   const list = result.data.map((i) => {
              //     return {
              //       ...i,
              //       codeAndName: `${i.categoryName}-${i.categoryCode}`
              //     }
              //   })
              //   this.$set(this.categoryListArrList, 'dataSource', list)
              //   this.$set(this.categoryListArrList, 'key', this.randomString())
            }
          })
          .catch(() => {
            resolve([])
          })
      })
    },
    // 随机数创造
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    // 确认按钮
    confirm() {
      let effectiveStartMonth = this.$utils.formateTime(
        new Date(this.formObject.effectiveStartMonth),
        'yyyy/MM'
      )
      let effectiveEndMonth = this.$utils.formateTime(
        new Date(this.formObject.effectiveEndMonth),
        'yyyy/MM'
      )
      let addBuyerAssessExcludeRequest = JSON.parse(JSON.stringify(this.formObject))

      delete addBuyerAssessExcludeRequest.orgIdArr //删除
      //生效月份 小于 失效月份
      if (effectiveStartMonth > effectiveEndMonth) {
        this.$toast({ content: this.$t('生效月份不能大于失效月份'), type: 'warning' })
        return
      }
      // addBuyerAssessExcludeRequest.categoryId = addBuyerAssessExcludeRequest.categoryId.join(',')
      addBuyerAssessExcludeRequest.effectiveStartMonth = new Date(effectiveStartMonth).getTime() //转换为毫秒时间戳
      addBuyerAssessExcludeRequest.effectiveEndMonth = new Date(effectiveEndMonth).getTime() //转换为毫秒时间戳

      delete addBuyerAssessExcludeRequest.partnerArchiveId //后端说暂时未用到 先不传
      delete addBuyerAssessExcludeRequest.partnerRelationId //后端说暂时未用到 先不传
      delete addBuyerAssessExcludeRequest.supplierEnterpriseCode //后端说暂时未用到 先不传
      delete addBuyerAssessExcludeRequest.supplierEnterpriseName //后端说暂时未用到 先不传
      delete addBuyerAssessExcludeRequest.supplierEnterpriseId //后端说暂时未用到 先不传

      // addBuyerAssessExcludeRequest.supplierCode = '0102' //供应商编码
      // addBuyerAssessExcludeRequest.supplierId = '485882608629817346' //供应商id
      // addBuyerAssessExcludeRequest.supplierName = 'SPEC' //供应商名称

      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          this.$API.analysisOfSetting.cateexceptionAdd(addBuyerAssessExcludeRequest).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function') //关闭弹窗
              this.$refs.templateRef.refreshCurrentGridData() //刷新表格统一方法
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    onOpen(args) {
      args.preventFocus = true
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.browse {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    background: transparent;
    opacity: 0;
  }
  .show-input {
    width: 90%;
    height: 80%;
    background: transparent;
  }
}
</style>
