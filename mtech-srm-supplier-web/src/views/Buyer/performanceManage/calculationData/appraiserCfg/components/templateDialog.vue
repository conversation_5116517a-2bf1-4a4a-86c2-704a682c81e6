<template>
  <mt-dialog
    ref="dialog"
    :buttons="buttons"
    :header="header"
    class="dialog-main"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules">
        <mt-form-item prop="orgIdArr" :label="$t('组织：')">
          <mt-DropDownTree
            v-if="orgFields.dataSource.length > 0"
            v-model="formObject.orgIdArr"
            :placeholder="$t('请选择组织')"
            :popup-height="500"
            :fields="orgFields"
            :enabled="editType !== 'edit'"
            :allow-filtering="true"
            filter-type="Contains"
            @change="selectOrganization"
            id="baseTreeSelect"
          ></mt-DropDownTree>
          <mt-select
            v-else
            :disabled="editType === 'edit'"
            :placeholder="$t('请选择组织')"
            :data-source="[]"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="templateId" :label="$t('绩效模板：')">
          <mt-select
            v-model="formObject.templateId"
            :disabled="editType === 'edit'"
            :data-source="templateList"
            :fields="{ text: 'templateName', value: 'templateId' }"
            :show-clear-button="true"
            filter-type="Contains"
            @select="selectTemplate"
            :placeholder="$t('组织下面的绩效模板')"
          ></mt-select>
        </mt-form-item>
        <!-- 原来的框架 -->
        <mt-form-item prop="categoryArr" :label="$t('品类：')">
          <mt-multi-select
            v-model="formObject.categoryArr"
            :disabled="editType === 'edit'"
            :data-source="categoryList"
            :allow-filtering="true"
            :show-clear-button="true"
            :show-select-all="true"
            filter-type="Contains"
            :fields="{ text: 'codeAndName', value: 'categoryId' }"
            :placeholder="$t('请选择品类')"
            @change="selectCategoryas"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="uid" :label="$t('责任人')">
          <div class="responsible">
            <mt-select
              v-model="formObject.uid"
              id="dropDownTreeCom"
              style="width: 100%"
              :fields="{ text: 'ownerName', value: 'uid' }"
              :data-source="ownerList"
              filter-bar-:placeholder="$t('Search')"
              :allow-filtering="true"
              :filtering="getOwnerList"
              filter-type="Contains"
              :placeholder="$t('负责人员')"
              :filter-bar-placeholder="$t('请输入用户名称进行搜索')"
              @select="selectOwner"
              :no-records-template="noRecordsTemplate"
            ></mt-select>
          </div>
        </mt-form-item>

        <mt-form-item prop="indicatorsArr" :label="$t('指标：')">
          <mt-multi-select
            v-model="formObject.indicatorsArr"
            :disabled="editType === 'edit'"
            :data-source="indexList"
            :show-clear-button="true"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择模板下的指标')"
            :fields="{ text: 'indexName', value: 'indexId' }"
            @change="indexChange"
          ></mt-multi-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      // 表单验证
      rules: {
        indicatorsArr: [
          {
            required: true,
            message: this.$t('请选择指标'),
            trigger: 'blur'
          }
        ],
        uid: [
          {
            required: true,
            message: this.$t('请选择责任人'),
            trigger: 'blur'
          }
        ],
        categoryArr: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        templateId: [
          {
            required: true,
            message: this.$t('请选择绩效模板'),
            trigger: 'blur'
          }
        ],
        orgIdArr: [
          {
            required: true,
            message: this.$t('请选择组织'),
            trigger: 'blur'
          }
        ]
      },
      orgFields: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },

      supplierarr: [],
      indexList: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('新增') }
        }
      ],
      formObject: {
        orgIdArr: [],
        orgCode: '', //组织机构编码   0
        orgId: '', //组织机构id   0
        orgName: '', //组织机构名称  0

        templateId: '', //绩效模板id  0
        templateName: '', //绩效模板名称  0

        // 品类
        categoryArr: [],
        categoryCode: '', //品类编码
        categoryId: '', //品类id
        categoryName: '', //品类名称

        // 责任人
        uid: '', //责任人uid
        employeeId: '', //责任人id
        employeeName: '', //	责任人姓名

        // 指标
        indicatorsArr: []
      },

      editStatus: false,
      templateList: [], //绩效模板下拉数组
      categoryList: [], //品类下拉数据
      ownerList: [], //部门下人员

      fuzzyName: '',
      noRecordsTemplate: this.$t('请输入用户名称进行搜索')
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    editType() {
      return this.modalData.editType
    }
  },
  mounted() {
    this.getOwnerList = utils.debounce(this.getOwnerList, 300)
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.$nextTick(() => {
        this.buttons = [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('保存') }
          }
        ]
      })
      let _data = { ...this.modalData.data }
      this.formObject = {
        ..._data,
        uid: _data.employeeId,
        orgIdArr: [_data.orgId],
        categoryArr: _data.categoryRelationList?.map((item) => item.categoryId),
        indicatorsArr: _data.raterIndexRelationList?.map((item) => item.indexId)
      }
      this.queryTemplateList() // 获取绩效模板列表
      this.queryCategoryList() // 获取品类列表
      this.getOwnerList({ text: _data.employeeName }) // 责任人列表
      this.queryIndexByTemplateId() // 获取指标列表
    }
  },
  async created() {
    // 初始化获取组织列表
    await this.queryOrgTree()
  },
  watch: {
    'formObject.orgIdArr': {
      handler() {
        this.$nextTick(() => {
          this.$refs.dialogRef.validateField('orgIdArr')
        })
      }
    }
  },
  methods: {
    // 初始化获取组织列表
    queryOrgTree() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.$set(this.orgFields, 'dataSource', [...res.data])
        }
      })
    },
    // 选择组织
    selectOrganization(e) {
      this.matchItem(this.orgFields.dataSource, e['value'][0])
      // 清空绩效模板
      this.formObject.templateId = ''
      this.formObject.templateName = ''
      // 清空品类
      this.formObject.categoryArr = []
      this.formObject.categoryId = ''
      this.formObject.categoryCode = ''
      this.formObject.categoryName = ''
      // 清空指标
      this.formObject.indicatorsArr = []
      // 清空人
      this.formObject.uid = ''
      this.formObject.employeeId = ''
      this.formObject.employeeName = ''
      this.queryTemplateList() //调用绩效模板列表
      this.queryCategoryList() // 获取品类列表
      // this.cateexquery(); //调用供应商接口
    },
    // 选取组织递归
    matchItem(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.formObject.orgCode = ele.orgCode
          this.formObject.orgId = ele.id
          this.formObject.orgName = ele.name
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.matchItem(ele.childrenList, id)
        }
      })
    },
    // 获取绩效模板
    queryTemplateList() {
      this.$API.performanceManage['categoryTemplateRelation']({
        page: {
          current: 1,
          size: 10000
        },
        condition: 'and',
        rules: [
          {
            field: 'orgId',
            operator: 'equal',
            value: this.formObject.orgId // 传值级联组织id
          }
        ]
      }).then((result) => {
        // 模板去重，一个模板可以对应多个品类
        this.templateList = []
        const list = result.data?.records || []
        list.forEach((item) => {
          if (
            !this.templateList.some((el) => el.templateId === item.templateId) &&
            item.status === 2
          ) {
            this.templateList.push(item)
          }
        })
      })
    },
    // 选择绩效模板点击事件
    selectTemplate(e) {
      console.log('formObject.templateId', this.formObject.templateId)
      let { itemData } = e
      this.formObject.templateId = String(itemData.templateId)
      this.formObject.templateName = itemData.templateName
      // 清空品类
      this.formObject.categoryArr = []
      this.formObject.categoryId = ''
      this.formObject.categoryCode = ''
      this.formObject.categoryName = ''
      // 清空指标
      this.formObject.indicatorsArr = []
      this.queryCategoryList() // 获取品类列表
      this.queryIndexByTemplateId() // 获取绩效模板指标列表
    },
    // 品类选中事件
    selectCategoryas() {
      // this.formObject.categoryArr = list.value
    },
    // 获取品类列表
    queryCategoryList() {
      this.$API.performanceManage['categoryTemplateRelation']({
        page: {
          current: 1,
          size: 10000
        },
        condition: 'and',
        rules: [
          {
            field: 'orgId',
            operator: 'equal',
            value: this.formObject.orgId // 传值级联组织id
          },
          {
            field: 'templateId',
            operator: 'equal',
            value: this.formObject.templateId // 传值模板id
          }
        ]
      }).then((result) => {
        this.categoryList =
          result.data?.records.map((item) => {
            item.codeAndName =
              (item.categoryCode ? `${item.categoryCode} - ` : '') + item.categoryName
            return item
          }) || []
      })
    },
    // 获取责任人下拉列表
    getOwnerList(val) {
      let params = {
        fuzzyName: val.text,
        // organizationId: '1485965381538336769' // this.formObject.orgId
        orgLevelCode: 'ORG05',
        orgType: 'ORG001ADM'
      }
      this.$API.supplierIndex.currentTenantEmployees(params).then((res) => {
        this.ownerList = []
        if (res.code == 200 && res.data != null) {
          this.ownerList = res.data.map((item) => {
            item.ownerName = item.employeeName + item.phoneNum
            return item
          })
          this.noRecordsTemplate = this.$t('没有找到记录')
        }
      })
    },
    // 选择责任人
    selectOwner(e) {
      if (e.itemData) {
        let { itemData } = e
        this.formObject.uid = itemData.uid
        this.formObject.employeeId = itemData.employeeId
        this.formObject.employeeName = itemData.employeeName
        this.formObject.raterAccount = itemData.accountName
        // this.formObject.raterEmail=itemData.email
        // this.formObject.raterTel=itemData.phoneNum
      }
    },
    // 获取指标
    queryIndexByTemplateId() {
      this.$API.performanceManage['queryIndexByTemplateId']({
        id: this.formObject.templateId // 传值级联绩效模板id
      }).then((result) => {
        this.indexList = this.collectIndex(result.data || [])
        // this.indexList = result.data;
        // this.$set(this.indexList, "dataSource", [...result.data]);
      })
    },
    collectIndex(res) {
      let list = []
      const dimensionList = res.buyerAssessTemplateDimensionDTOList
      if (!dimensionList || !Array.isArray(dimensionList)) {
        return []
      }
      dimensionList.forEach((item) => {
        const templateList = item.buyerAssessTemplateIndexDTOList
        Array.isArray(templateList) &&
          templateList.forEach((dto) => {
            if (dto.indexType == 2) {
              list.push(dto)
            } else if (dto.indexType == 1) {
              list = list.concat(dto.buyerAssessTemplateIndexDTOList || [])
            }
          })
      })
      return list
    },
    // 指标选中
    indexChange(val) {
      console.log(val)
    },
    // 确认按钮
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let result = utils.debounce(this.saveFn, 1000)
          result()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    saveFn() {
      let params = JSON.parse(JSON.stringify(this.formObject))
      const categoryArr = this.formObject.categoryArr
      let categoryRelationMap = {}
      if (categoryArr && categoryArr.length > 0) {
        categoryArr.forEach((item) => {
          const exitItem = this.categoryList.find((el) => el.categoryId === item)
          if (exitItem) {
            categoryRelationMap = Object.assign(categoryRelationMap, {
              [exitItem.categoryName]: exitItem.categoryId
            })
          }
        })
      }
      const indexArr = this.formObject.indicatorsArr
      let indexRelationMap = {}
      if (indexArr && indexArr.length > 0) {
        indexArr.forEach((item) => {
          const exitItem = this.indexList.find((el) => el.indexId === item)
          if (exitItem) {
            indexRelationMap = Object.assign(indexRelationMap, {
              [exitItem.indexName]: exitItem.indexId
            })
          }
        })
      }
      params.categoryRelationMap = categoryRelationMap
      params.indexRelationMap = indexRelationMap
      delete params.orgIdArr // 组织
      delete params.supplierEnarr //供应商
      delete params.indicatorsArr //指标
      delete params.categoryArr //指标
      // let updateBuyerAssessPlanCategoryRangeRequest = JSON.parse(
      //   JSON.stringify(this.formObject) //编辑得
      // );
      if (this.editStatus) {
        this.$API.performanceManage.raterUpdate(params).then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('编辑成功'),
              type: 'success'
            })
            this.$emit('confirm-function') //关闭弹窗
            // this.$refs.templateRef.refreshCurrentGridData(); //刷新表格统一方法
          }
        })
      } else {
        this.$API.performanceManage.raterAdd(params).then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('添加成功'),
              type: 'success'
            })
            this.$emit('confirm-function') //关闭弹窗
            // this.$refs.templateRef.refreshCurrentGridData(); //刷新表格统一方法
          }
        })
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-main {
  width: 900px;
  height: 600px;
  box-sizing: border-box;
}
.responsible {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.responsible-children {
  width: 45%;
}
::v-deep .e-searcher {
  width: 100% !important;
}
</style>
