<template>
  <div>
    <mt-button
      v-if="pageType !== 'Detail'"
      css-class="e-flat"
      :is-primary="true"
      @click="addDimension"
      >{{ $t('新增') }}</mt-button
    >
    <a-table
      :columns="pageConfig.columns"
      :data-source="pageConfig.data"
      :pagination="false"
      class="components-table-demo-nested"
      row-key="id"
      size="small"
    >
      <template slot="indexType" slot-scope="text, record">
        <span>{{ record.indexType == 1 ? $t('指标类') : $t('指标') }}</span>
      </template>
      <template slot="dataSource" slot-scope="text, record">
        <span>{{
          record.dataSource == 1 ? $t('系统自动') : record.dataSource == 2 ? $t('手动录入') : ''
        }}</span>
      </template>
      <template slot="defaultScore" slot-scope="text, record">
        <edit-input-cell
          :text="text"
          :page-type="pageType"
          @change="onCellChange(record.id, 'defaultScore', $event)"
        />
      </template>
      <template slot="additional" slot-scope="text, record">
        <span>{{
          record.additional === true ? $t('是') : record.additional === false ? $t('否') : ''
        }}</span>
      </template>
      <template slot="dimensionRow" slot-scope="text, record">
        <span
          v-if="record.indexType == 2"
          style="cursor: pointer; color: #6386ce"
          @click="viewDetail(record)"
          >{{ $t('明细') }}</span
        >
      </template>
      <template slot="weight" slot-scope="text, record">
        <edit-input-cell
          :text="text"
          :page-type="pageType"
          @change="onCellChange(record.id, 'weight', $event)"
        />
      </template>
      <span
        slot="operation"
        slot-scope="text, record, index"
        style="cursor: pointer; color: #6386ce"
        @click="deleteDimension(pageConfig.data, index)"
        >{{ $t('删除') }}</span
      >
      <a-table
        v-if="text.indexType == 1"
        slot="expandedRowRender"
        slot-scope="text"
        :columns="pageConfig.innerColumns"
        :data-source="text.childrenList"
        :pagination="false"
        row-key="id"
        size="small"
      >
        <span slot="index" slot-scope="text, record, index">{{ parseInt(index) + 1 }}</span>
        <template slot="dimensionRow" slot-scope="text, record">
          <span style="cursor: pointer; color: #6386ce" @click="viewDetail(record)">{{
            $t('明细')
          }}</span>
        </template>
        <template slot="dataSource" slot-scope="text, record">
          <span>{{
            record.dataSource == 1 ? $t('系统自动') : record.dataSource == 2 ? $t('手动录入') : ''
          }}</span>
        </template>
        <template slot="additional" slot-scope="text, record">
          <span>{{
            record.additional === true ? $t('是') : record.additional === false ? $t('否') : ''
          }}</span>
        </template>
        <template slot="weight" slot-scope="ctext, crecord">
          <edit-input-cell
            :text="ctext"
            :page-type="pageType"
            @change="onChildCellChange(crecord.id, 'weight', text.childrenList, $event)"
          />
        </template>
        <span
          slot="operation"
          slot-scope="ctext, crecord, index"
          style="cursor: pointer; color: #6386ce"
          @click="deleteInnerDimension(text.childrenList, index)"
          >{{ $t('删除') }}</span
        >
      </a-table>
    </a-table>
    <!-- 新增数据的弹窗 -->
    <add-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
    <!-- 指标明细的弹窗 -->
    <detail-dialog
      v-if="detailDialogShow"
      :dialog-data="detailDialogData"
      @handleDetailDialogShow="handleDetailDialogShow"
    ></detail-dialog>
  </div>
</template>
<script>
import { EditInputCell } from './config/index.js'

export default {
  components: {
    addDialog: require('./addDialog.vue').default,
    detailDialog: require('./detailDialog.vue').default,
    EditInputCell
  },
  props: {
    pageConfig: {
      type: Object,
      default: () => {}
    },
    pageType: {
      // 判断当前页面的类型，Add;Edit;Detail;Version
      type: String,
      require: true,
      default: ''
    }
  },
  model: {
    prop: 'pageConfig',
    event: 'change'
  },
  data() {
    return {
      configs: {},
      addDialogShow: false, // 是否显示弹窗
      dialogData: {}, // 新增弹窗需要传入的数据
      detailDialogShow: false, // 是否显示弹窗
      detailDialogData: {} // 明细弹窗需要传入的数据
    }
  },
  watch: {
    /* 监听传进来得内容 */
    pageConfig: {
      handler(newVal) {
        this.configs = newVal
      },
      immediate: true,
      deep: true
    },
    /* 监听变化内容 传递出去 */
    configs: {
      handler(newVal) {
        this.$emit('change', newVal)
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    viewDetail(row) {
      // 查看明细
      this.detailDialogData = {
        id: row.indexId ? row.indexId : row.id
      }
      this.detailDialogShow = true
    },
    // 父表新增事件
    addDimension() {
      if (!this.pageConfig.dimensionId) {
        this.$toast({ content: this.$t('请选择指标维度'), type: 'warning' })
        return false
      }
      if (!this.pageConfig.orgId) {
        this.$toast({ content: this.$t('请选择组织'), type: 'warning' })
        return false
      }
      // 打开新增弹窗
      this.dialogData = {
        orgId: this.pageConfig.orgId,
        dimensionId: this.pageConfig.dimensionId
      }
      this.addDialogShow = true
    },
    // 父表删除事件
    deleteDimension(data, index) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选数据？')
        },
        success: () => {
          // const dataSource = [...this.pageConfig.data]
          // this.pageConfig.data = dataSource.filter((item) => item.id !== id)
          data.splice(index, 1)
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
        }
      })
    },
    // 父表单元格编辑事件
    onCellChange(key, dataIndex, value) {
      const dataSource = [...this.pageConfig.data]
      const target = dataSource.find((item) => item.id === key)
      if (target) {
        target[dataIndex] = String(value)
        this.pageConfig.data = dataSource
      }
    },
    // 子表单元格编辑事件
    onChildCellChange(key, dataIndex, childrenList, value) {
      const dataSource = [...childrenList]
      const target = dataSource.find((item) => item.id === key)
      if (target) {
        target[dataIndex] = String(value)
        childrenList = dataSource
      }
    },
    // 子表删除事件
    deleteInnerDimension(childrenList, index) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选数据？')
        },
        success: () => {
          childrenList.splice(index, 1)
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
        }
      })
    },
    // 新增弹窗确认新增数据
    confirmSuccess(val) {
      // 新增数据去重
      const arr = val.filter((item) => {
        if (!this.pageConfig.data.some((itm) => itm.id === item.id)) {
          return item
        }
      })
      this.pageConfig.data.push(...arr)
      this.addDialogShow = false
    },
    // 切换新增弹窗显示状态
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    },
    // 切换新增弹窗显示状态
    handleDetailDialogShow(flag) {
      this.detailDialogShow = flag
    }
  }
}
</script>
