<template>
  <div class="rule-box mt-flex">
    <div class="main-container mt-flex-direction-column" style="width: 100%">
      <div class="operate-bars">
        <div class="operate-bar">
          <div class="op-item mt-flex" @click="handleback">
            {{ $t('返回') }}
          </div>
          <div class="op-item mt-flex" @click="handleSave" v-if="!isDetail">
            {{ $t('保存') }}
          </div>
        </div>
        <!-- 顶部主要信息 -->
        <div class="main-info">
          <mt-form ref="listForm" :model="listForm" :rules="formRules">
            <mt-form-item prop="orgCodeList" :label="$t('组织：')">
              <mt-DropDownTree
                :popup-height="500"
                :fields="orgFields"
                :enabled="!isDetail"
                v-model="listForm.orgCodeList"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择组织')"
                @select="orgSelect"
                id="orgTreeId"
              ></mt-DropDownTree>
            </mt-form-item>
            <mt-form-item prop="templateTypeCode" :label="$t('模板类型：')">
              <mt-select
                :data-source="templateTypeData"
                v-model="listForm.templateTypeCode"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :show-clear-button="true"
                :placeholder="$t('请选择模板类型')"
                :disabled="isDetail"
                @change="templateTypeChange"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="name" :label="$t('规则名称：')">
              <mt-input
                v-model="listForm.name"
                float-label-type="Never"
                :disabled="isDetail"
                :max-length="32"
                :placeholder="$t('请输入规则名称')"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
              <mt-input
                v-model="listForm.remark"
                :multiline="true"
                :maxlength="200"
                :disabled="isDetail"
              ></mt-input>
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="rule-list">
        <mt-template-page
          ref="ruleTemplateRef"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { toolbar, columnData, pageConfig } from './config/detail'
// import { flattenDeep } from 'lodash'
export default {
  data() {
    return {
      listForm: {
        orgCodeList: [],
        orgCode: '',
        orgId: '',
        orgName: '',
        templateTypeCode: '',
        templateTypeName: '',
        name: '',
        remark: '',
        levelRuleRelationList: []
      },
      postId: '',
      orgFields: null,
      dimensionId: '',
      disabled: false,
      pageConfig: pageConfig,
      formRules: {
        orgCodeList: [
          {
            required: true,
            message: this.$t('请选择组织'),
            trigger: 'blur'
          }
        ],
        templateTypeCode: [
          {
            required: true,
            message: this.$t('请选择模板类型'),
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: this.$t('请输入规则名称'),
            trigger: 'blur'
          }
        ]
      },
      orgData: [],
      templateTypeData: [],
      levelData: []
    }
  },
  computed: {
    isDetail() {
      return this.$route.name === 'rule-detail'
    },
    isAdd() {
      return this.$route.name === 'rule-add'
    },
    isEdit() {
      return this.$route.name === 'rule-edit'
    }
  },
  watch: {},
  mounted() {
    const _columnData = this.isDetail
      ? columnData.filter((item) => item.type !== 'checkbox')
      : columnData

    this.$set(this.pageConfig[0], 'toolbar', this.isDetail ? [] : toolbar)
    this.$set(this.pageConfig[0].grid, 'columnData', _columnData)
    this.$set(this.pageConfig[0].grid, 'dataSource', this.listForm.levelRuleRelationList)
    this.getOrgList()
    this.getSelectDictList()
    //如果由编辑页进入 初始化渲染数据
    if (!this.isAdd) {
      this.getEditData()
    }
  },
  methods: {
    // 获取任务名称
    async getSelectDictList() {
      this.templateTypeData = await this.getDictItems('MB-TYPE')
      this.levelData = await this.getDictItems('LEVEL')
    },
    // 根据名称获取字典数据
    getDictItems(key) {
      return this.$API.supplierInvitation
        .getDictCode({
          dictCode: key,
          nameLike: ''
        })
        .then((res) => {
          if (res.code === 200) {
            return res.data
          }
        })
    },
    handleback() {
      this.$router.go(-1)
    },
    // 提交
    handleSave() {
      this.$refs.listForm.validate((val) => {
        if (!val) return
        if (this.listForm.levelRuleRelationList?.length <= 0) {
          this.$toast({
            content: this.$t('请填写分类规则列表'),
            type: 'warning'
          })
          return
        }
        const postData = { ...this.listForm }
        const obj = this.postId ? { id: this.postId } : {}
        delete postData.orgCodeList
        this.$API.performanceManage[this.postId ? 'updateRuleList' : 'addRuleList']({
          ...postData,
          ...obj
        }).then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.postId = res.data.id
            // this.$router.go(-1)
          }
        })
      })
    },
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length < 1 && (e.toolbar.id == 'Del' || e.toolbar.id == 'Edit')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleAddRule()
      } else if (e.toolbar.id == 'Edit') {
        this.handleEditRule(_selectGridRecords[0])
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelRule(_selectGridRecords)
      }
    },
    // 添加指标
    handleAddRule() {
      this.$dialog({
        modal: () => import(`./components/index.vue`),
        data: {
          title: this.$t(`新增分类规则`),
          levelData: this.levelData
        },
        success: (val) => {
          let obj = {
            ...val
          }
          // 设置序号
          let index = 0
          this.listForm.levelRuleRelationList.forEach((item) => {
            if (item.lineNo > index) {
              index = item.lineNo
            }
          })
          obj.lineNo = index + 1
          this.listForm.levelRuleRelationList.push(obj)
        }
      })
    },
    // 删除指标
    handleDelRule(arr) {
      let info = arr.map((item) => {
        return item.lineNo
      })
      let list = this.listForm.levelRuleRelationList
      for (let i = 0; i < list.length; i++) {
        if (info.indexOf(list[i].lineNo) != -1) {
          list.splice(i, 1)
          i--
        }
      }
    },
    // 编辑指标
    handleEditRule(item) {
      this.$dialog({
        modal: () => import(`./components/index.vue`),
        data: {
          title: this.$t(`编辑分类规则`),
          levelData: this.levelData,
          info: item
        },
        success: (val) => {
          let obj = {
            ...val
          }
          let index = -1
          this.listForm.levelRuleRelationList.forEach((e, i) => {
            if (e.lineNo == val.lineNo) {
              index = i
            }
          })
          this.listForm.levelRuleRelationList.splice(index, 1, obj)
        }
      })
    },
    // 获取详细数据
    getEditData() {
      this.$API.performanceManage.getRuleDetail({ id: this.$route.query.id }).then((res) => {
        if (res.code === 200) {
          this.listForm = Object.assign({}, this.listForm, res.data)
          // this.listForm.orgCodeList = [res.data.orgId]
          setTimeout(() => {
            // 使用定时器是为了解决组织下拉树状表回显异常的问题
            this.listForm.orgCodeList = [res.data.orgId]
          }, 800)
          this.listForm.levelRuleRelationList.map((item, index) => {
            item.lineNo = index + 1
          })
          this.postId = res.data.id
          this.$set(this.pageConfig[0].grid, 'dataSource', this.listForm.levelRuleRelationList)
        }
      })
    },
    // 手动设置列表数据源
    setRuleDataSource(v, data) {
      if (v === 2) {
        this.$set(this.indexPageConfig[0].grid, 'dataSource', data)
      } else if (v === 1) {
        this.$set(this.indexCategoryPageConfig[0].grid, 'dataSource', data)
      }
    },
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.orgData = res.data
          this.orgFields = {
            dataSource: res.data,
            value: 'id',
            text: 'orgName',
            child: 'childrenList'
          }
        }
      })
    },
    arr2Obj(data) {
      const obj = {}
      data.map((item) => {
        obj[item.value] = item.text
      })
      return obj
    },
    // 根据id匹配树形结构数据
    getItemFromTree(tree) {
      const data = this.flatTreeData(tree || [])
      const obj = data.find((item) => item.id)
      return obj
    },
    // 扁平化树形结构数据
    flatTreeData(tree, children_key = 'childrenList') {
      if (!tree || Object.prototype.toString.call(tree) !== '[object Array]' || tree.length <= 0)
        return []
      return tree.reduce(
        (pre, cur) => pre.concat(cur, this.flatTreeData(cur[children_key], children_key)),
        []
      )
    },
    orgSelect(v) {
      this.listForm = Object.assign({}, this.listForm, {
        orgId: v.itemData?.id,
        orgName: v.itemData?.text
      })
      const obj = this.getItemFromTree(this.orgData, v.itemData?.id)
      this.listForm.orgCode = obj.orgCode
    },
    orgChange(v) {
      this.listForm.orgCode = v.value?.join(',')
    },
    templateTypeChange(v) {
      this.listForm.templateTypeName = v.itemData?.itemName
    }
  }
}
</script>

<style lang="scss" scoped>
.full-width {
  width: 100% !important;
}
/deep/ {
  .mt-drop-down-tree .e-input-group.e-disabled,
  .mt-drop-down-tree .e-input-group.e-disabled .e-input-group-icon.e-icons {
    background: #fafafa !important;
  }
  .mt-select-index {
    float: left;
  }

  .mt-template-page {
    /deep/ .repeat-template {
      padding: 20px;
    }
  }
  .mt-form-item {
    width: calc(33% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
    margin-bottom: 20px;
    .label {
      margin-bottom: 6px;
    }
  }
}

.rule-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .main-container {
    flex: 1;
    background: #fff;
    padding: 0 20px;

    .operate-bars {
      border-radius: 8px 8px 0 0;
      .operate-bar {
        height: 60px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        color: #4f5b6d;
        .op-item {
          cursor: pointer;
          align-items: center;
          margin-right: 20px;
          align-items: center;
          background-color: rgba(0, 0, 0, 0);
          border-color: rgba(0, 0, 0, 0);
          color: #00469c;
        }
      }
    }

    .rule-list {
      flex: 1;
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      margin-top: 20px;
    }
  }
}
</style>
