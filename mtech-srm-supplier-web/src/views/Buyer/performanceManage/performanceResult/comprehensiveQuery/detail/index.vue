<template>
  <div class="performance-cotainer">
    <performance-form :form-config="formConfig" />
    <div v-for="(item, index) in performanceData" :key="index">
      <!-- 折叠框通过设置id关联内容 -->
      <mt-accordion
        class="accordion-area"
        :data-source="[
          {
            header: `${item.info.dimensionName}`,
            content: `#PLATFORM-ENTERPRISE-DETAIL-${index}-CONTENT`,
            expanded: true
          }
        ]"
      >
      </mt-accordion>
      <div class="full-height" :id="`PLATFORM-ENTERPRISE-DETAIL-${index}-CONTENT`">
        <div class="table-header-desc">
          <span>{{ $t('原始分数：') + item.info.dimensionWeight }}</span>
          <span>{{ $t('得分：') + item.info.dimensionTypeScore }}</span>
          <span>{{ $t('排名：') + item.info.dimensionTypeRank }}</span>
        </div>
        <mt-template-page ref="templateRef" :template-config="pageConfig(item.indexScoreList)" />
      </div>
    </div>
  </div>
</template>

<script>
import MtAccordion from '@mtech-ui/accordion'
import performanceForm from './components/performanceForm.vue'
import Vue from 'vue'
import { columns } from './config/index'
export default {
  components: {
    performanceForm,
    MtAccordion
  },
  data() {
    return {
      formConfig: {
        orgName: this.$t('组织'), // 组织
        templateCode: 'temp123456', // 绩效模板编码
        visionCode: '2.0', // 绩效模板版本号
        supplierCode: '12313432412', // 供应商编码
        categoryName: '1', // 品类
        comprehensiveScore: '98', // 综合绩效得分
        supplierName: this.$t('供应商名称'), // 供应商名称
        assessMonth: '2019-03', // 绩效月份
        comprehensiveRank: '2', // 绩效排名
        calcDate: '2019-04-14' // 计算时间
      },
      performanceData: [
        // {
        //   info: {
        //     dimensionName: 'dimensionName',
        //     dimensionWeight: 'dimensionWeight',
        //     dimensionTypeScore: 'dimensionTypeScore',
        //     dimensionTypeRank: 'dimensionTypeRank'
        //   },
        //   indexScoreList: [{ dataSource: 2 }]
        // }
      ]
    }
  },
  created() {
    this.getDetailData()
  },
  methods: {
    getDetailData() {
      this.getComprehensiveDetailApi()(this.$route.query.id).then((res) => {
        const { code, data } = res
        if (code == 200) {
          this.formConfig = data
          this.performanceData = data.dimensionScoreDetailList
        }
      })
    },
    getComprehensiveDetailApi() {
      if (this.$route.query.isPur === 'true') {
        return this.$API.performanceManage.getComprehensiveDetail
      }
      return this.$API.performanceManage.getSupComprehensiveDetail
    },
    pageConfig(item) {
      const gridConfig = [
        {
          grid: {
            height: 'calc(100vh - 510px)',
            allowPaging: false,
            allowSorting: false,
            columnData: columns(item.indexType),
            useToolTemplate: false,
            dataSource: item
          }
        }
      ]
      // indexType判断是指标还是指标类 子表被废弃了，保留着可能后续用的上
      if (item.indexType == 1) {
        gridConfig[0]['grid']['detailTemplate'] = () => {
          return {
            template: {
              extends: Vue.component('childrenGrid', {
                template: `
                  <div style="min-height: 150px">
                    <mt-template-page
                      style="height: 100%"
                      ref="childRef"
                      :template-config="childrenConfig(data)"
                    >
                    </mt-template-page>
                  </div>
                `,
                data() {
                  return {
                    childrenConfig: (data) => {
                      return [
                        {
                          useToolTemplate: false,
                          useBaseConfig: false,
                          grid: {
                            height: 'auto',
                            allowPaging: false,
                            allowSorting: false,
                            columnData: columns(),
                            dataSource: data.childrenList
                          }
                        }
                      ]
                    }
                  }
                }
              })
            }
          }
        }
      }
      return gridConfig
    }
  }
}
</script>

<style lang="scss" scoped>
.performance-cotainer {
  background: #fff;
  height: auto;
  overflow: hidden;
  margin-top: 20px;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;
  .accordion-area {
    ::v-deep.e-acrdn-header {
      background: #eee !important;
    }
  }
}
.full-height {
  height: 100%;
  ::v-deep.e-content {
    height: unset !important;
  }
  .table-header-desc {
    padding-bottom: 10px;
    span {
      margin-right: 20px;
    }
  }
}
</style>
