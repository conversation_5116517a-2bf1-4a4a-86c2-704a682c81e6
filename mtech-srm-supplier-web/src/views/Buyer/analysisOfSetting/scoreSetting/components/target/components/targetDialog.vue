<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @open="onOpen" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules">
        <mt-form-item prop="orgIdArr" :label="$t('公司：')">
          <mt-DropDownTree
            :key="fieldsarr.dataSource.length"
            v-model="formObject.orgIdArr"
            :placeholder="$t('请选择公司：')"
            :popup-height="500"
            :fields="fieldsarr"
            @select="selectCompany"
            id="baseTreeSelect"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('原因：')">
          <mt-input
            :max-length="100"
            type="text"
            :placeholder="$t('请输入原因')"
            v-model="formObject.remark"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierEnterpriseId" :label="$t('供应商：')">
          <mt-select
            :allow-filtering="true"
            v-model="formObject.supplierEnterpriseId"
            :data-source="planeArrList"
            :fields="{
              text: 'supplierEnterpriseName',
              value: 'supplierEnterpriseId'
            }"
            :show-clear-button="true"
            @select="selectPlan"
            :placeholder="$t('请选择供应商')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      // 表单验证
      rules: {
        orgIdArr: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ],
        supplierEnterpriseId: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ]
      },
      formInfo: {
        orgId: '', //公司id
        companyId: '', //计划id 取计划模板
        planId: '' //计划id 取品类
      },
      fieldsarr: {
        dataSource: [], //公司树下拉数组
        value: 'id',
        text: 'name',
        child: 'children',
        code: 'orgCode'
      },
      // 弹窗确认事件
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      formObject: {
        orgCode: '', //组织机构编码
        orgId: '', //组织机构id
        orgName: '', //组织机构名称
        partnerArchiveId: '', //供应商档案id
        partnerRelationId: '', //	供应商关系id
        remark: '', //备注
        supplierEnterpriseCode: '', //供应商企业编码
        supplierEnterpriseId: '', //	供应商企业id
        supplierEnterpriseName: '', //供应商企业名称
        supplierCode: '' //供应商后加编码
      },
      editStatus: false,
      periodList: [], // 新增公司data
      planeArrList: [] //供应商下拉数组
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.$nextTick(() => {
        // this.$set(this.buttons[1].buttonModel, "content", "保存");
        this.buttons = [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('保存') }
          }
        ]
      })
      let _data = { ...this.modalData.data }
      this.formObject = {
        id: _data.id, //配置Id
        dimensionName: _data.dimensionName, //名称
        remark: _data.remark //备注
      }
    }
  },
  async created() {
    // 初始化获取公司列表
    this.TreeByAccount()
  },
  watch: {
    'formObject.orgIdArr': {
      handler() {
        this.$refs.dialogRef.validateField('orgIdArr')
      }
    }
  },
  methods: {
    // 获取供应商列表
    cateexquery() {
      let pamrsform = {
        page: {
          current: '1',
          size: '500'
        },
        orgId: this.formInfo.orgId, // 传值级联公司id
        status: [10, 20, 30], //10"合格"，20"冻结"，30"黑名单"
        needSupplierCode: 1
      }

      this.$API.analysisOfSetting['cateexquery'](pamrsform).then((result) => {
        this.planeArrList = result.data.records
      })
    },
    // 初始化获取公司列表
    TreeByAccount() {
      this.$API.supplierIndex
        .getOrgTree({
          orgLevelCode: 'ORG02'
        })
        .then((res) => {
          if (res.code == 200) {
            this.$set(this.fieldsarr, 'dataSource', [...res.data])
          }
        })
      // this.$API.analysisOfSetting["TreeByAccount"]({}).then((res) => {
      //   this.$set(this.fieldsarr, "dataSource", [...res.data]);
      // });
    },
    // 选取公司递归
    fn(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.formInfo.orgId = ele.id
          this.formObject.orgCode = ele.orgCode
          this.formObject.orgId = ele.id
          this.formObject.orgName = ele.name
          return
        }
        if (ele.children && ele.children.length > 0) {
          this.fn(ele.children, id)
        }
      })
    },
    // 选择公司
    selectCompany(e) {
      if (e.itemData) {
        let { itemData } = e
        this.fn(this.fieldsarr.dataSource, itemData.id)
        this.cateexquery() //调用供应商接口
      }
    },
    // 选择供应商点击事件
    selectPlan(e) {
      let { itemData } = e

      this.formObject.supplierEnterpriseCode = itemData.supplierCode //获取品类传值id

      this.formObject.supplierEnterpriseId = itemData.supplierEnterpriseId
      this.formObject.partnerArchiveId = itemData.partnerArchiveId
      this.formObject.supplierEnterpriseName = itemData.supplierEnterpriseName
      this.formObject.supplierCode = itemData.supplierCode
    },

    // 确认按钮
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let addBuyerAssessExcludeRequest = JSON.parse(JSON.stringify(this.formObject))
          delete addBuyerAssessExcludeRequest.orgIdArr // 公司
          // let updateBuyerAssessPlanCategoryRangeRequest = JSON.parse(
          //   JSON.stringify(this.formObject) //编辑得
          // );
          if (this.editStatus) {
            // console.log('维度设置--编辑', updateBuyerAssessPlanCategoryRangeRequest)
            // this.$API.analysisOfSetting
            //   .categoryStupdate(updateBuyerAssessPlanCategoryRangeRequest)
            //   .then((res) => {
            //     if (res.code == 200) {
            //       this.$emit("confirm-function"); //关闭弹窗
            //       this.$refs.templateRef.refreshCurrentGridData(); //刷新表格统一方法
            //     }
            //   });
          } else {
            console.log('维度设置--新增', addBuyerAssessExcludeRequest)
            this.$API.analysisOfSetting
              .cateexceptionAdd(addBuyerAssessExcludeRequest)
              .then((res) => {
                if (res.code == 200) {
                  this.$emit('confirm-function') //关闭弹窗
                  this.$refs.templateRef.refreshCurrentGridData() //刷新表格统一方法
                }
              })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    onOpen(args) {
      args.preventFocus = true
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.browse {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    background: transparent;
    opacity: 0;
  }
  .show-input {
    width: 90%;
    height: 80%;
    background: transparent;
  }
}
</style>
