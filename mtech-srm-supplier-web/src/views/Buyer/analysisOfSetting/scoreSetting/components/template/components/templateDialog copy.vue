<template>
  <mt-dialog
    ref="dialog"
    :buttons="buttons"
    :header="header"
    class="dialog-main"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules">
        <mt-form-item prop="orgIdArr" :label="$t('公司：')">
          <mt-DropDownTree
            v-if="fieldsarr.dataSource.length > 0"
            v-model="formObject.orgIdArr"
            :placeholder="$t('请选择公司')"
            :popup-height="500"
            :fields="fieldsarr"
            @select="selectCompany"
            id="baseTreeSelect"
          ></mt-DropDownTree>
          <mt-select v-else :placeholder="$t('请选择公司')" :data-source="[]"></mt-select>
        </mt-form-item>
        <mt-form-item prop="planId" :label="$t('计划：')">
          <mt-select
            v-model="formObject.planId"
            :data-source="planeArrList"
            :fields="{ text: 'planName', value: 'id' }"
            :show-clear-button="true"
            @select="selectPlan"
            :placeholder="$t('公司下面的分析计划')"
          ></mt-select>
        </mt-form-item>
        <!-- 原来的框架 -->
        <mt-form-item prop="categoryDTOList" :label="$t('品类：')">
          <mt-DropDownTree
            v-if="fields.dataSource.length"
            :filter-bar-placeholder="$t('请输入关键字')"
            :allow-filtering="true"
            :fields="fields"
            :show-check-box="true"
            v-model="formObject.categoryDTOList"
            id="checkboxTreeSelect"
            :placeholder="$t('请选择品类')"
            :key="fields.key"
            @input="selectCategoryas"
          ></mt-DropDownTree>
          <mt-select v-else :placeholder="$t('请选择品类')" :data-source="[]"></mt-select>
        </mt-form-item>
        <mt-form-item prop="raterId" :label="$t('责任人')">
          <div class="responsible">
            <mt-select
              v-model="formObject.deptId"
              :data-source="personneloplist"
              :fields="{ text: 'orgName', value: 'id' }"
              :show-clear-button="true"
              :placeholder="$t('请选择所属部门')"
              @change="stationGet"
              class="responsible-children"
            ></mt-select>
            <mt-select
              v-model="formObject.raterId"
              id="dropDownTreeCom"
              :fields="{ text: 'employeeName', value: 'userId' }"
              :data-source="anodrslist"
              filter-bar-:placeholder="$t('Search')"
              :allow-filtering="true"
              :placeholder="$t('部门负责人员')"
              :show-clear-button="false"
              @select="downTreeSelect"
              class="responsible-children"
            ></mt-select>
          </div>
        </mt-form-item>

        <mt-form-item prop="indicatorsarr" :label="$t('指标：')">
          <mt-multi-select
            v-model="formObject.indicatorsarr"
            @change="indexChange"
            :data-source="ndexByPlanId"
            :show-clear-button="true"
            :placeholder="$t('请选择模板下的指标')"
            :fields="{
              text: 'indexName',
              value: 'indexCode'
            }"
          ></mt-multi-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      // 表单验证
      rules: {
        indicatorsarr: [
          {
            required: true,
            message: this.$t('请选择指标'),
            trigger: 'blur'
          }
        ],
        raterId: [
          {
            required: true,
            message: this.$t('请选择责任人'),
            trigger: 'blur'
          }
        ],
        categoryDTOList: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        planId: [
          {
            required: true,
            message: this.$t('请选择计划'),
            trigger: 'blur'
          }
        ],
        orgIdArr: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ]
      },
      fields: {
        dataSource: [], //品类树下拉数组
        value: 'categoryCode',
        text: 'categoryName',
        child: 'childrens',
        key: 1
      },
      fieldsarr: {
        dataSource: [], //公司树下拉数组
        value: 'id',
        text: 'name',
        child: 'children',
        code: 'orgCode'
      },

      supplierarr: [],
      ndexByPlanId: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('新增') }
        }
      ],
      formInfo: {
        orgId: '', //公司id
        companyId: '', //计划id 取计划模板
        planId: '' //计划id 取品类
      },
      formObject: {
        orgIdArr: [],
        orgCode: '', //组织机构编码   0
        orgId: '', //组织机构id   0
        orgName: '', //组织机构名称  0

        planCode: '', //计划编码 0
        planId: '', //计划id  0
        planName: '', //计划名称  0

        // 品类
        categoryDTOList: [],
        categoryCode: '', //品类编码
        categoryId: '', //品类id
        categoryName: '', //品类名称

        // 供应商
        supplierDTOList: [],

        // 责任人
        deptId: '', //评分人部门id
        deptName: '', //评分人部门名称
        raterId: '', //责任人id
        raterName: '', //	责任人姓名

        // 指标
        indicatorsarr: [],
        indexDTOList: []
      },

      editStatus: false,
      planeArrList: [], //计划下拉数组
      personneloplist: [], //公司下部门数组
      anodrslist: [] //部门下人员接口
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.$nextTick(() => {
        this.buttons = [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('保存') }
          }
        ]
      })
      let _data = { ...this.modalData.data }
      this.formObject = {
        id: _data.id, //配置Id
        dimensionName: _data.dimensionName, //名称
        remark: _data.remark //备注
      }
    }
  },
  async created() {
    // 初始化获取公司列表
    await this.TreeByAccount()
  },
  watch: {
    'formObject.orgIdArr': {
      handler() {
        this.$refs.dialogRef.validateField('orgIdArr')
      }
    }
  },
  methods: {
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },

    // 品类选中事件
    selectCategoryas(e) {
      let newSelect = e[e.length - 1]
      console.log(newSelect)
      this.formObject.categoryId = ''
      this.formObject.categoryCode = ''
      this.formObject.categoryName = ''
      this.getCategory(this.fields.dataSource, newSelect, this.formObject)
      this.$refs.dialogRef.validateField('categoryDTOList')
    },
    getCategory(data, str, formObject) {
      data.forEach((item) => {
        if (str == item.categoryCode) {
          if (item.leafNode == 1) {
            formObject.categoryId = item.id
            formObject.categoryCode = item.categoryCode
            formObject.categoryName = item.categoryName
          } else {
            this.$toast({ content: this.$t('请选择最末级'), type: 'warning' })
            let oldData = this.formObject.categoryDTOList.filter((i) => {
              return i !== item.categoryCode
            })
            this.formObject.categoryDTOList = oldData.slice()
          }
        }
        if (item.childrens.length > 0) {
          this.getCategory(item.childrens, str, formObject)
        }
      })
    },
    // 获取计划品类列表
    PlanTemplateRangeCat() {
      this.$API.analysisOfSetting['PlanTemplateRangeCat']({
        planId: this.formInfo.planId
      }).then((result) => {
        this.fields.dataSource = []
        this.$set(this.fields, 'dataSource', [...result.data])
        this.$set(this.fields, 'key', this.randomString())
      })
    },
    indexChange(val) {
      this.formObject.indexDTOList = []
      this.ndexByPlanId.forEach((item) => {
        val.value.forEach((e) => {
          if (item.indexCode == e) {
            this.formObject.indexDTOList.push({
              indexCode: item.indexCode,
              indexName: item.indexName,
              indexVersion: item.indexVersion,
              indexId: item.id
            })
          }
        })
      })
    },

    // 选择计划点击事件
    selectPlan(e) {
      let { itemData } = e
      this.formInfo.planId = String(itemData.id) //获取品类传值id
      this.formObject.planId = String(itemData.id)
      this.formObject.planCode = itemData.planCode
      this.formObject.planName = itemData.planName
      this.formObject.supplierDTOList.forEach((item) => {
        item.planId = String(itemData.id)
      })
      // 清空品类
      this.formObject.categoryDTOList = []
      this.formObject.categoryId = ''
      this.formObject.categoryCode = ''
      this.formObject.categoryName = ''
      // 清空指标
      this.formObject.indicatorsarr = []
      this.formObject.indexDTOList = []
      this.PlanTemplateRangeCat() // 获取计划品类列表
      this.selectIndexByPlanId() // 获取计划指标列表
    },
    // 初始化获取公司列表
    TreeByAccount() {
      this.$API.analysisOfSetting['TreeByAccount']({}).then((res) => {
        this.$set(this.fieldsarr, 'dataSource', [...res.data])
      })
    },
    // 获取计划模板
    queryPlanByCompany() {
      this.$API.analysisOfSetting['queryPlanByCompany']({
        companyId: this.formInfo.orgId // 传值级联公司id
      }).then((result) => {
        this.planeArrList = result.data
      })
    },
    // 获取计划下指标
    selectIndexByPlanId() {
      this.$API.analysisOfSetting['selectIndexByPlanId']({
        planId: this.formInfo.planId // 传值级联计划id
        // planId: "1486285107652083714", // 传值级联计划id
      }).then((result) => {
        this.ndexByPlanId = result.data
        // this.ndexByPlanId = result.data;
        // this.$set(this.ndexByPlanId, "dataSource", [...result.data]);
      })
    },
    // 获取部门下评分人
    getOrganizationEmployees() {
      let parms = {
        orgId: this.formObject.deptId
      }
      this.$API.analysisOfSetting['getOrganizationEmployees'](parms).then((result) => {
        this.anodrslist = result.data
      })
    },
    // 获取公司下部门列表
    getChildrenDepartmentOrganization() {
      let parms = {
        onlyCurrentLevel: 0,
        organizationId: this.formInfo.orgId
      }
      this.$API.analysisOfSetting['getChildrenDepartmentOrganization'](parms).then((result) => {
        this.personneloplist = result.data
      })
    },
    // 选取公司递归
    fn(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.formInfo.orgId = ele.id
          this.formObject.orgCode = ele.orgCode
          this.formObject.orgId = ele.id
          this.formObject.orgName = ele.name
          return
        }
        if (ele.children && ele.children.length > 0) {
          this.fn(ele.children, id)
        }
      })
    },
    // 选择公司
    selectCompany(e) {
      if (e.itemData) {
        let { itemData } = e
        this.fn(this.fieldsarr.dataSource, itemData.id)
        // 清空计划
        this.formInfo.planId = '' //获取品类传值id
        this.formObject.planId = ''
        this.formObject.planCode = ''
        this.formObject.planName = ''
        // 清空供应商
        this.formObject.supplierDTOList = []
        // 清空部门
        this.formObject.deptId = ''
        this.formObject.deptName = ''
        // 清空品类
        this.formObject.categoryDTOList = []
        this.formObject.categoryId = ''
        this.formObject.categoryCode = ''
        this.formObject.categoryName = ''
        // 清空指标
        this.formObject.indicatorsarr = []
        this.formObject.indexDTOList = []
        // 清空人
        this.formObject.raterId = ''
        this.formObject.raterName = ''
        this.queryPlanByCompany() //调用计划接口
        this.getChildrenDepartmentOrganization() //调用公司下部门接口
      }
    },
    downTreeSelect(e) {
      if (e.itemData) {
        let { itemData } = e
        this.formObject.raterId = itemData.userId
        this.formObject.raterName = itemData.employeeName
        this.formObject.raterEmail = itemData.email
        this.formObject.raterTel = itemData.phoneNum
      }
    },
    // 部门下拉选中事件
    stationGet(e) {
      if (e.itemData) {
        let { itemData } = e
        this.formObject.deptId = itemData.id
        this.formObject.deptName = itemData.orgName
        // 清空人
        this.formObject.raterId = ''
        this.formObject.raterName = ''
        this.getOrganizationEmployees() //调用部门下评分人接口
      }
    },
    // 确认按钮
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let addBuyerAssessPlanRaterRequest = JSON.parse(JSON.stringify(this.formObject))
          delete addBuyerAssessPlanRaterRequest.orgIdArr // 公司
          delete addBuyerAssessPlanRaterRequest.supplierEnarr //供应商
          delete addBuyerAssessPlanRaterRequest.indicatorsarr //指标
          delete addBuyerAssessPlanRaterRequest.categoryArr //指标
          // let updateBuyerAssessPlanCategoryRangeRequest = JSON.parse(
          //   JSON.stringify(this.formObject) //编辑得
          // );
          if (this.editStatus) {
            // this.$API.analysisOfSetting
            //   .categoryStupdate(updateBuyerAssessPlanCategoryRangeRequest)
            //   .then((res) => {
            //     if (res.code == 200) {
            //       this.$emit("confirm-function"); //关闭弹窗
            //       this.$refs.templateRef.refreshCurrentGridData(); //刷新表格统一方法
            //     }
            //   });
          } else {
            this.$API.analysisOfSetting
              .cateexaddpoper(addBuyerAssessPlanRaterRequest)
              .then((res) => {
                if (res.code == 200) {
                  this.$emit('confirm-function') //关闭弹窗
                  this.$refs.templateRef.refreshCurrentGridData() //刷新表格统一方法
                }
              })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-main {
  width: 900px;
  height: 600px;
  box-sizing: border-box;
}
.responsible {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.responsible-children {
  width: 45%;
}
::v-deep .e-searcher {
  width: 100% !important;
}
</style>
