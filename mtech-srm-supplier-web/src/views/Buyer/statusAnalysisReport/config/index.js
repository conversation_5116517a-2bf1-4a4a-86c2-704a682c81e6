import { i18n } from '@/main.js'
import utils from '@/utils/utils'
import Vue from 'vue'
import inputView from '../components/inputView.vue'
const toolbar = [{ id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') }]
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'index',
    headerText: i18n.t('序号'),
    template: function () {
      return {
        template: Vue.component('indexTemplate', {
          template: `<div>{{Number(data.index) + 1}}</div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'orgName',
    headerText: i18n.t('所属公司'),
    searchOptions: {
      elementType: 'remote-autocomplete',
      renameField: 'orgCode',
      fields: { text: 'orgName', value: 'orgCode' },
      params: {
        organizationLevelCodes: ['ORG01', 'ORG02']
      },
      multiple: true,
      operator: 'in',
      url: '/masterDataManagement/tenant/organization/specified-level-paged-query'
    }
  },
  {
    width: 150,
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编码-SRM'),
    searchOptions: {
      elementType: 'remote-autocomplete',
      fields: { text: 'supplierName', value: 'partnerCode' },
      multiple: true,
      operator: 'in',
      url: '/masterDataManagement/tenant/supplier/paged-query',
      searchFields: ['supplierCode', 'partnerCode', 'supplierName']
    }
  },
  {
    width: 150,
    field: 'supplierCode',
    headerText: i18n.t('供应商编码-SAP'),
    searchOptions: {
      elementType: 'remote-autocomplete',
      fields: { text: 'supplierName', value: 'supplierCode' },
      multiple: true,
      operator: 'in',
      url: '/masterDataManagement/tenant/supplier/paged-query',
      searchFields: ['supplierCode', 'partnerCode', 'supplierName']
    }
  },
  {
    width: 150,
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: 150,
    field: 'supplierShortName',
    headerText: i18n.t('供应商简称')
  },
  {
    field: 'status',
    headerText: i18n.t('供应商状态'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('注册'),
        2: i18n.t('潜在'),
        10: i18n.t('合格'),
        20: i18n.t('冻结'),
        30: i18n.t('黑名单'),
        40: i18n.t('退出')
      }
    }
  },
  {
    field: 'supplierType',
    headerText: i18n.t('供应商类型'),
    valueConverter: {
      type: 'map',
      map: {
        logisticsProvider: i18n.t('物流商'),
        commonPurchaseSupplier: i18n.t('通采'),
        noBiddingPurchaseSupplier: i18n.t('非采'),
        '': '--'
      }
    }
  },
  {
    field: 'categoryType',
    headerText: i18n.t('供应类型'),
    valueConverter: {
      type: 'map',
      map: {
        2: i18n.t('代理'),
        1: i18n.t('原厂')
      }
    }
  },
  {
    field: 'supplier',
    headerText: i18n.t('供应商层级')
  },
  {
    field: 'categoryTypeCode',
    headerText: i18n.t('品类类型'),
    valueConverter: {
      type: 'map',
      map: {
        product: i18n.t('通采'),
        common: i18n.t('非采'),
        null: '--'
      }
    }
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'categoryStatus',
    headerText: i18n.t('品类状态'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('注册'),
        2: i18n.t('潜在'),
        3: i18n.t('新合格'),
        4: i18n.t('临时'),
        10: i18n.t('合格'),
        11: i18n.t('预合格'),
        20: i18n.t('失效'),
        40: i18n.t('退出')
      }
    }
  },
  {
    field: 'categoryLevel',
    headerText: i18n.t('品类等级'),
    valueConverter: {
      type: 'map',
      map: {
        A: i18n.t('A类'),
        B: i18n.t('B类'),
        C: i18n.t('C类')
      }
    }
  },
  {
    field: 'develop',
    headerText: i18n.t('采购开发')
  },
  {
    field: 'name1',
    headerText: i18n.t('总经理')
  },
  {
    field: 'mobile1',
    headerText: i18n.t('电话(总经理)'),
    template: () => ({ template: inputView })
  },
  {
    field: 'mail1',
    headerText: i18n.t('邮箱(总经理)'),
    template: () => ({ template: inputView })
  },
  {
    field: 'name2',
    headerText: i18n.t('质量负责人')
  },
  {
    field: 'mobile2',
    headerText: i18n.t('电话(质量)'),
    template: () => ({ template: inputView })
  },
  {
    field: 'mail2',
    headerText: i18n.t('邮箱(质量)'),
    template: () => ({ template: inputView })
  },
  {
    field: 'name3',
    headerText: i18n.t('业务负责人')
  },
  {
    field: 'mobile3	',
    headerText: i18n.t('电话(业务)')
  },
  {
    field: 'mail3',
    headerText: i18n.t('邮箱(业务)')
  },
  {
    field: 'detailAddress',
    headerText: i18n.t('供应商地址')
  },
  {
    field: 'applyerName',
    headerText: i18n.t('申请人'),
    ignore: true
  },
  {
    field: 'createDate',
    headerText: i18n.t('申请日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formateTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    },
    ignore: true
  }
]
export const pageConfig = [
  {
    gridId: '98f7ae0a-1bc5-4199-92b6-1b373b323634',
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData,
      asyncConfig: {
        url: '/supplier/tenant/buyer/process/perspective/listQualityStatus'
        // params: {
        //   page: {
        //     current: 1,
        //     size: 20
        //   }
        //   // condition: 'and',
        //   // defaultRules: [
        //   //   // {
        //   //   //   label: "供应商编号-SRM",
        //   //   //   field: "supplierInternalCode",
        //   //   //   type: "string",
        //   //   //   operator: "contains",
        //   //   //   value: "CO00000002"
        //   //   // },
        //   //   // {
        //   //   //   label: "供应商编号-SAP",
        //   //   //   field: "supplierCode",
        //   //   //   type: "string",
        //   //   //   operator: "contains",
        //   //   //   value: "M1001122"
        //   //   // }
        //   // ]
        // }
      },
      frozenColumns: 3
    }
  }
]
