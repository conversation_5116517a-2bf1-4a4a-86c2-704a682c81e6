import Vue from 'vue'
import { cloneDeep } from 'lodash'
import { i18n } from '@/main'
export const listColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编号-SRM'),
    width: 150,
    cellTools: []
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编号-SAP'),
    width: 140
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类'),
    width: 140,
    valueConverter: {
      type: 'function',
      filter: (val, data) => {
        if (!val) {
          return ''
        } else {
          return data.categoryCode + '-' + val
        }
      }
    }
  },
  {
    field: 'orgName',
    headerText: i18n.t('公司')
  },
  {
    field: 'categoryType',
    headerText: i18n.t('供应类型'),
    width: 100,
    valueConverter: {
      type: 'map',
      map: {
        2: i18n.t('代理'),
        1: i18n.t('原厂')
      }
    }
  },
  {
    field: 'currentStageName',
    headerText: i18n.t('品类阶段'),
    width: 100
  },
  // {
  //   field: 'pendingCount',
  //   headerText: i18n.t('待处理任务')
  //   // template: () => {
  //   //   return {
  //   //     template: Vue.component("operate-template", {
  //   //       template: `
  //   //         <div class="tp-box" style="color: #00469C">{{ countRate }}</div>
  //   //       `,
  //   //       data: function () {
  //   //         return { data: {} };
  //   //       },
  //   //       computed: {
  //   //         countRate() {
  //   //           return this.data.totalCount - this.data.successCount;
  //   //         },
  //   //       },
  //   //     }),
  //   //   };
  //   // },
  // },
  {
    field: 'authProjectCode',
    headerText: i18n.t('品类认证编号'),
    cellTools: []
  },
  {
    field: 'sceneName',
    headerText: i18n.t('引入场景'),
    width: 120
  },
  {
    field: 'status',
    headerText: i18n.t('品类状态'),
    width: 100,
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('注册'),
        2: i18n.t('潜在'),
        3: i18n.t('新合格'),
        4: i18n.t('临时'),
        10: i18n.t('合格'),
        11: i18n.t('预合格'),
        20: i18n.t('失效'),
        40: i18n.t('退出')
      }
    }
  },
  {
    field: 'certificationStatus',
    headerText: i18n.t('认证状态'),
    width: 90,
    template: function () {
      return {
        template: Vue.component('projectPreparation', {
          template: `<div>{{status}}</div>`,
          data() {
            return {
              status: ''
            }
          },
          mounted() {
            // map: { 0: i18n.t("未认证"), 1: i18n.t("认证中"), 2: i18n.t("认证完成"), 3: i18n.t("终止认证") },
            let currentBuAuthProjectStage = cloneDeep(this.data.currentBuAuthProjectStage)
            if (!currentBuAuthProjectStage) {
              this.status = this.$t('未认证')
              return
            }
            if (!currentBuAuthProjectStage.status) {
              this.status = this.$t('未认证')
              return
            }
            this.status = this.$t('未认证')
            this.status =
              currentBuAuthProjectStage.status == '0'
                ? this.$t('未认证')
                : currentBuAuthProjectStage.status == 1
                ? this.$t('认证中')
                : currentBuAuthProjectStage.status == 2
                ? this.$t('认证完成')
                : currentBuAuthProjectStage.status == 3
                ? this.$t('终止认证')
                : this.$t('未认证')
          },
          methods: {}
        })
      }
    },
    ignore: true
  },
  {
    width: 150,
    field: 'projectPreparation',
    headerText: i18n.t('立项准备'),
    template: function () {
      return {
        template: Vue.component('projectPreparation', {
          template: `<div :class='statusColor'>{{status}}</div>`,
          data() {
            return {
              status: '',
              statusColor: ''
            }
          },
          mounted() {
            let currentBuAuthProjectStage = cloneDeep(this.data.currentBuAuthProjectStage)
            if (!currentBuAuthProjectStage) {
              //判断一条流程也没有
              this.status = '/'
              return
            }
            let mapStageList = currentBuAuthProjectStage.stageList.map((item) => item.stageName)
            if (mapStageList.indexOf(this.data.column.headerText) == -1) {
              //判断没有这一条流程
              this.status = '/'
              return
            }
            let _taskList =
              currentBuAuthProjectStage.stageList[
                Number(mapStageList.indexOf(this.data.column.headerText))
              ].taskList
            let optional = [], //0
              required = [], //1
              forbidden = [] //2
            _taskList.forEach((item) => {
              if (item.flag == '0') {
                optional.push(item)
                //可选
              } else if (item.flag == '1') {
                //必选
                required.push(item)
              } else if (item.flag == '2') {
                //禁选
                forbidden.push(item)
              }
            })

            if (forbidden.length == _taskList.length) {
              this.status = '/'
            } else if (optional.length == _taskList.length) {
              this.status = this.$t('可选')
              this.statusColor = 'optional'
            } else if (required.length == _taskList.length) {
              let _complete = 0,
                _awaitFill = 0
              required.forEach((item) => {
                if (item.status == '40') {
                  _complete += 1
                }
                if (item.status == '10') {
                  _awaitFill += 1
                }
              })
              if (_complete == _taskList.length) {
                this.status = this.$t('已完成')
                this.statusColor = 'complete'
              } else if (_awaitFill == _taskList.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            } else if (
              forbidden.length > 0 &&
              optional.length &&
              forbidden.length + optional.length == _taskList.length
            ) {
              this.status = this.$t('可选')
              this.statusColor = 'optional'
            } else {
              let _complete = 0,
                _awaitFill = 0
              required.forEach((item) => {
                if (item.status == '40') {
                  _complete += 1
                }
                if (item.status == '10') {
                  _awaitFill += 1
                }
              })
              if (_complete == required.length) {
                this.status = this.$t('已完成')
                this.statusColor = 'complete'
              } else if (_awaitFill == required.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            }

            /* console.log(_taskList)  -----------第二次更改
            let _complete = 0,
              _noComplete = 0,
              _awaitFill = 0
            _taskList.forEach((item) => {
              if (item.flag == 1 && item.status == 40) {
                //完成
                _complete += 1
              } else if (item.flag != '1') {
                //完成
                _complete += 1
              } else {
                //未开始-进行中
                _noComplete += 1
                if (item.status == 10) _awaitFill += 1
              }
            })

            if (_complete == _taskList.length) {
              this.status = this.$t('已完成')
              this.statusColor = 'complete'
            } else if (_noComplete > 0 && _complete > 0) {
              this.status = this.$t('进行中')
              this.statusColor = 'ongoing'
            } else if (_noComplete == _taskList.length) {
              if (_awaitFill == _taskList.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            }*/
            // _taskList.forEach((item) => { -----------第一次更改
            //   if (item.finishCondition == 1 && item.status == 40) {
            //     _complete += 1
            //   } else if (item.finishCondition != '1') {
            //     _complete += 1
            //   } else {
            //     _noComplete += 1
            //   }
            // })

            // if (_complete == _taskList.length) {
            //   this.status = this.$t('已完成')
            //   this.statusColor = 'complete'
            // } else if (_noComplete > 0 && _complete > 0) {
            //   this.status = this.$t('进行中')
            //   this.statusColor = 'ongoing'
            // } else if (_noComplete == _taskList.length) {
            //   this.status = this.$t('未开始')
            //   this.statusColor = 'noStart'
            // }
            // console.log(_complete, _noComplete, _taskList.length)
            // let maptaskList = _taskList.map((item) => item.status)
            // let flag = false
            // for (let i = 0; i < maptaskList.length; i++) {
            //   if (maptaskList[i] != 40) {
            //     flag = true
            //     break
            //   }
            // }
            // if (flag) {
            //   this.status = this.$t('进行中')
            //   this.statusColor = 'ongoing'
            // } else {
            //   this.status = this.$t('已完成')
            //   this.statusColor = 'complete'
            // }
            // let index = -1
            // for (let idx in currentBuAuthProjectStage.stageList) {
            //   if (
            //     currentBuAuthProjectStage.stageList[idx].id ===
            //     currentBuAuthProjectStage.currentStageId
            //   ) {
            //     index = Number(idx)
            //     break
            //   }
            // }
            // console.log(mapStageList.indexOf(this.data.column.headerText), index)
            // if (index > mapStageList.indexOf(this.data.column.headerText)) {
            //   this.status = this.$t('已完成')
            //   this.statusColor = 'complete'
            // } else if (index == mapStageList.indexOf(this.data.column.headerText)) {
            //   let _taskList =
            //     currentBuAuthProjectStage.stageList[
            //       Number(mapStageList.indexOf(this.data.column.headerText))
            //     ].taskList
            //   let maptaskList = _taskList.map((item) => item.status)
            //   let flag = false
            //   for (let i = 0; i < maptaskList.length; i++) {
            //     if (maptaskList[i] != 40) {
            //       flag = true
            //       break
            //     }
            //   }
            //   if (flag) {
            //     this.status = this.$t('进行中')
            //     this.statusColor = 'ongoing'
            //   } else {
            //     this.status = this.$t('已完成')
            //     this.statusColor = 'complete'
            //   }
            //   // this.status = this.$t('进行中')
            // } else if (index < mapStageList.indexOf(this.data.column.headerText)) {
            //   this.status = this.$t('未开始')
            //   this.statusColor = 'noStart'
            // }
          },
          methods: {}
        })
      }
    },
    ignore: true
  },
  {
    width: 150,
    field: 'qualificationCertification',
    headerText: i18n.t('资质认证'),
    template: function () {
      return {
        template: Vue.component('qualificationCertification', {
          template: `<div :class='statusColor'>{{status}}</div>`,
          data() {
            return {
              status: '',
              statusColor: ''
            }
          },
          mounted() {
            let currentBuAuthProjectStage = cloneDeep(this.data.currentBuAuthProjectStage)
            if (!currentBuAuthProjectStage) {
              this.status = '/'
              return
            }
            let mapStageList = currentBuAuthProjectStage.stageList.map((item) => item.stageName)
            if (mapStageList.indexOf(this.data.column.headerText) == -1) {
              this.status = '/'
              return
            }
            let _taskList =
              currentBuAuthProjectStage.stageList[
                Number(mapStageList.indexOf(this.data.column.headerText))
              ].taskList
            let optional = [], //0
              required = [], //1
              forbidden = [] //2
            _taskList.forEach((item) => {
              if (item.flag == '0') {
                optional.push(item)
                //可选
              } else if (item.flag == '1') {
                //必选
                required.push(item)
              } else if (item.flag == '2') {
                //禁选
                forbidden.push(item)
              }
            })

            if (forbidden.length == _taskList.length) {
              this.status = '/'
            } else if (optional.length == _taskList.length) {
              this.status = this.$t('可选')
              this.statusColor = 'optional'
            } else if (required.length == _taskList.length) {
              let _complete = 0,
                _awaitFill = 0
              required.forEach((item) => {
                if (item.status == '40') {
                  _complete += 1
                }
                if (item.status == '10') {
                  _awaitFill += 1
                }
              })
              if (_complete == _taskList.length) {
                this.status = this.$t('已完成')
                this.statusColor = 'complete'
              } else if (_awaitFill == _taskList.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            } else if (
              forbidden.length > 0 &&
              optional.length &&
              forbidden.length + optional.length == _taskList.length
            ) {
              this.status = this.$t('可选')
              this.statusColor = 'optional'
            } else {
              let _complete = 0,
                _awaitFill = 0
              required.forEach((item) => {
                if (item.status == '40') {
                  _complete += 1
                }
                if (item.status == '10') {
                  _awaitFill += 1
                }
              })
              if (_complete == required.length) {
                this.status = this.$t('已完成')
                this.statusColor = 'complete'
              } else if (_awaitFill == required.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            }
            /* console.log(_taskList)
            let _complete = 0,
              _noComplete = 0,
              _awaitFill = 0
            _taskList.forEach((item) => {
              if (item.flag == 1 && item.status == 40) {
                //完成
                _complete += 1
              } else if (item.flag != '1') {
                //完成
                _complete += 1
              } else {
                //未开始-进行中
                _noComplete += 1
                if (item.status == 10) _awaitFill += 1
              }
            })

            if (_complete == _taskList.length) {
              this.status = this.$t('已完成')
              this.statusColor = 'complete'
            } else if (_noComplete > 0 && _complete > 0) {
              this.status = this.$t('进行中')
              this.statusColor = 'ongoing'
            } else if (_noComplete == _taskList.length) {
              if (_awaitFill == _taskList.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            }*/
            // _taskList.forEach((item) => {
            //   if (item.finishCondition == 1 && item.status == 40) {
            //     _complete += 1
            //   } else if (item.finishCondition != '1') {
            //     _complete += 1
            //   } else {
            //     _noComplete += 1
            //   }
            // })

            // if (_complete == _taskList.length) {
            //   this.status = this.$t('已完成')
            //   this.statusColor = 'complete'
            // } else if (_noComplete > 0 && _complete > 0) {
            //   this.status = this.$t('进行中')
            //   this.statusColor = 'ongoing'
            // } else if (_noComplete == _taskList.length) {
            //   this.status = this.$t('未开始')
            //   this.statusColor = 'noStart'
            // }
            // let index = -1
            // for (let idx in currentBuAuthProjectStage.stageList) {
            //   if (
            //     currentBuAuthProjectStage.stageList[idx].id ===
            //     currentBuAuthProjectStage.currentStageId
            //   ) {
            //     index = Number(idx)
            //     break
            //   }
            // }
            // // console.log(mapStageList.indexOf(this.data.column.headerText), index)
            // if (index > mapStageList.indexOf(this.data.column.headerText)) {
            //   this.status = this.$t('已完成')
            //   this.statusColor = 'complete'
            // } else if (index == mapStageList.indexOf(this.data.column.headerText)) {
            //   let _taskList =
            //     currentBuAuthProjectStage.stageList[
            //       Number(mapStageList.indexOf(this.data.column.headerText))
            //     ].taskList
            //   let maptaskList = _taskList.map((item) => item.status)
            //   let flag = false
            //   for (let i = 0; i < maptaskList.length; i++) {
            //     if (maptaskList[i] != 40) {
            //       flag = true
            //       break
            //     }
            //   }
            //   if (flag) {
            //     this.status = this.$t('进行中')

            //     this.statusColor = 'ongoing'
            //   } else {
            //     this.status = this.$t('已完成')
            //     this.statusColor = 'complete'
            //   }
            //   // this.status = this.$t('进行中')
            // } else if (index < mapStageList.indexOf(this.data.column.headerText)) {
            //   this.status = this.$t('未开始')

            //   this.statusColor = 'noStart'
            // }
          },
          methods: {}
        })
      }
    },
    ignore: true
  },
  {
    width: 150,
    field: 'systemCertification',
    headerText: i18n.t('体系认证'),
    template: function () {
      return {
        template: Vue.component('systemCertification', {
          template: `<div :class='statusColor'>{{status}}</div>`,
          data() {
            return {
              status: '',
              statusColor: ''
            }
          },
          mounted() {
            let currentBuAuthProjectStage = cloneDeep(this.data.currentBuAuthProjectStage)
            if (!currentBuAuthProjectStage) {
              this.status = '/'
              return
            }
            let mapStageList = currentBuAuthProjectStage.stageList.map((item) => item.stageName)
            if (mapStageList.indexOf(this.data.column.headerText) == -1) {
              this.status = '/'
              return
            }
            let _taskList =
              currentBuAuthProjectStage.stageList[
                Number(mapStageList.indexOf(this.data.column.headerText))
              ].taskList
            let optional = [], //0
              required = [], //1
              forbidden = [] //2
            _taskList.forEach((item) => {
              if (item.flag == '0') {
                optional.push(item)
                //可选
              } else if (item.flag == '1') {
                //必选
                required.push(item)
              } else if (item.flag == '2') {
                //禁选
                forbidden.push(item)
              }
            })

            if (forbidden.length == _taskList.length) {
              this.status = '/'
            } else if (optional.length == _taskList.length) {
              this.status = this.$t('可选')
              this.statusColor = 'optional'
            } else if (required.length == _taskList.length) {
              let _complete = 0,
                _awaitFill = 0
              required.forEach((item) => {
                if (item.status == '40') {
                  _complete += 1
                }
                if (item.status == '10') {
                  _awaitFill += 1
                }
              })
              if (_complete == _taskList.length) {
                this.status = this.$t('已完成')
                this.statusColor = 'complete'
              } else if (_awaitFill == _taskList.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            } else if (
              forbidden.length > 0 &&
              optional.length &&
              forbidden.length + optional.length == _taskList.length
            ) {
              this.status = this.$t('可选')
              this.statusColor = 'optional'
            } else {
              let _complete = 0,
                _awaitFill = 0
              required.forEach((item) => {
                if (item.status == '40') {
                  _complete += 1
                }
                if (item.status == '10') {
                  _awaitFill += 1
                }
              })
              if (_complete == required.length) {
                this.status = this.$t('已完成')
                this.statusColor = 'complete'
              } else if (_awaitFill == required.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            }
            /* console.log(_taskList)
            let _complete = 0,
              _noComplete = 0,
              _awaitFill = 0
            _taskList.forEach((item) => {
              if (item.flag == 1 && item.status == 40) {
                //完成
                _complete += 1
              } else if (item.flag != '1') {
                //完成
                _complete += 1
              } else {
                //未开始-进行中
                _noComplete += 1
                if (item.status == 10) _awaitFill += 1
              }
            })

            if (_complete == _taskList.length) {
              this.status = this.$t('已完成')
              this.statusColor = 'complete'
            } else if (_noComplete > 0 && _complete > 0) {
              this.status = this.$t('进行中')
              this.statusColor = 'ongoing'
            } else if (_noComplete == _taskList.length) {
              if (_awaitFill == _taskList.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            }*/
            // _taskList.forEach((item) => {
            //   if (item.finishCondition == 1 && item.status == 40) {
            //     _complete += 1
            //   } else if (item.finishCondition != '1') {
            //     _complete += 1
            //   } else {
            //     _noComplete += 1
            //   }
            // })

            // if (_complete == _taskList.length) {
            //   this.status = this.$t('已完成')
            //   this.statusColor = 'complete'
            // } else if (_noComplete > 0 && _complete > 0) {
            //   this.status = this.$t('进行中')
            //   this.statusColor = 'ongoing'
            // } else if (_noComplete == _taskList.length) {
            //   this.status = this.$t('未开始')
            //   this.statusColor = 'noStart'
            // }
            // let index = -1
            // for (let idx in currentBuAuthProjectStage.stageList) {
            //   if (
            //     currentBuAuthProjectStage.stageList[idx].id ===
            //     currentBuAuthProjectStage.currentStageId
            //   ) {
            //     index = Number(idx)
            //     break
            //   }
            // }
            // // console.log(mapStageList.indexOf(this.data.column.headerText), index)
            // if (index > mapStageList.indexOf(this.data.column.headerText)) {
            //   this.status = this.$t('已完成')
            //   this.statusColor = 'complete'
            // } else if (index == mapStageList.indexOf(this.data.column.headerText)) {
            //   let _taskList =
            //     currentBuAuthProjectStage.stageList[
            //       Number(mapStageList.indexOf(this.data.column.headerText))
            //     ].taskList
            //   let maptaskList = _taskList.map((item) => item.status)
            //   let flag = false
            //   for (let i = 0; i < maptaskList.length; i++) {
            //     if (maptaskList[i] != 40) {
            //       flag = true
            //       break
            //     }
            //   }
            //   if (flag) {
            //     this.status = this.$t('进行中')

            //     this.statusColor = 'ongoing'
            //   } else {
            //     this.status = this.$t('已完成')
            //     this.statusColor = 'complete'
            //   }
            //   // this.status = this.$t('进行中')
            // } else if (index < mapStageList.indexOf(this.data.column.headerText)) {
            //   this.status = this.$t('未开始')

            //   this.statusColor = 'noStart'
            // }
          },
          methods: {}
        })
      }
    },
    ignore: true
  },
  {
    width: 150,
    field: 'productCertification',
    headerText: i18n.t('产品认证'),
    template: function () {
      return {
        template: Vue.component('productCertification', {
          template: `<div :class='statusColor'>{{status}}</div>`,
          data() {
            return {
              status: '',
              statusColor: ''
            }
          },
          mounted() {
            let currentBuAuthProjectStage = cloneDeep(this.data.currentBuAuthProjectStage)
            if (!currentBuAuthProjectStage) {
              this.status = '/'
              return
            }
            let mapStageList = currentBuAuthProjectStage.stageList.map((item) => item.stageName)
            if (mapStageList.indexOf(this.data.column.headerText) == -1) {
              this.status = '/'
              return
            }
            let _taskList =
              currentBuAuthProjectStage.stageList[
                Number(mapStageList.indexOf(this.data.column.headerText))
              ].taskList
            let optional = [], //0
              required = [], //1
              forbidden = [] //2
            _taskList.forEach((item) => {
              if (item.flag == '0') {
                optional.push(item)
                //可选
              } else if (item.flag == '1') {
                //必选
                required.push(item)
              } else if (item.flag == '2') {
                //禁选
                forbidden.push(item)
              }
            })

            if (forbidden.length == _taskList.length) {
              this.status = '/'
            } else if (optional.length == _taskList.length) {
              this.status = this.$t('可选')
              this.statusColor = 'optional'
            } else if (required.length == _taskList.length) {
              let _complete = 0,
                _awaitFill = 0
              required.forEach((item) => {
                if (item.status == '40') {
                  _complete += 1
                }
                if (item.status == '10') {
                  _awaitFill += 1
                }
              })
              if (_complete == _taskList.length) {
                this.status = this.$t('已完成')
                this.statusColor = 'complete'
              } else if (_awaitFill == _taskList.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            } else if (
              forbidden.length > 0 &&
              optional.length &&
              forbidden.length + optional.length == _taskList.length
            ) {
              this.status = this.$t('可选')
              this.statusColor = 'optional'
            } else {
              let _complete = 0,
                _awaitFill = 0
              required.forEach((item) => {
                if (item.status == '40') {
                  _complete += 1
                }
                if (item.status == '10') {
                  _awaitFill += 1
                }
              })
              if (_complete == required.length) {
                this.status = this.$t('已完成')
                this.statusColor = 'complete'
              } else if (_awaitFill == required.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            }
            /* console.log(_taskList)
            let _complete = 0,
              _noComplete = 0,
              _awaitFill = 0
            _taskList.forEach((item) => {
              if (item.flag == 1 && item.status == 40) {
                //完成
                _complete += 1
              } else if (item.flag != '1') {
                //完成
                _complete += 1
              } else {
                //未开始-进行中
                _noComplete += 1
                if (item.status == 10) _awaitFill += 1
              }
            })

            if (_complete == _taskList.length) {
              this.status = this.$t('已完成')
              this.statusColor = 'complete'
            } else if (_noComplete > 0 && _complete > 0) {
              this.status = this.$t('进行中')
              this.statusColor = 'ongoing'
            } else if (_noComplete == _taskList.length) {
              if (_awaitFill == _taskList.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            }*/
            // _taskList.forEach((item) => {
            //   if (item.finishCondition == 1 && item.status == 40) {
            //     _complete += 1
            //   } else if (item.finishCondition != '1') {
            //     _complete += 1
            //   } else {
            //     _noComplete += 1
            //   }
            // })

            // if (_complete == _taskList.length) {
            //   this.status = this.$t('已完成')
            //   this.statusColor = 'complete'
            // } else if (_noComplete > 0 && _complete > 0) {
            //   this.status = this.$t('进行中')
            //   this.statusColor = 'ongoing'
            // } else if (_noComplete == _taskList.length) {
            //   this.status = this.$t('未开始')
            //   this.statusColor = 'noStart'
            // }
            // let index = -1
            // for (let idx in currentBuAuthProjectStage.stageList) {
            //   if (
            //     currentBuAuthProjectStage.stageList[idx].id ===
            //     currentBuAuthProjectStage.currentStageId
            //   ) {
            //     index = Number(idx)
            //     break
            //   }
            // }
            // // console.log(mapStageList.indexOf(this.data.column.headerText), index)
            // if (index > mapStageList.indexOf(this.data.column.headerText)) {
            //   this.status = this.$t('已完成')
            //   this.statusColor = 'complete'
            // } else if (index == mapStageList.indexOf(this.data.column.headerText)) {
            //   let _taskList =
            //     currentBuAuthProjectStage.stageList[
            //       Number(mapStageList.indexOf(this.data.column.headerText))
            //     ].taskList
            //   let maptaskList = _taskList.map((item) => item.status)
            //   let flag = false
            //   for (let i = 0; i < maptaskList.length; i++) {
            //     if (maptaskList[i] != 40) {
            //       flag = true
            //       break
            //     }
            //   }
            //   if (flag) {
            //     this.status = this.$t('进行中')

            //     this.statusColor = 'ongoing'
            //   } else {
            //     this.status = this.$t('已完成')
            //     this.statusColor = 'complete'
            //   }
            //   // this.status = this.$t('进行中')
            // } else if (index < mapStageList.indexOf(this.data.column.headerText)) {
            //   this.status = this.$t('未开始')

            //   this.statusColor = 'noStart'
            // }
          },
          methods: {}
        })
      }
    },
    ignore: true
  },
  {
    width: 150,
    field: 'effectiveSupplier',
    headerText: i18n.t('供应商生效'),
    template: function () {
      return {
        template: Vue.component('effectiveSupplier', {
          template: `<div :class='statusColor'>{{status}}</div>`,
          data() {
            return {
              status: '',
              statusColor: ''
            }
          },
          mounted() {
            let currentBuAuthProjectStage = cloneDeep(this.data.currentBuAuthProjectStage)
            if (!currentBuAuthProjectStage) {
              this.status = '/'
              return
            }
            let mapStageList = currentBuAuthProjectStage.stageList.map((item) => item.stageName)
            if (mapStageList.indexOf(this.data.column.headerText) == -1) {
              this.status = '/'
              return
            }
            let _taskList =
              currentBuAuthProjectStage.stageList[
                Number(mapStageList.indexOf(this.data.column.headerText))
              ].taskList
            let optional = [], //0
              required = [], //1
              forbidden = [] //2
            _taskList.forEach((item) => {
              if (item.flag == '0') {
                optional.push(item)
                //可选
              } else if (item.flag == '1') {
                //必选
                required.push(item)
              } else if (item.flag == '2') {
                //禁选
                forbidden.push(item)
              }
            })

            if (forbidden.length == _taskList.length) {
              this.status = '/'
            } else if (optional.length == _taskList.length) {
              this.status = this.$t('可选')
              this.statusColor = 'optional'
            } else if (required.length == _taskList.length) {
              let _complete = 0,
                _awaitFill = 0
              required.forEach((item) => {
                if (item.status == '40') {
                  _complete += 1
                }
                if (item.status == '10') {
                  _awaitFill += 1
                }
              })
              if (_complete == _taskList.length) {
                this.status = this.$t('已完成')
                this.statusColor = 'complete'
              } else if (_awaitFill == _taskList.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            } else if (
              forbidden.length > 0 &&
              optional.length &&
              forbidden.length + optional.length == _taskList.length
            ) {
              this.status = this.$t('可选')
              this.statusColor = 'optional'
            } else {
              let _complete = 0,
                _awaitFill = 0
              required.forEach((item) => {
                if (item.status == '40') {
                  _complete += 1
                }
                if (item.status == '10') {
                  _awaitFill += 1
                }
              })
              if (_complete == required.length) {
                this.status = this.$t('已完成')
                this.statusColor = 'complete'
              } else if (_awaitFill == required.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            }
            /* console.log(_taskList)
            let _complete = 0,
              _noComplete = 0,
              _awaitFill = 0
            _taskList.forEach((item) => {
              if (item.flag == 1 && item.status == 40) {
                //完成
                _complete += 1
              } else if (item.flag != '1') {
                //完成
                _complete += 1
              } else {
                //未开始-进行中
                _noComplete += 1
                if (item.status == 10) _awaitFill += 1
              }
            })

            if (_complete == _taskList.length) {
              this.status = this.$t('已完成')
              this.statusColor = 'complete'
            } else if (_noComplete > 0 && _complete > 0) {
              this.status = this.$t('进行中')
              this.statusColor = 'ongoing'
            } else if (_noComplete == _taskList.length) {
              if (_awaitFill == _taskList.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            }*/
            // _taskList.forEach((item) => {
            //   if (item.finishCondition == 1 && item.status == 40) {
            //     _complete += 1
            //   } else if (item.finishCondition != '1') {
            //     _complete += 1
            //   } else {
            //     _noComplete += 1
            //   }
            // })

            // if (_complete == _taskList.length) {
            //   this.status = this.$t('已完成')
            //   this.statusColor = 'complete'
            // } else if (_noComplete > 0 && _complete > 0) {
            //   this.status = this.$t('进行中')
            //   this.statusColor = 'ongoing'
            // } else if (_noComplete == _taskList.length) {
            //   this.status = this.$t('未开始')
            //   this.statusColor = 'noStart'
            // }
            // let index = -1
            // for (let idx in currentBuAuthProjectStage.stageList) {
            //   if (
            //     currentBuAuthProjectStage.stageList[idx].id ===
            //     currentBuAuthProjectStage.currentStageId
            //   ) {
            //     index = Number(idx)
            //     break
            //   }
            // }
            // // console.log(mapStageList.indexOf(this.data.column.headerText), index)
            // if (index > mapStageList.indexOf(this.data.column.headerText)) {
            //   this.status = this.$t('已完成')
            //   this.statusColor = 'complete'
            // } else if (index == mapStageList.indexOf(this.data.column.headerText)) {
            //   let _taskList =
            //     currentBuAuthProjectStage.stageList[
            //       Number(mapStageList.indexOf(this.data.column.headerText))
            //     ].taskList
            //   let maptaskList = _taskList.map((item) => item.status)
            //   let flag = false
            //   for (let i = 0; i < maptaskList.length; i++) {
            //     if (maptaskList[i] != 40) {
            //       flag = true
            //       break
            //     }
            //   }
            //   if (flag) {
            //     this.status = this.$t('进行中')

            //     this.statusColor = 'ongoing'
            //   } else {
            //     this.status = this.$t('已完成')
            //     this.statusColor = 'complete'
            //   }
            //   // this.status = this.$t('进行中')
            // } else if (index < mapStageList.indexOf(this.data.column.headerText)) {
            //   this.status = this.$t('未开始')

            //   this.statusColor = 'noStart'
            // }
          },
          methods: {}
        })
      }
    },
    ignore: true
  },
  {
    width: 150,
    field: 'publicRelease',
    headerText: i18n.t('公示发布'),
    template: function () {
      return {
        template: Vue.component('publicRelease', {
          template: `<div :class='statusColor'>{{status}}</div>`,
          data() {
            return {
              status: '',
              statusColor: ''
            }
          },
          mounted() {
            let currentBuAuthProjectStage = cloneDeep(this.data.currentBuAuthProjectStage)
            if (!currentBuAuthProjectStage) {
              this.status = '/'
              return
            }
            let mapStageList = currentBuAuthProjectStage.stageList.map((item) => item.stageName)
            if (mapStageList.indexOf(this.data.column.headerText) == -1) {
              this.status = '/'
              return
            }
            let _taskList =
              currentBuAuthProjectStage.stageList[
                Number(mapStageList.indexOf(this.data.column.headerText))
              ].taskList
            let optional = [], //0
              required = [], //1
              forbidden = [] //2
            _taskList.forEach((item) => {
              if (item.flag == '0') {
                optional.push(item)
                //可选
              } else if (item.flag == '1') {
                //必选
                required.push(item)
              } else if (item.flag == '2') {
                //禁选
                forbidden.push(item)
              }
            })

            if (forbidden.length == _taskList.length) {
              this.status = '/'
            } else if (optional.length == _taskList.length) {
              this.status = this.$t('可选')
              this.statusColor = 'optional'
            } else if (required.length == _taskList.length) {
              let _complete = 0,
                _awaitFill = 0
              required.forEach((item) => {
                if (item.status == '40') {
                  _complete += 1
                }
                if (item.status == '10') {
                  _awaitFill += 1
                }
              })
              if (_complete == _taskList.length) {
                this.status = this.$t('已完成')
                this.statusColor = 'complete'
              } else if (_awaitFill == _taskList.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            } else if (
              forbidden.length > 0 &&
              optional.length &&
              forbidden.length + optional.length == _taskList.length
            ) {
              this.status = this.$t('可选')
              this.statusColor = 'optional'
            } else {
              let _complete = 0,
                _awaitFill = 0
              required.forEach((item) => {
                if (item.status == '40') {
                  _complete += 1
                }
                if (item.status == '10') {
                  _awaitFill += 1
                }
              })
              if (_complete == required.length) {
                this.status = this.$t('已完成')
                this.statusColor = 'complete'
              } else if (_awaitFill == required.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            }
            /* let _complete = 0,
              _noComplete = 0,
              _awaitFill = 0
            _taskList.forEach((item) => {
              if (item.flag == 1 && item.status == 40) {
                //完成
                _complete += 1
              } else if (item.flag != '1') {
                //完成
                _complete += 1
              } else {
                //未开始-进行中
                _noComplete += 1
                if (item.status == 10) _awaitFill += 1
              }
            })

            if (_complete == _taskList.length) {
              this.status = this.$t('已完成')
              this.statusColor = 'complete'
            } else if (_noComplete > 0 && _complete > 0) {
              this.status = this.$t('进行中')
              this.statusColor = 'ongoing'
            } else if (_noComplete == _taskList.length) {
              if (_awaitFill == _taskList.length) {
                this.status = this.$t('未开始')
                this.statusColor = 'noStart'
              } else {
                this.status = this.$t('进行中')
                this.statusColor = 'ongoing'
              }
            }*/
            // let index = -1
            // for (let idx in currentBuAuthProjectStage.stageList) {
            //   if (
            //     currentBuAuthProjectStage.stageList[idx].id ===
            //     currentBuAuthProjectStage.currentStageId
            //   ) {
            //     index = Number(idx)
            //     break
            //   }
            // }
            // if (index > mapStageList.indexOf(this.data.column.headerText)) {
            //   this.status = this.$t('已完成')
            //   this.statusColor = 'complete'
            // } else if (index == mapStageList.indexOf(this.data.column.headerText)) {
            //   let _taskList =
            //     currentBuAuthProjectStage.stageList[
            //       Number(mapStageList.indexOf(this.data.column.headerText))
            //     ].taskList
            //   let maptaskList = _taskList.map((item) => item.status)
            //   let flag = false
            //   for (let i = 0; i < maptaskList.length; i++) {
            //     if (maptaskList[i] != 40) {
            //       flag = true
            //       break
            //     }
            //   }
            //   if (flag) {
            //     this.status = this.$t('进行中')

            //     this.statusColor = 'ongoing'
            //   } else {
            //     this.status = this.$t('已完成')
            //     this.statusColor = 'complete'
            //   }
            // } else if (index < mapStageList.indexOf(this.data.column.headerText)) {
            //   this.status = this.$t('未开始')

            //   this.statusColor = 'noStart'
            // }
          },
          methods: {}
        })
      }
    },
    ignore: true
  },
  // {
  //   field: 'contactName',
  //   headerText: i18n.t('调查表进度'),
  //   width: 120,
  //   template: () => {
  //     return {
  //       template: Vue.component('operate-template', {
  //         template: `
  //           <div class="tp-box" style="color: #00469C">{{ countRate }}</div>
  //         `,
  //         data: function () {
  //           return { data: {} }
  //         },
  //         computed: {
  //           countRate() {
  //             if (this.data.successCount && this.data.totalCount) {
  //               return this.data.successCount + '/' + this.data.totalCount
  //             } else {
  //               return 0 + '/' + 0
  //             }
  //           }
  //         }
  //       })
  //     }
  //   },
  //   ignore: true
  // }
  {
    field: 'questionnairesNumber',
    headerText: i18n.t('调查表审核'),
    width: 120,
    template: () => {
      return {
        template: Vue.component('count-template', {
          template: `
            <span style="color: #2783fe; cursor: pointer" @click="goToCheck">${i18n.t(
              '查看调查表'
            )}({{ data.questionnairesNumber || '0' }})</span>
          `,
          data: function () {
            return { data: {} }
          },
          methods: {
            goToCheck() {
              // if (!this.data.questionnairesNumber) return
              this.$router.push({
                path: '/supplier/pur/access-detail',
                query: {
                  id: this.data.partnerArchiveId,
                  orgId: this.data.orgId,
                  partnerRelationId: this.data.partnerRelationId,
                  categoryPartnerRelationId: this.data.categoryPartnerRelationId,
                  inviteId: this.data.inviteId,
                  type: this.data.taskStageInstanceStatus == 20 ? 'approve' : ''
                }
              })
            }
          }
        })
      }
    },
    ignore: true
  },
  {
    field: 'buyerThresholdId',
    headerText: i18n.t('门槛校验'),
    width: 120,
    template: () => {
      return {
        template: Vue.component('count-template', {
          template: `
            <span style="color: #2783fe; cursor: pointer" @click="goToCheck">${i18n.t(
              '查看门槛结果'
            )}</span>
          `,
          data: function () {
            return { data: {} }
          },
          methods: {
            goToCheck() {
              // if (!this.data.buyerThresholdId) { // 新接口去除此校验
              //   this.$toast({
              //     content: this.$t('获取ID失败，请重试!'),
              //     type: 'warning'
              //   })
              //   return
              // }
              console.log('this.data', this.data)
              this.$router.push({
                path: '/supplier/pur/access-threshold-results',
                query: {
                  id: this.data.buyerThresholdId,
                  orgId: this.data.orgId,
                  partnerRelationId: this.data.partnerRelationId,
                  categoryPartnerRelationId: this.data.categoryPartnerRelationId,
                  inviteId: this.data.inviteId
                }
              })
            }
          }
        })
      }
    },
    ignore: true
  },
  {
    field: 'taskStageInstanceStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        null: '',
        20: i18n.t('待审核'),
        30: i18n.t('已驳回'),
        40: i18n.t('已批准')
      }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: [
        { key: '20', value: i18n.t('待审核') },
        { key: '30', value: i18n.t('已驳回') },
        { key: '40', value: i18n.t('已批准') }
      ],
      fields: { text: 'value', value: 'key' }
    },
    ignore: true
  }
  // {
  //   field: "pendingCount",
  //   headerText: i18n.t("待处理任务"),

  // },
]
