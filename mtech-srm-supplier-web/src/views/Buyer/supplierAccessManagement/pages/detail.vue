<template>
  <div class="lifeCycle-container fbox">
    <!-- 顶部信息 start -->
    <div class="header-status fbox">
      <div class="header-logo">D</div>
      <div class="header-content flex1 fbox">
        <div class="titles-box">
          <div class="mian-title" v-if="!!info.supplierEnterpriseName">
            {{ info.supplierEnterpriseName || '--' }}
          </div>
          <div class="sub-title fbox">
            <div class="normal-title" v-if="!!info.contactPhone">
              {{ $t('联系方式：') }}{{ info.contactPhone }}
            </div>
            <div class="normal-title" v-if="!!info.supplierCode">
              {{ $t('所属公司：') }}{{ info.supplierCode }}
            </div>
            <div class="normal-title" v-if="!!info.contactName">
              {{ $t('供货品类：') }}{{ info.contactName }}
            </div>
          </div>
          <!-- <div v-else class="sub-title fbox">
            <div class="normal-title" v-if="!!info.supplierCode">
              {{ $t('供应商编码：') }}{{ info.supplierCode }}
            </div>
            <div class="normal-title" v-if="!!info.contactName">
              {{ $t('联系人：') }}{{ info.contactName }}
            </div>
            <div class="normal-title" v-if="!!info.contactPhone">
              {{ $t('联系方式：') }}{{ info.contactPhone }}
            </div>
            <div class="normal-title" v-if="!!info.contactMail">
              {{ $t('联系人邮箱：') }}{{ info.contactMail }}
            </div>
          </div> -->
        </div>
        <div class="btns-box fbox">
          <mt-button
            v-if="showOperateBtn"
            css-class="invite-btn e-flat"
            @click="onSave"
            :is-primary="true"
            >{{ $t('保存') }}</mt-button
          >
          <mt-button
            v-if="showOperateBtn"
            css-class="invite-btn e-flat"
            @click="onSubmit"
            :is-primary="true"
            >{{ $t('保存并提交') }}</mt-button
          >
          <mt-button css-class="invite-btn e-flat" @click="onBack" :is-primary="true">{{
            $t('返回')
          }}</mt-button>
        </div>
      </div>
    </div>
    <div class="approval-area">
      <div class="approval-row">
        <p class="approval-row-label">{{ $t('操作') }}</p>
        <div class="approval-row-item approval-row-item__radio">
          <mt-radio
            v-model="approvalForm.isApprove"
            :data-source="radioData"
            :disabled="!showOperateBtn"
            @change="radioChange"
          ></mt-radio>
        </div>
      </div>
      <div class="approval-row">
        <p class="approval-row-label">{{ $t('处理意见') }}</p>
        <div class="approval-row-item">
          <textarea
            v-model="approvalForm.approvalText"
            :multiline="true"
            :rows="6"
            maxlength="800"
            float-label-type="Never"
            :placeholder="$t('请输入驳回意见（800字以内）')"
            :disabled="!showOperateBtn"
          ></textarea>
        </div>
      </div>
      <div v-if="tableData.length !== 0" class="approval-row">
        <p class="approval-row-label">{{ $t('供应商关联关系') }}</p>
        <div style="flex: 1">
          <sc-table
            ref="scTableRef"
            :loading="loading"
            :columns="columns"
            :table-data="tableData"
            :fix-height="200"
            keep-source
            :sortable="false"
            :is-show-right-btn="false"
            :is-show-column-config="false"
          >
          </sc-table>
        </div>
      </div>
    </div>
    <!-- 顶部信息 end -->

    <mt-tabs
      :e-tab="false"
      overflow-mode="Popup"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div class="mian-content">
      <task-center
        ref="taskCenter"
        v-show="selectIndex === 0"
        :id="id"
        :org-id="orgId"
        :partner-relation-id="partnerRelationId"
        :category-partner-relation-id="categoryPartnerRelationId"
        :invite-id="inviteId"
        @curTaskChange="handleCurTaskChange"
        @approveInfoChange="handleApproveInfoChange"
        @handleShowBtn="handleShowBtn"
      ></task-center>
      <SupplierSelfReview v-if="selectIndex === 1" />
    </div>
  </div>
</template>

<script>
import TaskCenter from '../components/taskCenter.vue'
import SupplierSelfReview from '../components/supplierSelfReview.vue'
import MtRadio from '@mtech-ui/radio'
import ScTable from '@/components/ScTable/src/index'

export default {
  components: {
    TaskCenter,
    SupplierSelfReview,
    MtRadio,
    ScTable
  },
  data() {
    return {
      selectIndex: 0,
      dataSource: [
        {
          title: this.$t('任务总览')
        },
        {
          title: this.$t('供应商自查')
        }
      ],
      id: '',
      orgId: '',
      partnerRelationId: '',
      categoryPartnerRelationId: '',
      inviteId: '',
      info: {},
      pagetype: '',
      approvalForm: {
        isApprove: '40',
        approvalText: this.$t('同意')
      },
      radioData: [
        {
          label: this.$t('批准'),
          value: '40'
        },
        {
          label: this.$t('驳回'),
          value: '30'
        }
      ],
      curTask: null,
      isEdit: true, // 是否可以进行审核
      showOperateBtn: false,

      loading: false,
      tableData: []
    }
  },
  computed: {
    columns() {
      return [
        {
          title: this.$t('当前供应商名称'),
          field: 'supplierEnterpriseName',
          minWidth: 200
        },
        {
          title: this.$t('存在关系供应商名称'),
          field: 'relationSupplierName',
          minWidth: 200
        },
        {
          title: this.$t('关系'),
          field: 'relationTypeDesc',
          minWidth: 200
        }
      ]
    }
  },
  methods: {
    getTableData() {
      this.loading = true
      this.$API.supplierlifecycle
        .getByInviteIdApi({ id: this.inviteId })
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.data
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleShowBtn(val) {
      this.showOperateBtn = val
    },
    radioChange(e) {
      if (e === '30') {
        this.approvalForm.approvalText = ''
      } else {
        this.approvalForm.approvalText = this.$t('同意')
      }
    },
    handleCurTaskChange(task) {
      this.curTask = task
      this.isEdit = task.status !== 40
    },
    // 当前task审批信息
    handleApproveInfoChange(info) {
      const { approveStatus, approveOpinion } = info
      this.approvalForm.isApprove = approveStatus.toString()
      this.approvalForm.approvalText = approveOpinion
    },
    // 返回
    onBack() {
      this.$router.go(-1)
    },
    // 保存
    onSave() {
      const { supplierTaskList } = this.$refs.taskCenter.taskData
      const taskNoList = []
      const stageTaskInstanceIdList = []
      if (supplierTaskList && supplierTaskList.length) {
        supplierTaskList.forEach((item) => {
          taskNoList.push(item.taskNo)
          stageTaskInstanceIdList.push(item.id)
        })
      }
      this.$API.supplierlifecycle
        .saveTaskStage({
          stageTaskInstanceIdList,
          taskNoList,
          status: Number(this.approvalForm.isApprove),
          handlingOpinions: this.approvalForm.approvalText,
          inviteId: this.inviteId,
          categoryRelationId: this.categoryPartnerRelationId
        })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
            this.$refs.taskCenter.getTaskList()
          }
        })
    },
    // 提交
    onSubmit() {
      const { supplierTaskList } = this.$refs.taskCenter.taskData
      const taskNoList = []
      const stageTaskInstanceIdList = []
      if (supplierTaskList && supplierTaskList.length) {
        supplierTaskList.forEach((item) => {
          taskNoList.push(item.taskNo)
          stageTaskInstanceIdList.push(item.id)
        })
      }
      console.log('approvalForm.isApprove', this.approvalForm.isApprove)
      if (this.approvalForm.isApprove != 40 && this.approvalForm.isApprove != 30)
        return this.$toast({
          content: this.$t('请填写是否批准项'),
          type: 'warning'
        })
      this.$API.supplierlifecycle
        .submitTaskStage({
          stageTaskInstanceIdList,
          taskNoList,
          status: Number(this.approvalForm.isApprove),
          handlingOpinions: this.approvalForm.approvalText,
          inviteId: this.inviteId,
          categoryRelationId: this.categoryPartnerRelationId
        })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('保存并提交成功'),
              type: 'success'
            })
            this.$refs.taskCenter.getTaskList()
          }
        })
    },
    handleSelectTab(index) {
      this.selectIndex = index
    },

    // 获取任务详情
    getAccessDetail(id) {
      this.$API.supplierlifecycle.getDetail({ partnerArchiveId: id }).then((res) => {
        this.info = res.data
      })
    }
  },
  created() {
    let id = this.$route.query.id
    let orgId = this.$route.query.orgId
    let partnerRelationId = this.$route.query.partnerRelationId
    let categoryPartnerRelationId = this.$route.query.categoryPartnerRelationId
    let inviteId = this.$route.query.inviteId
    let pagetype = this.$route.query.type
    if (!id) {
      this.$toast({
        content: this.$t('获取ID失败，请重试!'),
        type: 'warning'
      })
      return
    }
    this.id = id
    this.orgId = orgId
    this.partnerRelationId = partnerRelationId
    this.categoryPartnerRelationId = categoryPartnerRelationId
    this.inviteId = inviteId
    this.pagetype = pagetype
    this.getAccessDetail(id)
    this.getTableData()
  }
}
</script>

<style lang="scss">
.mt-tabs {
  margin-top: 10px;
  width: 100%;
}
</style>

<style lang="scss" scoped>
.fbox {
  display: flex;
  align-items: stretch;
}

.flex1 {
  flex: 1;
}

.lifeCycle-container {
  flex-direction: column;
  min-width: 100%;
  min-height: 100vh;
  padding-top: 20px;
  position: absolute;
  z-index: 99;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: linear-gradient(#fafafa, #fafafa), linear-gradient(#000, #000);

  // min-width: 1200px;

  .header-status {
    width: 100%;
    padding: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .header-logo {
      border-radius: 100%;
      width: 60px;
      height: 60px;
      line-height: 60px;
      background: rgba(0, 70, 156, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      font-size: 40px;
      font-family: DINAlternate;
      font-weight: bold;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      margin-right: 20px;
    }
    .header-content {
      justify-content: space-between;
      .titles-box {
        justify-content: space-evenly;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .mian-title {
          font-size: 20px;

          font-weight: 600;
          color: rgba(41, 41, 41, 1);
        }
        .sub-title {
          font-size: 12px;

          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-top: 16px;
          .normal-title {
            font-size: 12px;

            font-weight: normal;
            color: rgba(41, 41, 41, 1);
            margin-right: 30px;
          }
        }
      }

      .btns-box {
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        // color: rgba(0, 70, 156, 1);
        max-width: 300px;
        .invite-btn {
          cursor: pointer;
        }
        .invite-btn:nth-child(1) {
          margin-right: 30px;
        }
      }
    }
  }

  .mian-content {
    flex: 1;
  }
}
.approval-area {
  margin: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  .approval-row {
    display: flex;
    padding: 0;
    margin: 0;
    .approval-row-label {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 140px;
      min-height: 40px;
      background: rgba(99, 134, 193, 0.1);
      &:first-child {
        border-bottom: 1px solid #e8e8e8;
      }
    }
    .approval-row-item {
      display: flex;
      align-items: center;
      flex: 1;
      &:first-of-type {
        border-bottom: 1px solid #e8e8e8;
      }
      &.approval-row-item__radio {
        padding-left: 40px;
      }
      textarea {
        width: 100%;
      }
    }
  }
}
</style>
