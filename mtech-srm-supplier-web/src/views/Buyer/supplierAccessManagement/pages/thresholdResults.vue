<template>
  <div class="lifeCycle-container fbox">
    <!-- 顶部信息 start -->
    <div class="header-status fbox">
      <div class="header-logo">D</div>
      <div class="header-content flex1 fbox">
        <div class="titles-box">
          <div class="mian-title" v-if="!!info.supplierEnterpriseName">
            {{ info.supplierEnterpriseName || '--' }}
          </div>
          <div class="sub-title fbox">
            <div class="normal-title" v-if="!!info.contactPhone">
              {{ $t('联系方式：') }}{{ info.contactPhone }}
            </div>
            <div class="normal-title" v-if="!!info.supplierEnterpriseCode">
              {{ $t('所属公司：') }}{{ info.supplierEnterpriseCode }}
            </div>
            <div class="normal-title" v-if="!!info.categoryName">
              {{ $t('供货品类：') }}{{ info.categoryName }}
            </div>
          </div>
        </div>
        <div class="btns-box fbox">
          <mt-button
            v-if="pagetype === 'approve'"
            css-class="invite-btn e-flat"
            @click="onSave"
            :is-primary="true"
            >{{ $t('保存') }}</mt-button
          >
          <mt-button
            v-if="pagetype === 'approve'"
            css-class="invite-btn e-flat"
            @click="onSubmit"
            :is-primary="true"
            >{{ $t('保存并提交') }}</mt-button
          >
          <mt-button css-class="invite-btn e-flat" @click="onBack" :is-primary="true">{{
            $t('返回')
          }}</mt-button>
        </div>
      </div>
    </div>
    <p class="table-title">{{ $t('合格项') }}</p>
    <mt-data-grid :data-source="dataSource" :column-data="columnData"></mt-data-grid>
    <p class="table-title">{{ $t('不合格项') }}</p>
    <mt-data-grid :data-source="dataSource1" :column-data="columnData"></mt-data-grid>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import Vue from 'vue'

export default {
  data() {
    return {
      id: '',
      orgId: '',
      partnerRelationId: '',
      categoryPartnerRelationId: '',
      stageTaskInstanceIdList: [],
      info: {},
      columnData: [
        {
          width: '50',
          field: 'sortNum',
          headerText: this.$t('序号'),
          textAlign: 'Center',
          template: () => {
            return {
              template: Vue.component('operate-template', {
                template: `
            <div class="tp-box">{{ countRate }}</div>
          `,
                data: function () {
                  return { data: {} }
                },
                computed: {
                  countRate() {
                    let index = parseInt(this.data.index) + 1
                    return index
                  }
                }
              })
            }
          }
        },
        {
          field: 'thresholdName',
          headerText: this.$t('门槛名称')
        },
        {
          field: 'fieldName',
          headerText: this.$t('监控字段')
        },
        {
          field: 'formType',
          headerText: this.$t('门槛类型'),
          valueAccessor: function (field, data) {
            const formTypeList = utils.getSupplierDict('thresholdType') || []
            return formTypeList.find((e) => e.dictCode == data[field]).dictName
          }
        },
        {
          field: 'originalValue',
          headerText: this.$t('门槛原值')
        },
        {
          field: 'symbol',
          headerText: this.$t('操作符号'),
          valueAccessor: (field, data) => {
            const symbolList = [
              {
                itemName: '>',
                itemCode: 1
              },
              {
                itemName: '<',
                itemCode: 2
              },
              {
                itemName: '≥',
                itemCode: 3
              },
              {
                itemName: '≤',
                itemCode: 4
              },
              {
                itemName: '=',
                itemCode: 5
              },
              {
                itemName: this.$t('非空'),
                itemCode: 6
              },
              {
                itemName: this.$t('为空'),
                itemCode: 7
              }
            ]
            return symbolList.find((e) => e.itemCode == data[field]).itemName
          }
        },
        {
          field: 'defaultValue',
          headerText: this.$t('目标值')
        }
      ],
      dataSource: [],
      dataSource1: []
    }
  },
  methods: {
    // 返回
    onBack() {
      this.$router.go(-1)
    },

    // 获取任务详情
    getAccessDetail() {
      this.$API.supplierlifecycle
        .getThresholdDetail_new({
          stageTaskInstanceIdList: this.stageTaskInstanceIdList,
          inviteId: this.inviteId,
          categoryRelationId: this.categoryPartnerRelationId
        })
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.info = data
            if (data && data.itemDTOList && data.itemDTOList.length) {
              for (let index = 0; index < data.itemDTOList.length; index++) {
                const item = data.itemDTOList[index]
                if (item.status == 1) {
                  // 合格
                  this.dataSource.push(item)
                } else {
                  // 不合格
                  this.dataSource1.push(item)
                }
              }
            }
          }
        })
    }
  },
  created() {
    let id = this.$route.query.id
    let orgId = this.$route.query.orgId
    let partnerRelationId = this.$route.query.partnerRelationId
    let categoryPartnerRelationId = this.$route.query.categoryPartnerRelationId
    let inviteId = this.$route.query.inviteId
    let pagetype = this.$route.query.type
    // if (!id) {
    //   this.$toast({
    //     content: this.$t('获取ID失败，请重试!'),
    //     type: 'warning'
    //   })
    //   return
    // }
    this.id = id
    this.orgId = orgId
    this.partnerRelationId = partnerRelationId
    this.categoryPartnerRelationId = categoryPartnerRelationId
    this.inviteId = inviteId
    this.pagetype = pagetype

    this.$API.supplierlifecycle
      .getTaskList({
        categoryRelationId: this.categoryPartnerRelationId,
        inviteId: this.inviteId
      })
      .then((res) => {
        const { code, data } = res
        if (code === 200 && !utils.isEmpty(data)) {
          console.log('getTaskListgetTaskList', data)
          const { supplierTaskList } = data
          if (supplierTaskList && supplierTaskList.length) {
            console.log('88888888888')
            supplierTaskList.forEach((item) => {
              this.stageTaskInstanceIdList.push(item.id)
            })
            this.getAccessDetail()
          }
        }
      })
  }
}
</script>

<style lang="scss">
.mt-tabs {
  margin-top: 10px;
  width: 100%;
}
</style>

<style lang="scss" scoped>
.fbox {
  display: flex;
  align-items: stretch;
}

.flex1 {
  flex: 1;
}

.lifeCycle-container {
  flex-direction: column;
  min-height: 100%;
  min-height: 100vh;
  padding-top: 20px;
  position: absolute;
  z-index: 99;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: linear-gradient(#fafafa, #fafafa), linear-gradient(#000, #000);

  min-width: 1200px;

  .header-status {
    padding: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .header-logo {
      border-radius: 100%;
      width: 60px;
      height: 60px;
      line-height: 60px;
      background: rgba(0, 70, 156, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      font-size: 40px;
      font-family: DINAlternate;
      font-weight: bold;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      margin-right: 20px;
    }
    .header-content {
      justify-content: space-between;
      .titles-box {
        justify-content: space-evenly;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .mian-title {
          font-size: 20px;

          font-weight: 600;
          color: rgba(41, 41, 41, 1);
        }
        .sub-title {
          font-size: 12px;

          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-top: 16px;
          .normal-title {
            font-size: 12px;

            font-weight: normal;
            color: rgba(41, 41, 41, 1);
            margin-right: 30px;
          }
        }
      }

      .btns-box {
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        // color: rgba(0, 70, 156, 1);
        max-width: 300px;
        .invite-btn {
          cursor: pointer;
        }
        .invite-btn:nth-child(1) {
          margin-right: 30px;
        }
      }
    }
  }

  .mian-content {
    flex: 1;
  }
}
.table-title {
  float: left;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  text-indent: 10px;
  border-left: 5px solid #00469c;
  margin: 20px 0;
  border-radius: 2px 0 0 2px;
}
.approval-area {
  margin: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  .approval-row {
    display: flex;
    padding: 0;
    margin: 0;
    .approval-row-label {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 140px;
      min-height: 40px;
      background: rgba(99, 134, 193, 0.1);
      &:first-child {
        border-bottom: 1px solid #e8e8e8;
      }
    }
    .approval-row-item {
      display: flex;
      align-items: center;
      flex: 1;
      &:first-of-type {
        border-bottom: 1px solid #e8e8e8;
      }
      &.approval-row-item__radio {
        padding-left: 40px;
      }
      textarea {
        width: 100%;
      }
    }
  }
}
</style>
