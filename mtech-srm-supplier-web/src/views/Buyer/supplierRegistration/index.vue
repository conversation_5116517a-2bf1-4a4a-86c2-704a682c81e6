<template>
  <div class="register-box">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <mt-dialog ref="toast" :header="$t('确认执行此操作') + '？'" :buttons="buttons">
      <div>
        <br />
        {{ $t('审批备注：') }} <br />
        <mt-input
          :maxlength="200"
          v-model="approvalRemark"
          :multiline="true"
          :rows="3"
          type="text"
          :placeholder="$t('请输入审批备注')"
        ></mt-input>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import { registColumn } from './config/column'
import axios from 'axios'
export default {
  data() {
    return {
      approvalRemark: '',
      selectData: {},
      templateConfig: [
        {
          useToolTemplate: false,
          useBaseConfig: true,
          gridId: '8d05a71b-76dd-4380-b785-c24aefff3d1f',
          toolbar: {
            tools: [
              [
                {
                  id: 'add',
                  icon: 'icon_table_new',
                  title: this.$t('新增'),
                  permission: ['O_02_1130']
                },
                {
                  id: 'edit',
                  icon: 'icon_table_edit',
                  title: this.$t('编辑'),
                  permission: ['O_02_1075']
                },
                {
                  id: 'deleted',
                  icon: 'icon_table_delete',
                  title: this.$t('删除'),
                  permission: ['O_02_1083']
                },
                // {
                //   id: "invite",
                //   icon: "icon_card_invite",
                //   title: this.$t("邀请"),
                // },
                // {
                //   id: 'agree',
                //   icon: 'icon_card_invite',
                //   title: this.$t('批准')
                // },
                // {
                //   id: 'disAgree',
                //   icon: 'icon_card_invite',
                //   title: this.$t('驳回')
                // },
                {
                  id: 'export',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                }
              ],
              // { id: "importData", icon: "icon_solid_Import", title: this.$t("导入") },
              [
                'Filter',
                // {
                //   id: 'export',
                //   icon: 'icon_solid_export',
                //   title: this.$t('导出')
                // },
                'Refresh',
                'Setting'
              ]
            ]
          },
          grid: {
            columnData: registColumn,
            asyncConfig: {
              url: '/supplier/tenant/buyer/invite/list'
            },
            frozenColumns: 3
          }
        }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.$t('保存') }
        }
      ]
    }
  },
  methods: {
    cancel() {
      this.$refs.toast.ejsRef.hide()
      this.approvalRemark = ''
    },
    confirm() {
      const inviteStatus = this.selectData.inviteStatus //等于4是驳回
      if (inviteStatus == 4) {
        if (!this.approvalRemark) {
          this.$toast({
            content: this.$t('驳回状态下审批备注必填哦~'),
            type: 'warning'
          })
          return
        }
      }
      this.$loading()
      let idList = []
      this.selectData.idList.forEach((item) => {
        idList.push(item.id)
      })
      let obj = {
        idList,
        inviteStatus: this.selectData.inviteStatus,
        approvalRemark: this.approvalRemark
      }

      this.$API.supplierRegister
        .handleRegister(obj)
        .then(() => {
          this.$hloading()
          this.$refs.templateRef.refreshCurrentGridData()
          this.cancel()
        })
        .catch((err) => {
          if (err) {
            this.$hloading()
            this.$toast({
              content: err.msg,
              type: 'warning'
            })
          }
        })
    },
    handleClickCellTitle(e) {
      if (e.field == 'inviteNo') {
        // this.$router.push({
        //   path: '/supplier/pur/register/detail',
        //   query: { type: 'view', record: [e.data], itemData: e.data }
        // })
        this.$dialog({
          modal: () => import('./components/addRegisterexamine.vue'),
          data: {
            title: this.$t('查看'),
            type: 'view',
            record: [e.data],
            itemData: e.data,
            refresh: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          },
          success: () => {},
          close: () => {}
        })
      }
    },
    handleClickToolBar(item) {
      let records = item.data ? [item.data] : item.gridRef.getMtechGridRecords()
      let name = item.toolbar ? item.toolbar.id : item.tool.id
      if (name == 'add') {
        //新增：打开弹窗
        this.$dialog({
          modal: () => import('./components/addRegister.vue'),
          data: {
            title: this.$t('新增'),
            type: 'add',
            record: []
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          },
          close: () => {}
        })
      } else if (name == 'invite') {
        if (records.length == 0) {
          this.$toast({
            content: this.$t('请选择要邀请的供应商'),
            type: 'warning'
          })
        } else {
          //   邀请弹窗
          this.$dialog({
            modal: () => import('./components/inviteDialog.vue'),
            data: {
              title: this.$t('邀请'),
              record: records
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            },
            close: () => {}
          })
        }
      } else if (name == 'recall') {
        if (records.length == 0) {
          this.$toast({
            content: this.$t('请选择要撤回的供应商'),
            type: 'warning'
          })
        } else {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认撤回数据？')
            },
            success: () => {
              this.$loading()
              let idList = []
              records.forEach((item) => {
                idList.push(item.id)
              })
              this.$API.supplierRegister
                .withdrawInvite({ idList })
                .then((res) => {
                  this.$hloading()
                  if (res.code == 200) {
                    this.$refs.templateRef.refreshCurrentGridData()
                  }
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'warning'
                  })
                })
            }
          })
        }
      } else if (name == 'deleted') {
        if (records.length == 0) {
          this.$toast({
            content: this.$t('请选择要删除的数据'),
            type: 'warning'
          })
        } else {
          let idList = []
          let num = 0
          records.forEach((item) => {
            idList.push(item.id)
            if (item.inviteStatus == 2 || item.inviteStatus == 3) {
              num++
            }
          })
          if (num == 0) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认删除数据？')
              },
              success: () => {
                this.$loading()
                this.$API.supplierRegister
                  .deleteInvite({ idList })
                  .then((res) => {
                    this.$hloading()
                    if (res.code == 200) {
                      this.$refs.templateRef.refreshCurrentGridData()
                    }
                  })
                  .catch((err) => {
                    this.$hloading()
                    this.$toast({
                      content: err.msg,
                      type: 'warning'
                    })
                  })
              }
            })
          } else {
            this.$toast({
              content: this.$t('数据状态不能删除'),
              type: 'warning'
            })
          }
        }
      } else if (name == 'edit') {
        if (records.length == 1) {
          if (
            records[0].inviteStatus == 1 ||
            records[0].inviteStatus == 4 ||
            records[0].inviteStatus == 5
          ) {
            this.$dialog({
              modal: () => import('./components/addRegister.vue'),
              data: {
                title: this.$t('编辑'),
                type: 'edit',
                record: records
              },
              success: () => {
                this.$refs.templateRef.refreshCurrentGridData()
              },
              close: () => {}
            })
          } else {
            this.$toast({
              content: this.$t('此状态下不能编辑'),
              type: 'warning'
            })
          }
        } else {
          this.$toast({
            content: this.$t('请选择一条数据进行编辑'),
            type: 'warning'
          })
        }
      } else if (name == 'agree' || name == 'disAgree') {
        // 是否选择数据，选择的数据中是否都是待确认的数据，批准就传3  驳回就传4，执行成功后刷新表格
        if (records.length == 0) {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        } else {
          var num = 0
          records.forEach((item) => {
            if (item.inviteStatus != 2) {
              num++
            }
          })
          if (num > 0) {
            this.$toast({
              content: this.$t('请选择待确认的数据进行操作'),
              type: 'warning'
            })
          } else {
            console.log(records, '-=-=-==-=')
            this.selectData = {
              idList: [...records],
              inviteStatus: name == 'agree' ? 3 : 4
            }
            this.$refs.toast.ejsRef.show()
          }
        }
      } else if (name == 'importData') {
        // 导入
        this.$dialog({
          modal: () => import('./components/importComp.vue'),
          data: {
            title: this.$t('导入'),
            type: 'importData'
          },
          success: () => {},
          close: () => {}
        })
      } else if (name == 'export') {
        let idList = []
        records.forEach((item) => {
          idList.push(item.id)
        })
        let params = this.$refs.templateRef.getAsyncParams()
        params['ids'] = idList
        this.$loading()
        axios
          .post('/api/supplier/tenant/buyer/invite/exportInvite', params, {
            responseType: 'blob' // 1.首先设置responseType对象格式为 blob:
          })
          .then((res) => {
            this.$hloading()
            // console.log(res); //把response打出来，看下图
            let blob = new Blob([res.data], {
              type: 'application/x-msdownload'
            })
            // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
            let url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象

            // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = '供应商注册管理.xlsx'
            a.click()
            // 5.释放这个临时的对象url
            window.URL.revokeObjectURL(url)
          })
          .catch(() => {
            this.$hloading()
            this.$toast({ content: '导出失败，请重试!', type: 'warning' })
          })
      }
    },
    handleClickCellTool(item) {
      if (item.tool.id == 'invite') {
        let records = [item.data]
        //   邀请弹窗
        this.$dialog({
          modal: () => import('./components/inviteDialog.vue'),
          data: {
            title: this.$t('邀请'),
            record: records
          }
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.register-box {
  width: 100%;
  height: 100%;
}
/deep/.status-label {
  width: 44px;
  height: 20px;
  background: rgba(99, 134, 193, 0.1);
  border-radius: 2px;
  font-size: 12px;
  font-weight: 500;
  color: rgba(99, 134, 193, 1);
}
/deep/ .status-enable {
  width: 44px;
  height: 20px;
  background: rgba(154, 154, 154, 0.1);
  border-radius: 2px;
  font-size: 12px;
  font-weight: 500;
  color: rgba(154, 154, 154, 1);
}
</style>
