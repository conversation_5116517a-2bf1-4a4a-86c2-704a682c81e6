<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      :hidden-tabs="true"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="rebateCode" :label="$t('返利协议单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.rebateCode"
                :show-clear-button="true"
                :placeholder="$t('请输入返利协议单号')"
              />
            </mt-form-item>
            <mt-form-item prop="rebateName" :label="$t('返利协议名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.rebateName"
                :show-clear-button="true"
                :placeholder="$t('请输入返利协议名称')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('所属公司')" label-style="top">
              <RemoteAutocomplete
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                v-model="searchFormModel.companyCode"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="administrativeCompany"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierCodeList" :label="$t('供应商')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.supplierCodeList"
                :multiple="true"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="supplier"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="statusList" :label="$t('状态')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.statusList"
                css-class="rule-element"
                :data-source="statusList"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
            <mt-form-item prop="startDate" :label="$t('返利起始日')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.startDate"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择返利起始日')"
                @change="(e) => handleDateTimeChange(e, 'startDate')"
              />
            </mt-form-item>
            <mt-form-item prop="endDate" :label="$t('返利结束日')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.endDate"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择返利结束日')"
                @change="(e) => handleDateTimeChange(e, 'endDate')"
              />
            </mt-form-item>
            <mt-form-item prop="feedbackDate" :label="$t('要求反馈日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.feedbackDate"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择要求反馈日期')"
                @change="(e) => handleDateTimeChange(e, 'feedbackDate')"
              />
            </mt-form-item>
            <mt-form-item prop="createDate" :label="$t('创建日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createDate"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择创建日期')"
                @change="(e) => handleDateTimeChange(e, 'createDate')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { pageConfig, statusList } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { getCurrentBu } from '@/constants/bu'

export default {
  components: {
    RemoteAutocomplete
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {
        startDateRange: [],
        endDateRange: [],
        feedbackDateRange: [],
        createDateRange: []
      },
      pageConfig,
      statusList
    }
  },
  computed: {},
  mounted() {
    this.$refs.templateRef.refreshCurrentGridData()
  },
  methods: {
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        const start = dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        const end = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
        this.searchFormModel[field + 'Range'] = [new Date(start).getTime(), new Date(end).getTime()]
      } else {
        this.searchFormModel[field + 'Range'] = []
      }
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      const selectedRecords = gridRef.getCustomSelectedRows()
      if (
        ['Delete', 'Edit', 'Print', 'Submit', 'Publish', 'audit'].includes(toolbar.id) &&
        selectedRecords.length === 0
      ) {
        this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
        return
      }

      if (['Edit', 'Print', 'audit'].includes(toolbar.id) && selectedRecords.length > 1) {
        this.$toast({ content: this.$t('只能选择一行数据'), type: 'warning' })
        return
      }

      let temp = null
      switch (toolbar.id) {
        case 'Create':
        case 'Edit':
          if (toolbar.id === 'Edit' && ![1, 3, 6].includes(selectedRecords[0].status)) {
            this.$toast({
              content: this.$t('仅可编辑状态为【新建、供应商拒绝、审批拒绝】的单据！'),
              type: 'warning'
            })
            return
          }
          this.handleToDetail(toolbar.id?.toLowerCase(), selectedRecords[0])
          break
        case 'Delete':
          temp = selectedRecords.find((r) => ![1, 3, 6, 7].includes(r.status))
          if (temp) {
            this.$toast({
              content: this.$t('仅可删除状态为【新建、供应商拒绝、审批拒绝、审批废弃】的单据！'),
              type: 'warning'
            })
            return
          }
          this.handleDelete(selectedRecords)
          break
        case 'Submit':
          temp = selectedRecords.find((r) => r.status !== 4)
          if (temp) {
            this.$toast({
              content: this.$t('仅可提交状态为【触发审批失败】的单据！'),
              type: 'warning'
            })
            return
          }
          this.handleSubmit(selectedRecords)
          break
        case 'Download':
          this.handleDownload()
          break
        case 'Print':
          this.handlePrint(selectedRecords[0])
          break
        case 'Publish':
          temp = selectedRecords.find((r) => ![1, 2, 6].includes(r.status))
          if (temp) {
            this.$toast({
              content: this.$t('仅可发布状态为【新建、供应商拒绝、审批拒绝】的单据！'),
              type: 'warning'
            })
            return
          }
          this.handlePublish(selectedRecords)
          break
        case 'audit':
          this.handleAudit(selectedRecords[0])
          break
        default:
          break
      }
    },
    // 点击单元格标题
    handleClickCellTitle(e) {
      const { field, data } = e
      switch (field) {
        case 'rebateCode':
          this.handleToDetail('view', data)
          break
        case 'operateRecords':
          this.hanldeViewOperateRecors(data)
          break
        default:
          break
      }
    },
    // 新增、编辑、查看
    handleToDetail(type, row) {
      this.$router.push({
        name: 'rebate-agreement-create-detail',
        query: {
          type,
          id: row?.id || null,
          refreshId: Date.now()
        }
      })
    },
    handleAudit(row) {
      let params = {
        applyId: row.id,
        businessType: 'rebate_voucher_submit'
      }
      this.$API.deadMaterials.deadMaterialsOALink(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    // 删除
    handleDelete(list) {
      const ids = []
      list.forEach((item) => ids.push(item.id))
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$loading()
          this.$API.rebateManagement
            .deleteRebateAgreementList(ids)
            .then(() => {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
              this.$hloading()
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    // 提交
    async handleSubmit(list) {
      const ids = []
      list.forEach((item) => ids.push(item.id))
      const res = await this.$API.rebateManagement.submitRebateAgreement(ids)
      if (res.code === 200) {
        this.$toast({ content: this.$t('提交成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      }
    },
    // 导出
    async handleDownload() {
      const page = this.$refs.templateRef.getCurrentUsefulRef().ejsRef.pageSettings
      const params = {
        ...this.searchFormModel,
        queryType: 2,
        page,
        bu: getCurrentBu()
      }
      const res = await this.$API.rebateManagement.exportRebateAgreementList(params)
      if (res.data) {
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        download({ fileName: getHeadersFileName(res), blob: res.data })
      }
    },
    // 打印
    async handlePrint(row) {
      let buffer = await this.$API.rebateManagement
        .printRebateAgreement({ id: row.id })
        .catch(() => {})
      buffer?.data && (buffer = buffer.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    },
    // 发布
    async handlePublish(list) {
      const ids = []
      list.forEach((item) => ids.push(item.id))
      const res = await this.$API.rebateManagement.publishRebateAgreement(ids)
      if (res.code === 200) {
        this.$toast({ content: this.$t('发布成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      }
    },
    // 查看操作记录
    hanldeViewOperateRecors(row) {
      this.$dialog({
        modal: () => import('./components/operateRecordsDialog.vue'),
        data: {
          title: this.$t('操作记录'),
          id: row.id
        },
        success: () => {}
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/mixin.scss';

.full-height {
  height: 100%;
}
::v-deep {
  .template_checkbox {
    text-align: center;
    input[type='checkbox'] {
      visibility: visible;
    }
  }
  .j-select.ant-select .ant-select-selection {
    background-color: #f5f5f5;
    border-color: rgba(0, 0, 0, 0.42);
  }

  .e-rowcell.sticky-col-0,
  .e-headercell.sticky-col-0 {
    @include sticky-col;
    left: 0px;
  }
  .e-rowcell.sticky-col-1,
  .e-headercell.sticky-col-1 {
    @include sticky-col;
    left: 50px; // (注意：) 50px就是前面所有固定列的列宽相加，比如这里就是 0 + 复选框列（50px） = 50px
  }
}
</style>
