<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <ScTable
        ref="sctableRef"
        :columns="columns"
        :table-data="tableData"
        :edit-config="editConfig"
        :sort-config="sortConfig"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            v-show="!item.isHidden"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :disabled="item.disabled"
            size="small"
            @click="handleClickToolBar(item)"
            >{{ item.name }}</vxe-button
          >
        </template>
      </ScTable>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'

export default {
  components: { ScTable },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      tableData: [],
      editConfig: {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    sortConfig() {
      return {
        defaultSort: {
          field: 'stageAmount',
          order: 'asc'
        },
        showIcon: false
      }
    },
    toolbar() {
      return [
        {
          code: 'add',
          name: this.$t('新增'),
          status: 'info'
        },
        {
          code: 'delete',
          name: this.$t('删除'),
          status: 'info'
        }
      ]
    },
    columns() {
      const dedaultColumns = [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'rebateLadderType',
          title: this.$t('阶梯类型'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              const selectedItem = this.stepTypeList.find(
                (item) => item.value === row.rebateLadderType
              )
              const rebateLadderTypeName = selectedItem?.text || null
              return [<span>{rebateLadderTypeName}</span>]
            },
            edit: ({ row }) => {
              // 阶梯类型只能选择其一，不能混合选择
              const stepTypeListTemp = [...this.stepTypeList]
              const stList = []
              this.tableData.forEach((r) => r.rebateLadderType && stList.push(r))
              if (this.tableData.length > 1 && stList.length >= 1) {
                stepTypeListTemp.forEach((item) => {
                  item.value !== stList[0].rebateLadderType
                    ? (item.disabled = true)
                    : (item.disabled = false)
                })
              } else {
                stepTypeListTemp.forEach((item) => (item.disabled = false))
              }
              return [
                <vxe-select
                  v-model={row.rebateLadderType}
                  clearable
                  options={stepTypeListTemp}
                  option-props={{ label: 'text', value: 'value' }}
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'stageAmount',
          title: this.$t('截止数值'),
          sortable: true,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.stageAmount} clearable type='integer' min='0' transfer />
              ]
            }
          }
        },
        {
          field: 'rebateRatio',
          title: this.$t('返利比例（%）'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-show={row.rebateLadderType === 1}
                  v-model={row.rebateRatio}
                  clearable
                  type='number'
                  min='0'
                  max='100'
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'rebateAmount',
          title: this.$t('返利金额（元）'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-show={row.rebateLadderType === 2}
                  v-model={row.rebateAmount}
                  clearable
                  type='number'
                  min='0'
                  transfer
                />
              ]
            }
          }
        }
      ]

      if (this.modalData.type === 7) {
        const col = {
          field: 'rebatePriceDiff',
          title: this.$t('返利差价'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-show={row.rebateLadderType === 3}
                  v-model={row.rebatePriceDiff}
                  clearable
                  type='number'
                  min='0'
                  transfer
                />
              ]
            }
          }
        }
        dedaultColumns.push(col)
      }

      return dedaultColumns
    },
    stepTypeList() {
      const defaultList = [
        { text: this.$t('返利比例'), value: 1 },
        { text: this.$t('返利金额'), value: 2 }
      ]
      if (this.modalData.type === 7) {
        defaultList.push({ text: this.$t('单片差价'), value: 3 })
      }
      return defaultList
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show() //显示弹窗
    this.tableData = this.modalData.list
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      if (!this.validateList()) {
        return
      }
      const dataList = this.tableData
      dataList.forEach((r) => r.isAdd && (r.id = null))
      this.$emit('confirm-function', dataList)
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete()
          break
      }
    },
    // 新增
    async handleAdd() {
      const newRowData = {
        isAdd: true
      }
      const { row: newRow } = await this.tableRef.insertAt(newRowData)
      this.tableRef.setEditRow(newRow)
      this.tableData.unshift(newRow)
    },
    // 删除
    handleDelete() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (!selectedRecords.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          await this.tableRef.removeCheckboxRow()
          this.tableData = this.tableRef.getTableData().fullData
        }
      })
    },
    validateList() {
      if (this.tableData.length === 0) {
        this.$toast({ content: this.$t('阶梯返利等级设置明细不能为空'), type: 'warning' })
        return false
      }
      for (let item of this.tableData) {
        const index = this.tableRef.getRowSeq(item)
        const { rebateLadderType, stageAmount, rebateRatio, rebateAmount, rebatePriceDiff } = item
        if (!rebateLadderType) {
          this.$toast({ content: this.getMsg(index, 'rebateLadderType'), type: 'warning' })
          return false
        }
        if (!stageAmount) {
          this.$toast({ content: this.getMsg(index, 'stageAmount'), type: 'warning' })
          return false
        }
        if (rebateLadderType === 1 && !rebateRatio && rebateRatio !== 0) {
          this.$toast({ content: this.getMsg(index, 'rebateRatio'), type: 'warning' })
          return false
        }
        if (rebateLadderType === 2 && !rebateAmount && rebateAmount !== 0) {
          this.$toast({ content: this.getMsg(index, 'rebateAmount'), type: 'warning' })
          return false
        }
        if (rebateLadderType === 3 && !rebatePriceDiff && rebatePriceDiff !== 0) {
          this.$toast({ content: this.getMsg(index, 'rebatePriceDiff'), type: 'warning' })
          return false
        }
      }
      return true
    },
    getMsg(index, key) {
      const keyMap = {
        rebateLadderType: this.$t('阶梯类型'),
        stageAmount: this.$t('截止数值'),
        materialCode: this.$t('物料编码'),
        rebateRatio: this.$t('返利比例'),
        rebateAmount: this.$t('返利金额'),
        rebatePriceDiff: this.$t('单片差价')
      }
      const msg = this.$t('第') + index + this.$t('行') + ', ' + keyMap[key] + this.$t('不能为空')
      return msg
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  width: 100%;
}
</style>
