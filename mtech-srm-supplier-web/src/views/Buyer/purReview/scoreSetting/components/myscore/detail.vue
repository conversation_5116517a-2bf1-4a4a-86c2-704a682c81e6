<template>
  <div style="height: 100%; background: #fff">
    <div class="detail-header--wrap">
      <p class="detail-header-name">
        {{ detailData.code }}
        <span class="tags tag1">{{ detailData.bizTypeName }}</span>
        <span class="tags tag2" v-if="detailData.isNotify == 1">{{ $t('通知供应商') }}</span>
        <span class="detail-header-button--wrap">
          <mt-button type="text" class="detail-header-button" @click="backDetail">{{
            $t('返回')
          }}</mt-button>
          <mt-button
            v-if="actionScoreStatus"
            type="text"
            class="detail-header-button"
            @click="save"
            >{{ $t('保存') }}</mt-button
          >
          <mt-button
            v-if="actionScoreStatus"
            type="text"
            class="detail-header-button"
            @click="submitScore"
            >{{ $t('提交打分') }}</mt-button
          >
        </span>
      </p>
      <p class="detail-header-category detail-header-items">
        <span class="detail-header-item">{{ $t('公司：') }}{{ detailData.buyerOrgName }}</span>
        <span class="detail-header-item"
          >{{ $t('供应商名称：') }}{{ detailData.supplierName }}</span
        >
        <span class="detail-header-item">{{ $t('评审内容：') }}{{ detailData.content }}</span>
        <span class="detail-header-item">
          {{ $t('计划时间：') }}{{ detailData.planTimeBegin }}-{{ detailData.planTimeEnd }}
        </span>
        <span class="detail-header-item"
          >{{ $t('评审组长：') }}{{ detailData.taskLeaderName }}</span
        >
      </p>
      <p class="detail-header-items">
        <span class="detail-header-item">{{ $t('品类：') }}{{ detailData.categoryName }}</span>
        <span class="detail-header-item">{{ $t('创建人：') }}{{ detailData.createUserName }}</span>
        <span class="detail-header-item">{{ $t('创建日期：') }}{{ detailData.createTime }}</span>
      </p>
    </div>
    <div class="detail-table--wrap" v-if="statusOrder">
      <order-item
        ref="orderItemRef"
        :order-data="detailData.templateResponses"
        :info="detailData"
      ></order-item>
    </div>
  </div>
</template>
<script>
// import { detailPageConfig } from "../template/config/index";
export default {
  components: {
    orderItem: () => import('./components/orderItem/index.vue')
  },
  data() {
    return {
      //   detailPageConfig,
      tabConfig: { headerPlacement: 'left' },
      detailData: {},
      statusOrder: false,
      SourceData: []
    }
  },
  created() {
    this.getDetailData()
  },
  methods: {
    backDetail() {
      this.$router.push({
        path: '/supplier/pur/review?tab=3'
      })
    },
    // 请求数据
    getDetailData() {
      //   let form = new FormData()
      //   form.append("code",this.$route.query.code)
      let obj = {
        code: this.$route.query.code
      }
      this.$API.supplierReviewTask.getScoreDetail(obj).then((res) => {
        if (res.code == 200) {
          this.detailData = res.data
          // 时间戳转成时间
          this.detailData.planTimeBegin = this.$utils.formateTime(
            Number(this.detailData.planTimeBegin),
            'yyyy-MM-dd'
          )
          this.detailData.planTimeEnd = this.$utils.formateTime(
            Number(this.detailData.planTimeEnd),
            'yyyy-MM-dd'
          )

          // 拼接评审内容
          this.detailData.content = ''
          this.statusOrder = true
          this.insertStatus(this.detailData.templateResponses)
          this.detailData.templateResponses.forEach((item, index) => {
            if (index !== this.detailData.templateResponses.length - 1) {
              this.detailData.content += item.templateTypeName + ','
            } else {
              this.detailData.content += item.templateTypeName
            }
          })
        }
      })
    },
    // 插入当前status
    insertStatus(data) {
      data.forEach((item) => {
        item.statusCode = this.detailData.status
        if (item.itemResponses) {
          this.insertStatus(item.itemResponses)
        }
      })
    },
    // 保存
    save() {
      this.$refs.orderItemRef.setData()
      this.batchDetailScore(false)
    },
    submitScore() {
      this.$refs.orderItemRef.setData()
      this.batchDetailScore(true)
    },
    // 提交
    batchDetailScore(isSubmit) {
      const dataSource = this.SourceData.length
        ? this.SourceData
        : this.detailData.templateResponses

      const reviewTaskTemplateItems = []
      // 提交-校验打分项必填
      let valid = true
      let flag = true
      for (let i = 0; i < dataSource.length; i++) {
        const item = dataSource[i]
        console.log('batchDetailScore', item)
        if (
          [
            'quality-qpa',
            'RD-rd1',
            'fc-zrvn',
            'odmin-zh',
            'gf-gfzl',
            'gf-gfsw',
            'gf-gfjs',
            'gf-gfzlyf',
            'tx-txyf',
            'tx-txsw',
            'tx-txzl'
          ].includes(item.templateTypeId)
        ) {
          let weight = 0
          let isUnique = false
          const uniqueSet = new Set()
          for (let j = 0; j < item.itemResponses?.length; j++) {
            const element = item.itemResponses[j]
            weight += Number(element.weight)
            if (!element.itemName) {
              this.$toast({
                content: this.$t('请输入维度'),
                type: 'warning'
              })
              valid = false
              break
            }
            if (uniqueSet.has(element.itemName)) {
              isUnique = true
            } else {
              uniqueSet.add(element.itemName)
            }
            if (element.actScore === null || element.actScore === '') {
              this.$toast({
                content: this.$t('请输入自检得分'),
                type: 'warning'
              })
              valid = false
              break
            }
            let newItem = {}
            newItem.id = element.id?.includes('row_') ? null : element.id
            newItem.taskCode = this.$route.query.code
            newItem.templateCode = item.templateCode
            newItem.itemName = element.itemName
            newItem.fullScore = element.fullScore
            newItem.symbol = element.symbol
            newItem.score = element.score
            newItem.actScore = element.actScore
            newItem.weight = element.weight
            newItem.useSetStatus = element.useSetStatus
            reviewTaskTemplateItems.push(newItem)
          }
          if (valid && isUnique) {
            this.$toast({
              content: this.$t('维度不能重复'),
              type: 'warning'
            })
            valid = false
            break
          }
          if (valid && weight !== 100) {
            this.$toast({
              content: this.$t('权重之和只能为100'),
              type: 'warning'
            })
            valid = false
            break
          }
        } else {
          for (let j = 0; j < item.itemResponses?.length; j++) {
            const element = item.itemResponses[j]
            for (let z = 0; z < element.itemResponses?.length; z++) {
              const ele = element.itemResponses[z]
              if (ele.useSetStatus === null) {
                ele.useSetStatus = 0
              }
              if (isSubmit) {
                // 不适用关闭态 并且 未打分
                if (ele.useSetStatus !== 1 && ele.actScore == null) {
                  flag = false
                  break
                }
              }

              let newItem = {}
              newItem.id = ele.id
              newItem.taskCode = ele.taskCode
              newItem.templateCode = ele.templateCode
              newItem.itemCode = ele.itemCode
              newItem.useSetStatus = ele.useSetStatus
              newItem.actScore = ele.actScore
              newItem.reason = ele.reason
              newItem.fileSupportSituation = ele.fileSupportSituation
              newItem.sampleInspectInfo = ele.sampleInspectInfo
              newItem.executionMatchDegree = ele.executionMatchDegree
              newItem.scoringBasisFileList = ele.scoringBasisFileList
              reviewTaskTemplateItems.push(newItem)
            }
          }
        }
      }
      if (!valid) {
        return
      }
      if (!flag) {
        this.$toast({
          content: this.$t('有评审项未打分，请打分后再提交！'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: !isSubmit ? this.$t('保存') : this.$t('打分'),
          message: !isSubmit ? this.$t('确认保存吗？') : this.$t('确认提交打分吗？')
        },
        success: () => {
          this.$loading()
          let url = null
          if (isSubmit) {
            url = this.$API.supplierReviewTask.batchSubmitDetailScore(reviewTaskTemplateItems)
          } else {
            url = this.$API.supplierReviewTask.batchSaveDetailScore(reviewTaskTemplateItems)
          }
          url
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.$toast({
                  content: !isSubmit ? this.$t('保存成功') : this.$t('提交成功'),
                  type: 'success'
                })
                isSubmit
                  ? this.$router.push({
                      path: '/supplier/pur/review?tab=3'
                    })
                  : this.getDetailData()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    getDataSource(data) {
      data.forEach((e) => {
        this.SourceData.forEach((item, index) => {
          if (item.itemCode == e.itemCode) {
            this.SourceData.splice(index, 1)
          }
        })
        this.SourceData.push(e)
      })
    }
  },
  computed: {
    actionScoreStatus() {
      return this.detailData.status == 10
    }
  }
}
</script>
<style lang="scss" scoped>
.detail-header--wrap {
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 20px 30px;
  margin-bottom: 16px;

  .detail-header-name {
    font-size: 20px;
    line-height: 32px;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
    .tags {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      padding: 2px;
      border-radius: 2px;
      margin-left: 10px;
    }
    .tag1 {
      color: rgb(237, 161, 51);
      background: rgba(237, 161, 51, 0.1);
    }
    .tag2 {
      color: rgb(99, 134, 193);
      background: rgba(99, 134, 193, 0.1);
    }
  }
  .detail-header-category {
    font-size: 12px;
    line-height: 16px;
    color: rgba(41, 41, 41, 1);
  }
  .detail-header-items {
    font-size: 14px;
    font-weight: 600;
    margin-top: 20px;
    color: rgba(41, 41, 41, 1);
    .detail-header-item {
      margin-right: 24px;
    }
  }

  .detail-header-button--wrap {
    float: right;
    .detail-header-button {
      margin-right: 24px;
    }
  }
}
</style>
