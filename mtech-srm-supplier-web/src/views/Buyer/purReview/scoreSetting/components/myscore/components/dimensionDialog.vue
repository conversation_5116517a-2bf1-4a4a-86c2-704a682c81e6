<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules">
        <mt-form-item prop="orgIdArr" :label="$t('公司：')">
          <mt-DropDownTree
            v-if="fieldsarr.dataSource.length"
            v-model="formObject.orgIdArr"
            :placeholder="$t('请选择公司：')"
            :popup-height="500"
            :fields="fieldsarr"
            @select="selectCompany"
            id="baseTreeSelect"
          ></mt-DropDownTree>
          <mt-select v-else :placeholder="$t('请选择公司')" :data-source="[]"></mt-select>
        </mt-form-item>
        <mt-form-item prop="planId" :label="$t('选择计划模板：')">
          <mt-select
            v-model.number="formObject.planId"
            :data-source="planeArrList"
            :fields="{ text: 'planName', value: 'id' }"
            :show-clear-button="true"
            @select="selectPlan"
            :placeholder="$t('公司下面的分析计划')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="categorytree" :label="$t('适用品类：')">
          <mt-DropDownTree
            @input="selectCategoryas"
            v-model="formObject.categorytree"
            v-if="fields.dataSource.length"
            :fields="fields"
            :show-check-box="true"
            id="checkboxTreeSelect"
            :placeholder="$t('请选择')"
            :allow-multi-selection="true"
            :auto-check="true"
            :key="fields.key"
          ></mt-DropDownTree>
          <mt-select v-else :placeholder="$t('请选择品类')" :data-source="[]"></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      // 表单验证
      rules: {
        orgIdArr: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ],
        categorytree: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        planId: [
          {
            required: true,
            message: this.$t('请选择计划'),
            trigger: 'blur'
          }
        ]
      },
      fieldsarr: {
        dataSource: [], //公司树下拉数组
        value: 'id',
        text: 'name',
        child: 'children',
        code: 'orgCode'
      },
      fields: {
        dataSource: [], //品类树下拉数组
        value: 'categoryCode',
        text: 'categoryName',
        child: 'childrens',
        key: 1
        // parentValue: "pid",
        // hasChildren: "hasChild",
      },
      formInfo: {
        orgId: '', //公司id
        companyId: '', //计划id 取计划模板
        planId: '', //计划id 取品类
        categorytree: [] //品类树下拉数组
      },
      // 弹窗底部按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      // 新增表单内容
      formObject: {
        categoryInfoRequests: [
          {
            categoryCode: '', //品类编码
            categoryId: '', //品类ID
            categoryName: '', //品类名称
            parentId: '' //父级ID
          }
        ],
        orgCode: '', //组织机构编码
        orgId: '', //组织机构id
        orgName: '', //组织机构名称
        planCode: '', //计划编码
        planId: '', //计划id
        planName: '' //计划名称
      },
      editStatus: false,
      planeArrList: [] //计划下拉数组
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    }
  },
  watch: {
    'formObject.orgIdArr': {
      handler() {
        this.$refs.dialogRef.validateField('orgIdArr')
      }
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.$nextTick(() => {
        // this.$set(this.buttons[1].buttonModel, "content", "保存");
        this.buttons = [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('保存') }
          }
        ]
      })
      let _data = { ...this.modalData.data }
      this.formObject = {
        id: _data.id, //配置Id
        dimensionName: _data.dimensionName, //名称
        remark: _data.remark //备注
      }
    }
  },
  async created() {
    // 初始化获取公司列表
    this.TreeByAccount()
    // 编辑
    if (this.modalData.isEdit) {
      let emitData = this.modalData.policyData //行内传值数据
      this.queryPlanByCompany() //调用计划接口
      this.formObject = emitData[0]
      this.formObject.orgId = '1475802394828308482' // 先写死 后期有公司对应 id  就可以了
    }
    // 查看
    // if (this.modalData.viewlock == 1) {
    //   let emitData = this.modalData.policyData; //行内传值数据
    //   this.strategies(emitData.id);
    //   this.editorIdstatus = true;
    // }
    // // 请求计划
    // let companyOrg = await this.getUserDetail();
    // if (!companyOrg || !companyOrg.id) {
    //   this.$hloading();
    //   this.$toast({ content: "获取当前组织信息失败，请重试", type: "error" });
    //   return;
    // }
    // this.getChildrenCompanyOrganization(companyOrg.id);
  },
  methods: {
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    // 选取公司递归
    fn(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.formInfo.orgId = ele.id
          this.formObject.orgCode = ele.orgCode
          this.formObject.orgId = ele.id
          this.formObject.orgName = ele.name
          return
        }
        if (ele.children && ele.children.length > 0) {
          this.fn(ele.children, id)
        }
      })
    },

    // 选择公司
    selectCompany(e) {
      if (e.itemData) {
        let { itemData } = e
        this.fn(this.fieldsarr.dataSource, itemData.id)
        this.queryPlanByCompany() //调用计划接口
      }
    },
    // 品类数组递归去重
    fcin(data, e, arr) {
      data.forEach((item) => {
        if (e.includes(item.categoryCode)) {
          arr.push({
            categoryCode: item.categoryCode,
            categoryId: item.id,
            categoryName: item.categoryName,
            parentId: item.parentId
          })
        }
        if (Array.isArray(item.childrens)) {
          this.fcin(item.childrens, e, arr)
        }
      })
    },
    // 品类选中事件
    selectCategoryas(e) {
      this.formObject.categoryInfoRequests = []
      this.fcin(this.fields.dataSource, e, this.formObject.categoryInfoRequests)
      this.$refs.dialogRef.validateField('categorytree')
    },
    // 获取计划品类列表
    TreeByPlanIdangeCat() {
      this.$API.analysisOfSetting['TreeByPlanIdangeCat']({
        planId: this.formInfo.planId
      }).then((result) => {
        this.$set(this.fields, 'dataSource', [...result.data])
        this.$set(this.fields, 'key', this.randomString())
      })
    },
    // 选择计划点击事件
    selectPlan(e) {
      let { itemData } = e
      this.formInfo.planId = itemData.id //获取品类传值id
      this.formObject.planId = itemData.id //获取品类传值id
      this.formObject.planCode = itemData.planCode
      this.formObject.planName = itemData.planName
      this.TreeByPlanIdangeCat() // 获取计划品类列表
    },

    // 获取计划模板
    queryPlanByCompany() {
      this.$API.analysisOfSetting['queryPlanByCompany']({
        companyId: this.formInfo.orgId // 传值级联公司id
      }).then((result) => {
        this.planeArrList = result.data
      })
    },
    // 初始化获取公司列表
    TreeByAccount() {
      this.$API.analysisOfSetting['TreeByAccount']({}).then((res) => {
        this.$set(this.fieldsarr, 'dataSource', [...res.data])
      })
    },
    // 确认按钮
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let addBuyerAssessPlanCategoryRangeRequest = JSON.parse(JSON.stringify(this.formObject))
          delete addBuyerAssessPlanCategoryRangeRequest.orgIdArr // 公司
          // let updateBuyerAssessPlanCategoryRangeRequest = JSON.parse(
          //   JSON.stringify(this.formObject) //编辑得
          // );
          if (this.editStatus) {
            // console.log('维度设置--编辑', updateBuyerAssessPlanCategoryRangeRequest)
            // this.$API.analysisOfSetting
            //   .categoryStupdate(updateBuyerAssessPlanCategoryRangeRequest)
            //   .then((res) => {
            //     if (res.code == 200) {
            //       this.$emit("confirm-function"); //关闭弹窗
            //       this.$refs.templateRef.refreshCurrentGridData(); //刷新表格统一方法
            //     }
            //   });
          } else {
            // console.log(
            //   "维度设置--新增",
            //   addBuyerAssessPlanCategoryRangeRequest
            // );
            this.$API.analysisOfSetting
              .categoryStrategyAdd(addBuyerAssessPlanCategoryRangeRequest)
              .then((res) => {
                if (res.code == 200) {
                  this.$emit('confirm-function') //关闭弹窗
                  this.$refs.templateRef.refreshCurrentGridData() //刷新表格统一方法
                }
              })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
/* .dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.browse {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    background: transparent;
    opacity: 0;
  }
  .show-input {
    width: 90%;
    height: 80%;
    background: transparent;
  }
} */
</style>
