<template>
  <div style="height: 100%; background: #fff">
    <div class="detail-header--wrap">
      <p class="detail-header-name">
        {{ detailData.code }}
        <span class="tags tag1">{{ detailData.bizTypeName }}</span>
        <span class="tags tag1">{{ isPublishName }}</span>
        <span class="tags tag2" v-if="detailData.isNotify == 1">{{ $t('通知供应商') }}</span>
        <span class="detail-header-button--wrap">
          <i class="detail-header-button">{{ $t('评审结果') }}：{{ reviewResultName }}</i>
          <mt-button type="text" class="detail-header-button" @click="backDetail">{{
            $t('返回')
          }}</mt-button>
          <mt-button
            type="text"
            class="detail-header-button"
            @click="submitReview"
            v-if="detailData.status == 10 || detailData.status == 30"
            >{{ $t('提交评审申请') }}</mt-button
          >
          <mt-button
            type="text"
            class="detail-header-button"
            @click="resultReview"
            v-if="detailData.status == 50"
            >{{ $t('结果提交审批') }}</mt-button
          >
        </span>
      </p>
      <p class="detail-header-category detail-header-items">
        <span class="detail-header-item">{{ $t('公司：') }}{{ detailData.buyerOrgName }}</span>
        <span class="detail-header-item"
          >{{ $t('供应商名称：') }}{{ detailData.supplierName }}</span
        >
        <span class="detail-header-item">{{ $t('评审内容：') }}{{ detailData.content }}</span>
        <span class="detail-header-item">
          {{ $t('计划时间：') }}{{ detailData.planTimeBegin }}-{{ detailData.planTimeEnd }}
        </span>
        <span class="detail-header-item" v-if="detailData.bizType != 4"
          >{{ $t('评审组长：') }}{{ detailData.taskLeaderName }}</span
        >
      </p>
      <p class="detail-header-items">{{ $t('评审规则：') }} {{ detailData.ruleNames }}</p>
      <p class="detail-header-items">
        <span class="detail-header-item">{{ $t('品类：') }}{{ detailData.categoryName }}</span>
        <span class="detail-header-item">{{ $t('创建人：') }}{{ detailData.createUserName }}</span>
        <span class="detail-header-item">{{ $t('创建日期：') }}{{ detailData.createTime }}</span>
      </p>
    </div>
    <div class="detail-table--wrap" v-if="insouse">
      <mt-template-page
        ref="templateRef"
        :use-tool-template="false"
        :template-config="detailPageConfig"
        @input="getResultDataOrder"
      >
        <order-item
          slot="slot-0"
          v-if="detailData.templateResponses"
          :order-data="detailData.templateResponses"
          :info="detailData"
          :get-detail="getDetailData"
        ></order-item>
        <team-template
          slot="slot-1"
          :team-data="
            detailData.expertMembersResponses
              ? detailData.expertMembersResponses
              : tempExpertResponseData
          "
        ></team-template>
        <below-item
          slot="slot-2"
          v-if="detailData.notQualifyResponses"
          :below-data="detailData.notQualifyResponses"
        ></below-item>
        <result-range
          slot="slot-3"
          v-if="detailData.resultItemResponses"
          :result-data="detailData.resultItemResponses"
          :info="detailData"
        ></result-range>
      </mt-template-page>
    </div>
    <!-- 二组数据 -->
    <div v-if="oneinsouse" class="detail-table--wrap">
      <mt-template-page
        ref="templateRef"
        :use-tool-template="false"
        :template-config="newdetailPageConfig"
      >
        <order-item
          slot="slot-0"
          :order-data="detailData.templateResponses"
          :info="detailData"
          :get-detail="getDetailData"
        ></order-item>
        <team-template
          slot="slot-1"
          :team-data="
            detailData.expertMembersResponses
              ? detailData.expertMembersResponses
              : tempExpertResponseData
          "
        ></team-template>
      </mt-template-page>
    </div>
  </div>
</template>
<script>
import { detailPageConfig, newdetailPageConfig } from '../template/config/index'
export default {
  components: {
    orderItem: () => import('../template/components/orderItem/index.vue'),
    teamTemplate: () => import('../template/components/teamTemplate/index.vue'),
    belowItem: () => import('../template/components/belowItem/index.vue'),
    resultRange: () => import('../template/components/resultRange/index.vue')
  },
  data() {
    return {
      oneinsouse: false,
      insouse: false,
      detailPageConfig,
      newdetailPageConfig,
      tabConfig: { headerPlacement: 'left' },
      detailData: {},
      resultParam: [],
      orderItemData: {},
      isPublishENUM: [
        { text: this.$t('已发布'), value: 1 },
        { text: this.$t('未发布'), value: 0 }
      ],
      publishStatus: null,
      tempExpertResponseData: {
        firstLevelResponse: null,
        reviewResponse: null
      }
    }
  },
  created() {
    this.getDetailData()
  },
  mounted() {
    const isPublish = this.detailData.isPublish
    this.isPublishENUM.forEach((item) => {
      if (item.value === isPublish) {
        this.$nextTick(() => {
          this.publishStatus = item.text
        })
      }
    })
  },
  methods: {
    backDetail() {
      this.$router.go(-1)
      // this.$router.push({
      //   path: "/supplier/pur/review?tab=2",
      // });
    },
    // 请求数据
    getDetailData() {
      console.log('getDetailDatagetDetailDatagetDetailData')
      let form = new FormData()
      form.append('code', this.$route.query.code)
      console.log('form', form)
      this.$API.supplierReviewTask.getPreviewDetail(form).then((res) => {
        console.log('getDetailDatagetDetailDatagetDetailData', res)
        if (res.code == 200) {
          if (
            res.data.status == 10 ||
            res.data.status == 20 ||
            res.data.status == 30 ||
            res.data.status == 40
          ) {
            this.oneinsouse = true
            this.insouse = false
          }
          if (
            res.data.status == 50 ||
            res.data.status == 60 ||
            res.data.status == 70 ||
            res.data.status == 80 ||
            res.data.status == 90 ||
            res.data.status == 100 ||
            res.data.status == 112 ||
            res.data.status == 113 ||
            res.data.status == 114 ||
            res.data.status == 115 ||
            res.data.status == 116 ||
            res.data.status == 117
          ) {
            this.oneinsouse = false
            this.insouse = true
          }
          this.detailData = res.data // detail 接口数据请求

          // 时间戳转成时间
          this.detailData.planTimeBegin = this.$utils.formateTime(
            Number(this.detailData.planTimeBegin),
            'yyyy-MM-dd'
          )
          this.detailData.planTimeEnd = this.$utils.formateTime(
            Number(this.detailData.planTimeEnd),
            'yyyy-MM-dd'
          )
          // 拼接评审内容
          this.detailData.content = ''
          this.detailData.templateResponses.forEach((item, index) => {
            if (index !== this.detailData.templateResponses.length - 1) {
              this.detailData.content += item.templateTypeName + ','
            } else {
              this.detailData.content += item.templateTypeName
            }
          })
        }
      })
    },
    // submitReview() {
    //   this.$dialog({
    //     data: {
    //       title: this.$t('提交'),
    //       message: this.$t('提交审查申请')
    //     },
    //     success: () => {
    //       this.$API.analysisOfSetting.submitReviewApply([this.detailData.code]).then(() => {
    //         this.$toast({
    //           content: this.$t('提交成功'),
    //           type: 'success'
    //         })
    //         this.getDetailData()
    //       })
    //     }
    //   })
    // },
    resultReview() {
      this.$dialog({
        data: {
          title: this.$t('提交'),
          message: this.$t('结果提交审批')
        },
        success: () => {
          const param = this.resultParam.map((item) => {
            return {
              templateCode: item.templateCode,
              advantage: item.advantage,
              inferiority: item.inferiority,
              report: item.report
            }
          })
          this.$API.supplierReviewTask.sumitReviewApiRist(this.detailData.code, param).then(() => {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.getDetailData()
          })
        }
      })
    },
    getResultDataOrder(e) {
      this.resultParam = JSON.parse(JSON.stringify(e))
    }
  },
  computed: {
    reviewResultName() {
      const resultStatus = this.detailData.resultStatus
      return resultStatus === 0 ? this.$t('不通过') : resultStatus === 1 ? this.$t('已通过') : '--'
    },
    isPublishName() {
      const resultStatus = this.detailData.isPublish
      return resultStatus === 0 ? this.$t('已发布') : resultStatus === 1 ? this.$t('未发布') : '--'
    }
  }
}
</script>
<style lang="scss" scoped>
.detail-header--wrap {
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 20px 30px;
  margin-bottom: 16px;

  .detail-header-name {
    font-size: 20px;
    line-height: 32px;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
    .tags {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      padding: 2px;
      border-radius: 2px;
      margin-left: 10px;
    }
    .tag1 {
      color: rgb(237, 161, 51);
      background: rgba(237, 161, 51, 0.1);
    }
    .tag2 {
      color: rgb(99, 134, 193);
      background: rgba(99, 134, 193, 0.1);
    }
  }
  .detail-header-category {
    font-size: 12px;
    line-height: 16px;
    color: rgba(41, 41, 41, 1);
  }
  .detail-header-items {
    font-size: 14px;
    font-weight: 600;
    margin-top: 20px;
    color: rgba(41, 41, 41, 1);
    .detail-header-item {
      margin-right: 24px;
    }
  }

  .detail-header-button--wrap {
    float: right;
    .detail-header-button {
      margin-right: 24px;
    }
  }
}
</style>
