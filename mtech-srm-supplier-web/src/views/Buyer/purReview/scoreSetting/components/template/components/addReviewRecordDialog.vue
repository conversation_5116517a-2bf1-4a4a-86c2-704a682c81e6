<template>
  <mt-dialog
    ref="dialog"
    :enable-resize="false"
    :position="{ X: 'right', Y: 'top' }"
    css-class="right-wrapper"
    :buttons="buttons"
    :header="header"
    height="100%"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules" class="form-box">
        <mt-form-item prop="name" :label="$t('名称：')">
          <mt-input
            :placeholder="$t('请输入评审任务名称')"
            v-model="formObject.name"
            :disabled="enableEdit.name"
            :max-length="30"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="buyerOrgId" :label="$t('公司：')">
          <mt-DropDownTree
            :fields="fieldsCompany"
            :popup-height="500"
            id="baseTreeSelects"
            :placeholder="$t('请选择公司')"
            :show-clear-button="false"
            :key="fieldsCompany.key"
            v-model="selectCompay"
            @select="companyhandle"
            v-if="fieldsCompany.dataSource.length && !taskCode"
            :enabled="!enableEdit.buyerOrgId"
          ></mt-DropDownTree>
          <mt-select
            :placeholder="$t('请选择公司')"
            :fields="{ text: 'orgName', value: 'id' }"
            :data-source="[]"
            @change="companyhandle"
            v-if="!fieldsCompany.dataSource.length && !taskCode"
            :disabled="enableEdit.buyerOrgId"
          ></mt-select>
          <mt-input v-model="formObject.buyerOrgName" v-if="taskCode" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierInternalCode" :label="$t('供应商：')">
          <mt-select
            :placeholder="$t('请选择供应商')"
            :data-source="supplierList"
            :allow-filtering="true"
            v-model="formObject.supplierInternalCode"
            @change="supplierHandle"
            :fields="{ value: 'supplierInternalCode', text: 'supplierName' }"
            :disabled="enableEdit.supplierInternalCode"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="bizType" :label="$t('评审类型：')">
          <mt-select
            v-model="formObject.bizType"
            :data-source="reviewTypeTaskArray"
            :disabled="reviewDisabled || enableEdit.bizType"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            @change="bizTypeHandle"
            :show-clear-button="true"
            :placeholder="$t('请选择评审类型')"
            v-if="!taskCode"
          ></mt-select>
          <mt-input v-model="formObject.bizTypeName" v-if="taskCode" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="categoryId" :label="$t('品类：')">
          <mt-select
            :placeholder="$t('请选择品类')"
            :data-source="categoryList"
            :fields="{ value: 'categoryId', text: 'categoryName' }"
            :disabled="enableEdit.categoryId"
            v-model="formObject.categoryId"
            @select="categoryIdSelect"
            v-if="!taskCode"
          ></mt-select>
          <mt-input v-model="formObject.categoryName" v-if="taskCode" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="isNotify"
          :label="$t('通知供应商自查：')"
          v-if="formObject.bizType == 2"
        >
          <mt-select
            v-model="formObject.isNotify"
            :data-source="noticeList"
            :show-clear-button="true"
            :placeholder="$t('通知供应商自查')"
            :disabled="enableEdit.isNotify"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          prop="modelBuesness"
          :label="$t('评审业务类型：')"
          v-if="formObject.bizType == 2 || formObject.bizType == 1"
        >
          <mt-DropDownTree
            :fields="fieldsTemplate"
            :popup-height="500"
            :show-check-box="true"
            id="baseTreeSelect"
            :placeholder="$t('请选择评审业务类型')"
            :allow-multi-selection="true"
            :show-clear-button="true"
            @select="nodeOnCheck"
            :auto-check="true"
            :key="fieldsTemplate.key"
            v-model="formObject.modelBuesness"
            :disabled="enableEdit.modelBuesness"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item
          prop="sceneDefineId"
          :label="$t('场景：')"
          v-if="
            formObject.bizType != 2 &&
            formObject.bizType != 1 &&
            formObject.categoryId &&
            formObject.supplierInternalCode
          "
        >
          <mt-select
            :placeholder="$t('请选择场景')"
            :data-source="sceneList"
            :fields="{ value: 'id', text: 'sceneName' }"
            v-model="formObject.sceneDefineId"
            :disabled="enableEdit.sceneDefineId"
            @change="sceneChange"
            v-if="!taskCode"
          ></mt-select>
          <mt-input
            v-model="formObject.sceneDefineName"
            v-if="taskCode"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="categoryArr"
          :label="$t('评审包：')"
          v-if="formObject.bizType != 2 && formObject.bizType != 1"
        >
          <mt-input v-model="packageName" disabled></mt-input>
        </mt-form-item>
        <mt-form-item prop="planTimeEnd" :label="$t('计划时间：')">
          <mt-date-range-picker
            :placeholder="$t('选择开始时间和结束时间')"
            :open-on-focus="true"
            v-model="timeRanges"
            @change="timeHandle"
            :disabled="enableEdit.Range"
          ></mt-date-range-picker>
        </mt-form-item>
        <mt-form-item
          prop="companyRegisterAddr"
          v-if="taskCode !== 'selfTempType'"
          :label="$t('注册地址')"
        >
          <mt-input
            :disabled="!enableEdit.buyerOrgId"
            v-model="formObject.companyRegisterAddr"
            v-if="taskCode !== 'selfTempType'"
            :max-length="30"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="factoryAddr" v-if="taskCode !== 'selfTempType'" :label="$t('工厂地址')">
          <mt-input
            :placeholder="$t('请输入工厂地址')"
            v-model="formObject.factoryAddr"
            v-if="taskCode !== 'selfTempType'"
            :max-length="30"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplyType" v-if="taskCode !== 'selfTempType'" :label="$t('抽取方式')">
          <mt-select
            :placeholder="$t('请选择抽取方式')"
            :data-source="supplierType"
            v-model="formObject.supplyType"
            @change="changeSupplierType"
            v-if="taskCode !== 'selfTempType'"
            :fields="{ value: 'itemCode', text: 'itemName' }"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('条件：')" style="width: 100%">
          <div class="filters" v-for="(f, _i) in formObject.ruleLists" :key="'filter' + _i">
            <mt-form-item
              style="width: 30%; margin-right: 2%"
              :prop="'ruleLists[' + _i + '].joinSymbol'"
              :rules="[
                {
                  validator: (rule, value, callback) => {
                    if (!f.joinSymbol) {
                      callback($t('请选择'))
                    } else {
                      callback()
                    }
                  },
                  trigger: 'blur'
                }
              ]"
            >
              <mt-select
                :data-source="filterOrAnd"
                style="width: 100%"
                :placeholder="$t('请选择')"
                :disabled="reviewStatus || enableEdit.rules"
                v-model="f.joinSymbol"
                v-if="_i != 0"
              ></mt-select>
              <mt-select
                :data-source="filterOrAndZero"
                style="width: 100%"
                :placeholder="$t('请选择')"
                :disabled="reviewStatus || !enableEdit.rules"
                v-model="f.joinSymbol"
                v-if="_i == 0"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              style="width: 50%; margin-right: 2%"
              :prop="'ruleLists[' + _i + '].templateTypeId'"
              :rules="[
                {
                  validator: (rule, value, callback) => {
                    if (!f.templateTypeId) {
                      callback($t('请选择'))
                    } else {
                      callback()
                    }
                  },
                  trigger: 'blur'
                }
              ]"
            >
              <mt-select
                :data-source="selectdBussness"
                style="width: 100%"
                :value="f.templateTypeId"
                @change="templateChange($event, f)"
                :open-dispatch-change="false"
                :disabled="reviewStatus || enableEdit.rules"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              style="width: 40%; margin-right: 2%"
              :prop="'ruleLists[' + _i + '].conditionObject'"
              :rules="[
                {
                  validator: (rule, value, callback) => {
                    if (!f.conditionObject) {
                      callback($t('请选择'))
                    } else {
                      callback()
                    }
                  },
                  trigger: 'blur'
                }
              ]"
            >
              <mt-select
                :data-source="filterResultList"
                style="width: 100%"
                v-model="f.conditionObject"
                :disabled="reviewStatus || enableEdit.rules"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              style="width: 40%; margin-right: 2%"
              :prop="'ruleLists[' + _i + '].symbol'"
              :rules="[
                {
                  validator: (rule, value, callback) => {
                    if (!f.symbol) {
                      callback($t('请选择'))
                    } else {
                      callback()
                    }
                  },
                  trigger: 'blur'
                }
              ]"
            >
              <mt-select
                :data-source="filterEqualList"
                style="width: 100%"
                v-model="f.symbol"
                :disabled="reviewStatus || enableEdit.rules"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              style="width: 40%; margin-right: 2%"
              :prop="'ruleLists[' + _i + '].standard'"
              :rules="[
                {
                  validator: (rule, value, callback) => {
                    if (!f.standard) {
                      callback($t('请选择'))
                    } else {
                      callback()
                    }
                  },
                  trigger: 'blur'
                }
              ]"
            >
              <mt-input
                style="width: 100%"
                :placeholder="$t('请输入')"
                :disabled="reviewStatus || enableEdit.rules"
                v-if="f.conditionObject == 4"
                v-model="f.standard"
              ></mt-input>
              <mt-inputNumber
                v-else
                :placeholder="$t('请输入')"
                css-class="inputNumber"
                :disabled="reviewStatus || enableEdit.rules"
                v-model="f.standard"
                :min="0"
                :max="1000"
                width="120"
              ></mt-inputNumber>
            </mt-form-item>
            <mt-form-item style="width: 240px">
              <i
                class="mt-icons mt-icon-icon_card_plus iconSize"
                @click="addCondition"
                v-if="!reviewStatus && !enableEdit.rules"
              ></i>
              <i
                class="mt-icons mt-icon-icon_card_minus iconSize"
                @click="delConditon(_i)"
                v-if="_i !== 0 && !reviewStatus && !enableEdit.rules"
              ></i>
            </mt-form-item>
          </div>
        </mt-form-item>
      </mt-form>

      <div v-show="isGeneralProcurement || taskCode === 'selfTempType'">
        <h2 class="h2" v-if="formObject.bizType != 4">{{ $t('团队成员') }}</h2>
        <teamTemplate
          :team-data="formObject.userLists"
          :team-o-rg-item="teamORgItem"
          v-if="formObject.bizType != 4 && refresh"
        ></teamTemplate>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
// import { conditionObject } from "../../../../../moduleConfig/PolicySettings/components/reviewHomepage/config";

import teamTemplate from '../components/teamTemplate/teamTeamplateAdd.vue'
import utils from '@/utils/utils'
import { utils as $utils } from '@mtech-common/utils'
export default {
  data() {
    var modelBuesness = (rule, value, callback) => {
      const Value = this.formObject.modelBuesness
      if (!Value || !Value.length) {
        callback(new Error(this.$t('请选择业务类型')))
      } else {
        callback()
      }
    }
    return {
      supplierType: [
        { itemCode: 1, itemName: this.$t('系统抽取') },
        { itemCode: 0, itemName: this.$t('手动选择') }
      ],
      // 通采 or 非采
      isGeneralProcurement: false,
      // 公司
      companyList: [],
      // 供应商列表
      supplierList: [],
      // 品类
      categoryList: [],
      // 评审类型
      reviewTypeList: [
        { itemCode: '1', itemName: this.$t('例行审查') },
        { itemCode: '2', itemName: this.$t('专项审查') },
        { itemCode: '3', itemName: this.$t('认证审查-现场审查') },
        { itemCode: '4', itemName: this.$t('认证审查-供应商自查') }
      ],
      // 通知供应商自查
      noticeList: [
        {
          value: 0,
          text: this.$t('否')
        },
        { value: 1, text: this.$t('是') }
      ],
      // 评审业务类型
      fieldsTemplate: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children',
        key: this.randomString()
      },
      fieldsCompany: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children',
        code: 'id',
        key: this.randomString()
      },
      // 场景
      sceneList: [],
      packageName: '',

      // 条件
      selectdBussness: [],
      // 评审条件得分
      filterResultList: [
        { value: '1', text: this.$t('得分') },
        { value: '2', text: this.$t('合格项') },
        { value: '3', text: this.$t('不合格项') },
        { value: '4', text: this.$t('结果') }
      ],
      filterEqualList: [
        //评审运算 = >
        { value: '1', text: '>' },
        { value: '2', text: '<' },
        { value: '3', text: '>=' },
        { value: '4', text: '<=' },
        { value: '5', text: '=' },
        { value: '6', text: '!=' }
      ],
      filterOrAnd: [
        { value: '1', text: this.$t('且') },
        { value: '2', text: this.$t('或') }
      ],
      filterOrAndZero: [{ value: '0', text: this.$t('当') }],
      conditionJson: {
        conditionObject: undefined,
        symbol: undefined,
        standard: undefined,
        joinSymbol: '0',
        templateTypeId: undefined
      },
      formObject: {
        name: null, //名字
        supplierInternalCode: null,
        supplierCode: null, // 供应商code
        supplierName: null, // 供应商name
        supplierId: null, // 供应商name                  缺失
        partnerArchiveId: null, // 供应商档案id
        partnerRelationId: null, // 供应商关系id
        supplierEnterpriseId: null, //供应商企业id
        supplierEnterpriseCode: null, //供应商企业code
        supplierEnterpriseName: null, // 供应商企业名称
        buyerOrgId: null, // 采方组织id
        buyerOrgCode: null, // 采方组织code
        buyerOrgName: null, // 采方组织名字
        bizType: '', // 评审类型
        categoryId: null, // 分类id
        categoryCode: null, // 分类code
        categoryName: null, // 分类名称

        isNotify: null, // 是否通知供应商
        modelBuesness: null, // 评审业务类型

        planTimeBegin: null, // 开始时间
        planTimeEnd: null, //ji结束时间

        templateList: [], //模板集合
        ruleLists: [
          {
            conditionObject: undefined,
            symbol: undefined,
            standard: undefined,
            joinSymbol: '0',
            templateTypeId: undefined
          }
        ], //规则集合
        userLists: [], //用户组
        sceneDefineId: null, // 场景id
        sceneDefineName: null // 场景name
      },
      editStatus: false, // 是不是编辑
      reviewStatus: false,

      // 时间
      timeRanges: null,
      // 表格
      teamORgItem: {},
      rules: {
        name: [
          {
            required: true,
            message: this.$t('请输入评审名称'),
            trigger: 'blur'
          }
        ],
        buyerOrgId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        supplierInternalCode: [
          { required: true, message: this.$t('请选择供应商'), trigger: 'blur' }
        ],
        bizType: [
          {
            required: true,
            message: this.$t('请选择评审类型'),
            trigger: 'blur'
          }
        ],
        categoryId: [{ required: true, message: this.$t('请选择分类'), trigger: 'blur' }],
        sceneDefineId: [{ required: true, message: this.$t('请选择场景'), trigger: 'blur' }],
        isNotify: [
          {
            required: true,
            message: this.$t('请选择是否通知供应商自查'),
            trigger: 'blur'
          }
        ],
        modelBuesness: [{ validator: modelBuesness, trigger: 'blur' }],
        planTimeEnd: [{ required: true, message: this.$t('请输入日期'), trigger: 'blur' }],
        supplyType: [{ required: true, message: this.$t('请选择抽取方式'), trigger: 'blur' }],
        companyRegisterAdd: [
          { required: true, message: this.$t('请输入注册地址'), trigger: 'blur' }
        ]
      },
      selectCompay: [],
      isFirst: true,
      modelBuesnessBak: [],
      isClearRules: false,
      refresh: true // 由于之前程序员写的程序结构导致渲染时序有问题，综合考虑后觉得当userLists从后台取得数据之后去刷新表格最合适
    }
  },
  components: {
    teamTemplate
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    info() {
      return this.modalData.info
    },
    taskCode() {
      return this.modalData.taskCode
    },
    reviewTypeTaskArray() {
      let e = this.reviewTypeList
      if (!this.editStatus) {
        e = this.reviewTypeList.filter((i) => i.itemCode != 1)
      } else {
        if (this.formObject.bizType == 1) {
          e = this.reviewTypeList.filter((i) => i.itemCode == 1)
        }
        if (this.formObject.bizType == 3 || this.formObject.bizType == 4) {
          e = this.reviewTypeList.filter((i) => i.itemCode != 1)
        }
      }
      return e
    },
    reviewDisabled() {
      return this.editStatus
    },
    buttons() {
      const action = this.editStatus ? this.update : this.submit
      if (this.formObject.bizType == '4') {
        // 自查状态
        return [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: action,
            buttonModel: { isPrimary: 'true', content: this.$t('完成') }
          }
        ]
      }
      return [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('下一步') }
        }
      ]
    },
    enableEdit() {
      return {
        name: false,
        buyerOrgId: this.editStatus,
        supplierInternalCode: false,
        categoryId: this.editStatus,
        bizType: this.editStatus,
        sceneDefineId:
          this.editStatus && (this.formObject.bizType == 3 || this.formObject.bizType == 4),
        // Range:
        //   this.editStatus &&
        //   (this.formObject.bizType == 3 || this.formObject.bizType == 4),
        Range: false,
        rules: this.editStatus && (this.formObject.bizType == 3 || this.formObject.bizType == 4),
        modelBuesness:
          this.editStatus && (this.formObject.bizType == 3 || this.formObject.bizType == 4),
        isNotify: this.editStatus && (this.formObject.bizType == 3 || this.formObject.bizType == 4)
      }
    },
    selectdBussnessNotice() {
      return JSON.parse(JSON.stringify(this.selectdBussness))
    }
  },
  mounted() {
    console.log('taskCode', this.modalData.taskCode)
    this.$refs['dialog'].ejsRef.show()
    console.log('this.modalData ', this.modalData)
    if (this.modalData && this.modalData.code) {
      this.editStatus = true
      let form = new FormData()
      form.append('code', this.modalData.code)
      this.$API.supplierReviewTask.getPreviewDetail(form).then((res) => {
        console.log('getPreviewDetailgetPreviewDetail', this.formObject)
        this.formObject = res.data
        this.selectCompay = [res.data.buyerOrgId]
        this.getSupplierList(res.data.buyerOrgId)
        this.getCategoryList(res.data.categoryId)
        this.formObject.bizType = `${this.formObject.bizType}`
        if (this.formObject.bizType == '3' || this.formObject.bizType == 4) {
          this.queryByOrgIdAndCategoryId()
        }
        this.formObject.ruleLists = JSON.parse(JSON.stringify(this.formObject.ruleResponses))
        if (!this.formObject.ruleLists.length) {
          this.formObject.ruleLists.push({
            conditionObject: '',
            symbol: '',
            standard: '',
            joinSymbol: '0',
            templateTypeId: ''
          })
        }
        this.formObject.userLists = JSON.parse(JSON.stringify(this.formObject.userResponses))
        this.refreshTeamTable()
        if (this.formObject.planTimeBegin && this.formObject.planTimeEnd) {
          this.timeRanges = [
            new Date(Number(this.formObject.planTimeBegin)),
            new Date(Number(this.formObject.planTimeEnd))
          ]
        } else {
          this.timeRanges = []
        }
        this.formObject.modelBuesness = this.formObject.ruleLists.map((i) => {
          if (i.templateTypeId) {
            return i.templateTypeId
          }
        })
        this.formObject.modelBuesness = this.formObject.modelBuesness.filter((i) => i)
        if (!this.formObject.modelBuesness.length) {
          this.formObject.modelBuesness = null
        }
        this.modelBuesnessBak = JSON.stringify(this.formObject.modelBuesness)
      })
    }
  },
  async created() {
    console.log('this.taskCode', this.taskCode)
    if (this.taskCode) {
      await this.getCateSupplierList()
      await this.getReviewTypeTemplate()
    } else {
      // 初始化获取公司列表
      await this.TreeByAccount()
      // await this.getReviewType();
      await this.getReviewTypeTemplate()
      this.getSceneList()
    }
  },
  watch: {
    'formObject.orgIdArr': {
      handler() {
        this.$refs.dialogRef.validateField('orgIdArr')
      }
    },
    selectdBussnessNotice: {
      handler(n, o) {
        if (JSON.stringify(n) != JSON.stringify(o) && o.length) {
          this.formObject.ruleLists.forEach(() => {
            // e.templateTypeId = null
            // console.log('8888', e.templateTypeId)
          })
        }
      },
      deep: true
    }
  },
  methods: {
    // 供应类型选择事件
    changeSupplierType({ itemData }) {
      console.log('{ itemData }{ itemData }', itemData)
      this.formObject.supplierInternalType
      if (itemData.itemCode === 0) {
        this.isGeneralProcurement = true
      } else {
        this.isGeneralProcurement = false
      }
    },
    refreshTeamTable() {
      this.refresh = false
      this.$nextTick(() => {
        this.refresh = true
      })
    },
    // 品类认证页面获取供应商列表
    getCateSupplierList() {
      this.$API.CategoryCertification.getSupListByAuthCode({
        authProjectCode: this.info.projectCode
      }).then((res) => {
        console.log('getSupListByAuthCode', res)
        if (res.code == 200 && !utils.isEmpty(res.data)) {
          this.supplierList = res.data
        } else {
          this.supplierList = []
        }
      })
    },
    // 获取场景下拉
    getSceneList() {
      this.$API.supplierReviewTask.getSceneList().then((res) => {
        this.sceneList = res?.data
      })
    },
    // 获取随机数
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    // 初始化获取公司列表
    TreeByAccount() {
      const param = {
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true
      }
      this.$API.supplierReviewTask.getMasterCompay(param).then((res) => {
        this.fieldsCompany.dataSource = res.data || []
      })
    },
    // 评审类型
    getReviewType() {
      this.$API.supplierReviewTask
        .getMasterReviewType({ dictCode: 'reviewTaskType' })
        .then((res) => {
          this.reviewTypeList = res.data || []
          // this.reviewTypeList.forEach((item) => {
          //   item.itemCode = Number(item.itemCode);
          // });
        })
    },
    // 评审业务类型
    getReviewTypeTemplate() {
      this.$API.supplierReviewTask
        .getMasterReviewTemplateType({ dictCode: 'reviewType' })
        .then((res) => {
          this.fieldsTemplate.dataSource = res.data || []
          this.fieldsTemplate.key = this.randomString()
        })
    },
    // 选择公司
    companyhandle(e) {
      const { id, text } = e.itemData
      this.formObject.buyerOrgId = id
      this.formObject.buyerOrgName = text
      this.fn(this.fieldsCompany.dataSource, id)
      this.getSupplierList(id)
      this.teamORgItem.organizationId = id
      this.teamORgItem.organizationName = text
    },
    fn(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.formObject.buyerOrgCode = ele.orgCode
          this.teamORgItem.organizationCode = ele.orgCode
          return
        }
        if (ele.children && ele.children.length > 0) {
          this.fn(ele.children, id)
        }
      })
    },
    // 获取供应商
    getSupplierList(orgId) {
      const param = { orgId }
      this.$API.supplierReviewTask.getMasterSupplier(param).then((res) => {
        this.supplierList = res.data || []
      })
    },
    // 选择供应商
    supplierHandle({ itemData }) {
      this.getDetailAddress(itemData.supplierEnterpriseId)
      if (this.taskCode) {
        this.formObject.partnerArchiveId = itemData.partnerArchiveId
        this.formObject.partnerRelationId = itemData.partnerRelationId
        this.formObject.supplierCode = itemData.supplierCode
        this.formObject.supplierName = itemData.supplierName
        this.formObject.supplierEnterpriseId = itemData.supplierEnterpriseId
        this.formObject.supplierInternalCode = itemData.supplierInternalCode
        this.formObject.supplierEnterpriseCode = itemData.supplierEnterpriseCode
        this.formObject.supplierEnterpriseName = itemData.supplierEnterpriseName
        this.formObject.categoryId = itemData.categoryId
        this.formObject.categoryCode = itemData.categoryCode
        this.formObject.categoryName = itemData.categoryName
        this.formObject.buyerOrgId = itemData.orgId
        this.formObject.buyerOrgCode = itemData.orgCode
        this.formObject.buyerOrgName = itemData.orgName
        this.teamORgItem.organizationId = itemData.orgId
        this.teamORgItem.organizationName = itemData.orgName
        this.teamORgItem.organizationCode = itemData.orgCode
        this.formObject.sceneDefineId = itemData.sceneId
        this.formObject.sceneDefineName = itemData.sceneName
        this.formObject.authProjectCode = itemData.authProjectCode
        this.formObject.authProjectId = itemData.authProjectId
        this.formObject.authProjectName = itemData.authProjectName
        this.formObject.bizType = this.taskCode == 'selfTempType' ? 4 : 3
        this.formObject.bizTypeName =
          this.taskCode == 'selfTempType'
            ? this.$t('认证审查-供应商自查')
            : this.$t('认证审查-现场审查')
        this.queryByOrgIdAndCategoryId()
      } else {
        const {
          id,
          partnerArchiveId,
          supplierCode,
          supplierName,
          supplierEnterpriseCode,
          supplierEnterpriseId,
          supplierEnterpriseName,
          supplierInternalCode
        } = itemData
        this.formObject.partnerArchiveId = partnerArchiveId
        this.formObject.partnerRelationId = id
        this.formObject.supplierCode = supplierCode
        this.formObject.supplierName = supplierName
        this.formObject.supplierEnterpriseId = supplierEnterpriseId
        this.formObject.supplierEnterpriseCode = supplierEnterpriseCode
        this.formObject.supplierEnterpriseName = supplierEnterpriseName
        this.formObject.supplierInternalCode = supplierInternalCode
        this.getCategoryList(id)
      }
    },
    // 获取注册地址
    getDetailAddress(id) {
      console.log('this.getDetailAddress(itemData.id)', id)
      const param = { id }
      this.$API.supplierReviewTask.getDetailAddress(param).then((res) => {
        this.formObject.companyRegisterAddr = res.data.detailAddress || null
      })
    },
    // 获取品类
    getCategoryList(partnerRelationId) {
      const param = { partnerRelationId }
      this.$API.supplierReviewTask.getMasterCate(param).then((res) => {
        this.categoryList = res.data || []
      })
    },
    // 选择品类
    categoryIdSelect({ itemData }) {
      const { categoryCode, categoryId, categoryName } = itemData
      this.formObject.categoryId = categoryId
      this.formObject.categoryCode = categoryCode
      this.formObject.categoryName = categoryName
    },
    // 选择时间、
    timeHandle(e) {
      if (e.startDate) {
        this.formObject.planTimeBegin = new Date(e.startDate).getTime()
        this.formObject.planTimeEnd = new Date(e.endDate).getTime()
      } else {
        this.formObject.planTimeBegin = null
        this.formObject.planTimeEnd = null
      }
    },

    // 添加条件
    addCondition() {
      // const selectdBussness = JSON.parse(JSON.stringify(this.selectdBussness))
      // delete this.selectdBussness
      const selectdBussness = JSON.parse(JSON.stringify(this.selectdBussness))
      // this.selectdBussness = selectdBussness
      this.$nextTick(() => {
        this.selectdBussness = selectdBussness
      })
      const conditions = {
        conditionObject: '',
        symbol: '',
        standard: '',
        joinSymbol: '',
        templateTypeId: ''
      }
      // const formObject = JSON.parse(JSON.stringify(this.formObject))
      this.formObject.ruleLists.push(conditions)
      // console.log('this.formObject123', formObject)
      // this.$set(this, 'formObject', formObject)
      // this.selectdBussness = selectdBussness
      console.log('this.formObject', this.selectdBussness)
    },
    // 删除条件
    delConditon(i) {
      const formObject = JSON.parse(JSON.stringify(this.formObject))
      formObject.ruleLists = formObject.ruleLists.filter((v, _i) => _i != i)
      this.$set(this, 'formObject', formObject)
    },
    // 确认按钮
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          console.log('{ itemData }{ itemData }', this.formObject.supplierInternalType)
          // 判断当前的通采 非采的类型
          // 能不能点击下一步，通采： 调接口成功失败抽取专家，非采： 继续下一步

          // 调接口 请求抽取专家 专家抽取成功即提示抽取完成，失败则提示：审查专家抽取失败
          if (this.formObject.supplyType === 1) {
            // 1.获取评审模板
            // const ids = this.formObject.ruleLists.map((v) => v.templateTypeId)
            const ids = this.templateRuleLists.split(',')
            const orgId = this.formObject.buyerOrgId
            const categoryId = this.formObject.categoryId
            const params = {
              orgId,
              categoryId,
              ids
            }
            this.$API.supplierReviewTask
              .getPrestItem(params)
              .then((res) => {
                let templateArr = []
                res.data.forEach((item) => {
                  let obj = {
                    templateCode: item.templateCode,
                    templateTypeName: item.templateTypeName,
                    templateTypeId: item.templateTypeId,
                    templateItemRequests: []
                  }
                  if (item.dimensionDTOS) {
                    item.dimensionDTOS.forEach((e) => {
                      if (e.indexDTOS) {
                        e.indexDTOS.forEach((e1) => {
                          obj.templateItemRequests.push({
                            itemCode: e1.reviewCode
                          })
                        })
                      }
                    })
                  }
                  templateArr.push(obj)
                })
                // 2.获取模板成功后，调用保存add接口，新增专家
                // 2.1 入参拼接
                this.formObject.templateLists = templateArr
                // console.log('getPrestItemgetPrestItem', this.formObject)
                const param = $utils.cloneDeep(this.formObject)
                delete param.userLists
                this.$API.supplierReviewTask.taskReviewTaskSave(param).then(() => {
                  this.$emit('confirm-function')
                })
              })
              .catch((e) => {
                console.log('errorerrorerror', e)
              })

            return
          }

          if (!this.formObject.userLists.length) {
            console.log('请添加团队成员!', this.formObject.userLists.length)
            this.$toast({
              content: this.$t('请添加团队成员!'),
              type: 'warning'
            })
            return
          }
          const leaders = this.formObject.userLists.filter((v) => v.roleCode == 'leader')
          if (!leaders.length) {
            this.$toast({
              content: this.$t('未设置组长，请设置!'),
              type: 'warning'
            })
            return
          }
          const ids = this.formObject.ruleLists.map((v) => v.templateTypeId)
          const orgId = this.formObject.buyerOrgId
          const categoryId = this.formObject.categoryId
          const params = {
            orgId,
            categoryId,
            ids
          }
          // 编辑评审清单-数据回显
          // 如果 评审类型 为 【例行审查】，点击 下一步 需要单独请求接口去获取 评审项页 编辑回显数据（和新增一样）
          if (this.editStatus) {
            if (!this.formObject.templateResponses) {
              this.$API.supplierReviewTask.getPrestItem(params).then((res) => {
                this.dialogItem(this.formObject, res.data)
              })
            } else {
              if (this.modelBuesnessBak === JSON.stringify(ids)) {
                const data = JSON.parse(JSON.stringify(this.formObject.templateResponses))
                data.forEach((item) => {
                  item.itemResponses.forEach((e) => {
                    e.dimensionName = e.itemName
                    e.itemResponses.forEach((e1) => {
                      e1.reviewName = e1.itemName
                      e1.reviewCode = e1.itemCode
                    })
                    e.indexDTOS = JSON.parse(JSON.stringify(e.itemResponses))
                  })
                  item.dimensionDTOS = JSON.parse(JSON.stringify(item.itemResponses))
                })
                this.dialogItem(this.formObject, data)
              } else {
                this.$API.supplierReviewTask.getPrestItem(params).then((res) => {
                  this.dialogItem(this.formObject, res.data)
                })
              }
            }
          } else {
            this.$API.supplierReviewTask.getPrestItem(params).then((res) => {
              this.dialogItem(this.formObject, res.data)
            })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 选择评审业务类型
    nodeOnCheck({ itemData }) {
      if (!itemData) return
      const { id, hasChildren, isChecked, checked } = itemData
      console.log('itemDataitemDataitemData', itemData, checked)
      // 如果 又checked 代表点击右上角的全部清除，则直接赋值为空
      if (checked) {
        this.selectdBussness = []
        return
      }
      if (hasChildren) {
        const [result] = this.filterSiblings(id)
        const preitem = this.getStringByObj(result)
        preitem.forEach((item) => {
          const keysBusness = []
          this.selectdBussness.map((i) => {
            keysBusness.push(i.value)
          })
          if (isChecked == 'true' && !keysBusness.includes(item.value)) {
            this.selectdBussness.push(item)
          }
          if (isChecked == 'false' && keysBusness.includes(item.value)) {
            this.selectdBussness.splice(keysBusness.indexOf(item.value), 1)
          }
        })
      } else {
        let _id = id
        if (isNaN(id)) {
          _id = id.split('-')[1]
        }
        if (!this.filterParent(_id).length) {
          return
        }
        const [result] = this.filterParent(_id)
        const preitem = this.getStringByObj(result, id)
        console.log('keysBusness12', preitem) // 当前点击数据父级下所有的数据
        const keysBusness = []
        this.selectdBussness.map((i) => {
          keysBusness.push(i.value)
        })
        if (isChecked == 'true' && !keysBusness.includes(id)) {
          this.selectdBussness.push(
            preitem[
              preitem.findIndex((item) => {
                return item.value === id
              })
            ]
          )
        }
        if (isChecked == 'false' && keysBusness.includes(id)) {
          this.selectdBussness.splice(keysBusness.indexOf(id), 1)
          console.log('keysBusness', keysBusness, this.selectdBussness)
        }
      }
      const selectdBussness = JSON.parse(JSON.stringify(this.selectdBussness))
      this.selectdBussness = selectdBussness
      this.$nextTick(() => {
        this.selectdBussness = selectdBussness
      })
    },
    filterParent(id) {
      // 根据子id 查询父级id
      let result = []
      this.fieldsTemplate.dataSource.forEach((item) => {
        item.children.forEach((ditem) => {
          if (ditem.id == id || ditem.itemCode == id) {
            result = [item]
          }
        })
      })
      return result
    },
    filterSiblings(id) {
      //查找 父级元素
      return this.fieldsTemplate.dataSource.filter((item) => item.id == id)
    },
    getStringByObj(item, id) {
      const arrNomarl = []
      item.children.map((i) => {
        let value = i.id
        if (isNaN(id)) {
          value = id
        }
        const json = { text: `${item.name}-${i.name}`, value }
        arrNomarl.push(json)
      })
      return arrNomarl
    },
    sceneChange(e) {
      this.formObject.sceneDefineId = e.itemData.id
      this.formObject.sceneDefineName = e.itemData.sceneName
      this.queryByOrgIdAndCategoryId()
    },
    // 评审类型改变
    bizTypeHandle({ itemData }) {
      if (!itemData) return
      if (itemData.itemCode == 1 || itemData.itemCode == 2) {
        this.reviewStatus = false
      }
      // 移除表单项的校验结果
      this.$refs.dialogRef.clearValidate()
      if (this.isFirst && this.editStatus) {
        // console.log("首次");
        //  清空已选择的条件
      } else {
        this.ruleListsReset()
      }
      this.isFirst = false
    },
    // 根据场景获取评审包
    async queryByOrgIdAndCategoryId() {
      const orgId = this.formObject.buyerOrgId
      const categoryId = this.formObject.categoryId
      const taskTemplateCode = this.formObject.bizType
      let packageCode = null
      // orgId: "1475802394828308482",
      // categoryId: "1475798895969271809",
      const OneResult = await this.$API.supplierReviewTask.queryByOrgIdAndCategoryIdPre({
        categoryId,
        orgId,
        taskTemplateCode
      })
      if (OneResult) {
        packageCode = OneResult.data.packageCode
      }
      this.$API.supplierReviewTask.queryByOrgIdAndCategoryId(packageCode).then((res) => {
        this.packageName = res.data.packageName
        this.reviewStatus = true
        this.formObject.ruleLists = []
        this.templateRuleLists = res.data.templateTypes
        this.ruleLists = res.data.reviewPackagePassRules.map((item) => {
          const itemData = {
            isChecked: 'true',
            hasChildren: false,
            id: item.templateType
          }
          this.nodeOnCheck({ itemData })
          this.formObject.ruleLists.push({
            conditionObject: item.conditionAttribute,
            symbol: item.conditionSymbol,
            standard: item.conditionTarget,
            joinSymbol: item.conditionType,
            templateTypeId: item.templateType
          })
          item.text = item.templateTypeName
          item.value = item.templateType
          return item
        })
      })
    },
    dialogItem(data, itemResponse) {
      this.$dialog({
        modal: () => import('../components/setItemDialog.vue'),
        data: {
          title: this.$t('维护指标评审人'),
          data: data,
          itemResponse
        },
        success: () => {
          this.$emit('confirm-function')
          // this.$refs.templateRef.refreshCurrentGridData();
        }
      })
    },
    submit() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          this.$loading()
          const param = $utils.cloneDeep(this.formObject)
          console.log('paramparamparamparam', param)
          delete param.templateList
          delete param.userLists
          this.$API.supplierReviewTask
            .taskReviewTaskSave(param)
            .then(() => {
              this.$hloading()
              this.$emit('confirm-function')
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    update() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          this.$loading()
          const param = $utils.cloneDeep(this.formObject)
          delete param.templateList
          delete param.userLists
          this.$API.supplierReviewTask
            .taskReviewTaskUpdate(param)
            .then(() => {
              this.$hloading()
              this.$emit('confirm-function')
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    ruleListsReset() {
      this.formObject.ruleLists = [
        {
          conditionObject: undefined,
          symbol: undefined,
          standard: undefined,
          joinSymbol: '0',
          templateTypeId: undefined
        }
      ]
      this.formObject.categoryId = ''
      this.formObject.categoryName = ''
      this.formObject.sceneDefineId = ''
      this.formObject.sceneDefineName = ''
      this.packageName = ''
      this.selectdBussness = []
      this.formObject.modelBuesness = null
    },
    templateChange({ itemData }, f) {
      if (!itemData) return
      f.templateTypeId = itemData.value
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .e-searcher {
  width: 100% !important;
}
.filters {
  display: flex;
  padding: 5px 0;
}
.iconSize {
  font-size: 30px;
  margin-left: 15px;
  cursor: pointer;
}
.form-box {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.mt-form-item {
  width: 48%;
}
.h2 {
  font-size: 14px;
  font-weight: bold;
}
::v-deep .mt-input-number input {
  width: 80px;
  padding-right: 0 !important;
}
</style>
