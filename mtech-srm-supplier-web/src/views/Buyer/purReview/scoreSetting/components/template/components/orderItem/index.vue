<template>
  <div class="oreder-warp">
    <div class="treeView">
      <ul class="left-nav">
        <li
          :class="{ active: index_ == index }"
          v-for="(item, index) in reviewContent"
          :key="item.id"
          @click="tabClick(index, item)"
        >
          {{ item.name }}
        </li>
      </ul>
    </div>
    <div class="table">
      <div v-show="!isShow">
        <template v-if="this.$route.query.flag !== '1'">
          <vxe-button
            v-if="
              info.supplyType === 1 &&
              info.bizType === 3 &&
              info.isPublish === 1 &&
              info.status === 114 &&
              (tabName.includes('QPA') || tabName.includes('QSA') || tabName.includes($t('环保')))
            "
            :disabled="isSetScorer"
            style="background-color: #f3f6fa; margin-top: 5px"
            @click="handleSelectScorer"
          >
            {{ $t('设置打分人') }}
          </vxe-button>
          <!-- <vxe-button
        v-if="info.supplyType === 1 && info.bizType === 3 && info.isPublish === 1"
        @click="submitScorer"
        >{{ $t('保存') }}</vxe-button> -->
          <vxe-button
            v-if="info.supplyType === 1 && info.bizType === 3 && info.isPublish === 1"
            @click="submitReleaseScorer"
            >{{ $t('发布') }}</vxe-button
          >
        </template>
        <ScTable
          ref="xTable"
          max-height="100%"
          :columns="columns"
          :tree-config="treeConfig"
          :table-data="tableData"
          :show-overflow="false"
          :row-config="{ height: 120 }"
          @checkbox-change="selectChangeEvent"
          @checkbox-all="checkboxChangeEvent"
        />
      </div>
      <div v-show="isShow">
        <template v-if="this.$route.query.flag !== '1'">
          <vxe-button
            v-if="
              info.supplyType === 1 &&
              info.bizType === 3 &&
              info.isPublish === 1 &&
              info.status === 114 &&
              (tabName.includes('QPA') || tabName.includes('QSA') || tabName.includes($t('环保')))
            "
            :disabled="isSetScorer"
            style="background-color: #f3f6fa; margin-top: 5px"
            @click="handleSelectScorer(false)"
          >
            {{ $t('设置打分人') }}
          </vxe-button>
          <vxe-button
            v-if="info.supplyType === 1 && info.bizType === 3 && info.isPublish === 1"
            @click="submitReleaseScorer"
          >
            {{ $t('发布') }}
          </vxe-button>
        </template>
        <mt-form ref="scoreRef" :model="scoreForm" style="margin-top: 20px">
          <mt-form-item v-if="!isSupplier" :label="$t('评分人')" prop="scoretor" label-style="left">
            <mt-input v-model="scoreForm.userName" :disabled="true" />
          </mt-form-item>
        </mt-form>
        <div style="display: flex">
          <div style="margin-right: 20px">
            <span>{{ $t('评审模板') }}：</span>
            <span style="color: #2783fe; cursor: pointer" @click="handleDownload">{{
              $t('附件下载')
            }}</span>
          </div>
          <div>
            <span v-if="!isSupplier">{{ $t('审查结果') }}：</span>
            <span v-if="isSupplier">{{ $t('供应商自查结果') }}：</span>
            <span style="color: #2783fe; cursor: pointer" @click="fileUpload">{{
              $t('附件下载')
            }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import ScTable from '@/components/ScTable/src/index'
import utils from '@/utils/utils'
import { i18n } from '@/main'

export default {
  components: {
    ScTable
  },
  props: {
    orderData: {
      type: Array,
      default: () => {
        return []
      }
    },
    info: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  computed: {
    isSupplier() {
      return this.info.bizType === 4
    }
  },

  data() {
    return {
      isSetScorer: true,
      index_: 0,
      tabName: '',
      reviewContent: [],
      treeConfig: {
        transform: true,
        rowField: 'itemCode',
        parentField: 'parentCode',
        expandAll: true
      },
      columns: [
        {
          type: 'checkbox',
          width: 50
        },
        { field: 'id', type: 'seq', width: 200, treeNode: true },
        {
          field: 'itemName',
          title: i18n.t('评审项'),
          width: 360
        },
        {
          field: 'range',
          title: i18n.t('分值范围'),
          width: 100
        },
        {
          field: 'weight',
          title: i18n.t('权重(%)'),
          width: 100
        },
        {
          field: 'selfScore',
          title: i18n.t('自查得分'),
          width: 100
        },
        {
          field: 'supportFile',
          title: i18n.t('支持文件'),
          width: 100
        },
        {
          field: 'isControlled',
          title: i18n.t('是否受控'),
          width: 100,
          formatter: (data) => {
            if (data.row.isControlled == 1) {
              return i18n.t('否')
            } else if (data.row.isControlled == 0) {
              return i18n.t('是')
            }
          }
        },
        {
          field: 'opinionDesc',
          title: i18n.t('描述'),
          width: 100
        },
        {
          field: 'scoringFileInfos',
          title: i18n.t('打分依据-自查'),
          width: 150,
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  {row.scoringFileInfos?.map((e) => {
                    return (
                      <div>
                        <p>
                          <a
                            onClick={() => {
                              this.preview(e)
                            }}>
                            {e.fileName}
                          </a>
                        </p>
                      </div>
                    )
                  })}
                </div>
              ]
            }
          }
        },
        // {
        //   field: 'scoringBasisFileList',
        //   title: i18n.t('打分依据-现场审查'),
        //   width: 200,
        //   slots: {
        //     default: ({ row }) => {
        //       return [
        //         <div>
        //           <input
        //             ref='file'
        //             type='file'
        //             style='display: none'
        //             onClick={() => {
        //               this.chooseRowsFiles(row)
        //             }}
        //             multiple='multiple'
        //             accept='.xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar,'
        //           />
        //           {row.scoringBasisFileList?.map((e) => {
        //             return (
        //               <div>
        //                 <p>
        //                   <a
        //                     onClick={() => {
        //                       this.preview(e)
        //                     }}>
        //                     {e.fileName}
        //                   </a>
        //                   <span
        //                     style='margin-left:10px;cursor: pointer;'
        //                     onClick={() => {
        //                       this.handleDownload(e)
        //                     }}>
        //                     下载
        //                   </span>
        //                 </p>
        //               </div>
        //             )
        //           })}
        //         </div>
        //       ]
        //     }
        //   }
        // },
        // {
        //   field: 'selfReason',
        //   title: i18n.t('自查原因'),
        //   width: 200
        // },
        // {
        //   field: 'unqualified', //symbol  score
        //   title: i18n.t('合格线'),
        //   width: 120
        // },
        {
          field: 'actScore',
          title: i18n.t('得分'),
          width: 100
        },
        {
          field: 'useSetStatus',
          title: i18n.t('是否适用'),
          width: 100,
          formatter: ({ row }) => {
            if (row.useSetStatus === 1) {
              return i18n.t('否')
            } else if (row.useSetStatus === 0) {
              return i18n.t('是')
            }
          }
        },
        {
          field: 'fileSupportSituation',
          title: i18n.t('文件支撑情况'),
          width: 100
        },
        {
          field: 'sampleInspectInfo',
          title: i18n.t('抽查情况'),
          width: 100
        },
        {
          field: 'executionMatchDegree',
          title: i18n.t('执行吻合度'),
          width: 100
        },
        {
          field: 'scoreUserName',
          title: i18n.t('打分人'),
          width: 160
        },
        {
          field: 'scoreTime',
          title: i18n.t('打分时间'),
          width: 180,
          slots: {
            default: ({ row }) => {
              return [
                <span>
                  {row.scoreTime
                    ? utils.formateTime(Number(row.scoreTime), 'yyyy-MM-dd hh:mm:ss')
                    : ''}
                </span>
              ]
            }
          }
        }
      ],
      tableData: [],
      fileInfos: [], // 存储上传文件
      selectedRow: [],
      returnScorerData: [],

      isShow: false,
      scoreForm: {
        userId: null,
        userName: null
      },
      qpaScoretor: {
        userId: null,
        userName: null
      },
      rdScoretor: {
        userId: null,
        userName: null
      },
      zrvnScoretor: {
        userId: null,
        userName: null
      }, // 越南非采打分人
      odminScoretor: {
        userId: null,
        userName: null
      },
      gfzlScoretor: {
        userId: null,
        userName: null
      },
      gfswScoretor: {
        userId: null,
        userName: null
      },
      gfjsScoretor: {
        userId: null,
        userName: null
      },
      gfzlyfScoretor: {
        userId: null,
        userName: null
      },
      templateId: null,
      txyfScoretor: {
        userId: null,
        userName: null
      },
      txswScoretor: {
        userId: null,
        userName: null
      },
      txzlScoretor: {
        userId: null,
        userName: null
      }
    }
  },
  created() {
    this.setScorer() // 设置打分人按钮权限
    if (this.orderData.length > 0) {
      this.orderData.forEach((item) => {
        let obj = {
          id: '',
          name: ''
        }
        obj.id = item.id
        obj.name = item.templateName
        this.reviewContent.push(obj)
      })
      this.tabName = this.reviewContent[0].name
      this.handleData()
    }
  },
  watch: {
    // 监听父组件传来的值  解决第一次不渲染的问题
    orderData: {
      handler() {
        this.handleData()
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.getScorers() // 获取打分人下拉
    this.getFileTypeScoreUserInfo()
  },

  methods: {
    getFileTypeScoreUserInfo() {
      let params = {
        taskCode: this.info.code
      }
      this.$API.supplierReviewTask.getFileTypeScoreUserInfo(params).then((res) => {
        if (res.code === 200) {
          res.data.forEach((item) => {
            if (item.templateType.includes('quality-qpa')) {
              this.qpaScoretor.userId = item.scoreUserId
              this.qpaScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('RD-rd1')) {
              this.rdScoretor.userId = item.scoreUserId
              this.rdScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('fc-zrvn')) {
              this.zrvnScoretor.userId = item.scoreUserId
              this.zrvnScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('odmin-zh')) {
              this.odminScoretor.userId = item.scoreUserId
              this.odminScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('gf-gfzl')) {
              this.gfzlScoretor.userId = item.scoreUserId
              this.gfzlScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('gf-gfsw')) {
              this.gfswScoretor.userId = item.scoreUserId
              this.gfswScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('gf-gfjs')) {
              this.gfjsScoretor.userId = item.scoreUserId
              this.gfjsScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('gf-gfzlyf')) {
              this.gfzlyfScoretor.userId = item.scoreUserId
              this.gfzlyfScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('tx-txyf')) {
              this.txyfScoretor.userId = item.scoreUserId
              this.txyfScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('tx-txsw')) {
              this.txswScoretor.userId = item.scoreUserId
              this.txswScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('tx-txzl')) {
              this.txzlScoretor.userId = item.scoreUserId
              this.txzlScoretor.userName = item.scoreUserName
            }
          })
        }
      })
    },
    // 设置打分人按钮权限
    setScorer() {
      const curUserID = JSON.parse(sessionStorage.getItem('userInfo')).uid
      if (
        this.info?.expertMembersResponses?.firstLevelResponse != null &&
        this.info?.expertMembersResponses?.firstLevelResponse.some(
          (item) => item.userId === curUserID && item.roleCode === 'member'
        )
      ) {
        this.isSetScorer = true
      } else if (
        this.info?.expertMembersResponses?.reviewResponse != null &&
        this.info?.expertMembersResponses?.reviewResponse.some(
          (item) => item.userId === curUserID && item.roleCode === 'member'
        )
      ) {
        this.isSetScorer = true
      } else if (
        this.info?.expertMembersResponses?.reviewResponse != null &&
        this.info?.expertMembersResponses?.reviewResponse.some(
          (item) => item.userId === curUserID && item.roleCode === 'leader'
        )
      ) {
        this.isSetScorer = false
      }
    },
    // 获取打分人下拉
    getScorers() {
      if (
        this.info.expertMembersResponses === undefined ||
        this.info.expertMembersResponses === null
      ) {
        return
      }

      if (this.info.expertMembersResponses.reviewResponse !== null) {
        // 只设置质量专家
        this.info.expertMembersResponses.reviewResponse.forEach((item) => {
          if (
            item.approveForm === 1 &&
            this.returnScorerData.every((tar) => tar.value != item.userId)
          ) {
            this.returnScorerData.push({
              text: item.userName + '-' + item.email,
              value: item.userId
            })
          }
        })
      }
    },
    // 保存
    submitScorer(flag = true) {
      let params = {}
      if (flag) {
        if (!this.selectedRow?.length) {
          this.$toast({
            content: this.$t('请勾选数据'),
            type: 'warning'
          })
          return
        }
        const updateScoreUserList = []
        this.selectedRow.forEach((item) => {
          if (item.children === null || item.children.length === 0) {
            updateScoreUserList.push({
              templateCode: item.templateCode,
              itemCode: item.itemCode,
              scoreUserId: this.scorer.userId,
              scoreUserName: this.scorer.userName.split('-')[0]
            })
          }
        })
        params = { taskCode: this.info.code, updateScoreUserList: updateScoreUserList }
      } else {
        params = {
          taskCode: this.info.code,
          updateScoreUserList: [
            {
              templateCode: this.orderData[this.index_].templateCode,
              scoreUserId: this.scoreForm.userId,
              scoreUserName: this.scoreForm.userName.split('-')[0]
            }
          ]
        }
      }

      this.$API.supplierReviewTask.submitScorer(params).then((res) => {
        if (res.code == 200 && res.data) {
          this.$toast({
            content: this.$t('设置成功'),
            type: 'success'
          })
          this.$parent.$parent.getDetailData()
        }
      })
    },
    // 发布
    submitReleaseScorer() {
      const templateLists = this.info.templateResponses.map((item) => {
        const templateItemRequests = []
        if (
          [
            'quality-qpa',
            'RD-rd1',
            'fc-zrvn',
            'odmin-zh',
            'gf-gfzl',
            'gf-gfsw',
            'gf-gfjs',
            'gf-gfzlyf',
            'tx-txyf',
            'tx-txsw',
            'tx-txzl'
          ].includes(item.templateTypeId)
        ) {
          if (item?.templateTypeId === 'quality-qpa') {
            templateItemRequests.push({
              scoreUserId: this.qpaScoretor?.userId,
              scoreUserName: this.qpaScoretor?.userName
            })
          }
          if (item?.templateTypeId === 'RD-rd1') {
            templateItemRequests.push({
              scoreUserId: this.rdScoretor?.userId,
              scoreUserName: this.rdScoretor?.userName
            })
          }
          if (item?.templateTypeId === 'fc-zrvn') {
            templateItemRequests.push({
              scoreUserId: this.zrvnScoretor?.userId,
              scoreUserName: this.zrvnScoretor?.userName
            })
          }
          if (item?.templateTypeId === 'odmin-zh') {
            templateItemRequests.push({
              scoreUserId: this.odminScoretor?.userId,
              scoreUserName: this.odminScoretor?.userName
            })
          }
          if (item?.templateTypeId === 'gf-gfzl') {
            templateItemRequests.push({
              scoreUserId: this.gfzlScoretor?.userId,
              scoreUserName: this.gfzlScoretor?.userName
            })
          }
          if (item?.templateTypeId === 'gf-gfsw') {
            templateItemRequests.push({
              scoreUserId: this.gfswScoretor?.userId,
              scoreUserName: this.gfswScoretor?.userName
            })
          }
          if (item?.templateTypeId === 'gf-gfjs') {
            templateItemRequests.push({
              scoreUserId: this.gfjsScoretor?.userId,
              scoreUserName: this.gfjsScoretor?.userName
            })
          }
          if (item?.templateTypeId === 'gf-gfzlyf') {
            templateItemRequests.push({
              scoreUserId: this.gfzlyfScoretor?.userId,
              scoreUserName: this.gfzlyfScoretor?.userName
            })
          }
          if (item?.templateTypeId === 'tx-txyf') {
            templateItemRequests.push({
              scoreUserId: this.txyfScoretor?.userId,
              scoreUserName: this.txyfScoretor?.userName
            })
          }
          if (item?.templateTypeId === 'tx-txsw') {
            templateItemRequests.push({
              scoreUserId: this.txswScoretor?.userId,
              scoreUserName: this.txswScoretor?.userName
            })
          }
          if (item?.templateTypeId === 'tx-txzl') {
            templateItemRequests.push({
              scoreUserId: this.txzlScoretor?.userId,
              scoreUserName: this.txzlScoretor?.userName
            })
          }
        } else {
          item.itemResponses?.forEach((tar) => {
            if (
              tar.itemResponses != undefined &&
              tar.itemResponses != null &&
              tar.itemResponses.length > 0
            ) {
              tar.itemResponses.forEach((tr) => {
                templateItemRequests.push({
                  itemCode: tr.itemCode,
                  scoreUserId: tr.scoreUserId,
                  scoreUserName: tr.scoreUserName
                })
              })
            } else if (
              tar.children != undefined &&
              tar.children != null &&
              tar.children.length > 0
            ) {
              tar.children.forEach((tr) => {
                templateItemRequests.push({
                  itemCode: tr.itemCode,
                  scoreUserId: tr.scoreUserId,
                  scoreUserName: tr.scoreUserName
                })
              })
            } else {
              templateItemRequests.push({
                itemCode: tar.itemCode,
                scoreUserId: tar.scoreUserId,
                scoreUserName: tar.scoreUserName
              })
            }
          })
        }
        return {
          templateCode: item.templateCode,
          templateTypeName: item.templateTypeName,
          templateTypeId: item.templateTypeId,
          templateItemRequests: templateItemRequests
        }
      })
      const publishTask = {
        code: this.info.code,
        templateLists: templateLists
      }
      this.$loading()
      this.$API.supplierReviewTask
        .submitReleaseScorer(publishTask)
        .then((res) => {
          if (res.code == 200 && res.data) {
            this.$hloading()
            this.$toast({
              content: this.$t('设置成功'),
              type: 'success'
            })
            this.$router.go(-1)
          }
        })
        .catch(() => {
          this.$hloading()
        })
    },
    // 查看附件
    handleDownload() {
      let fileList = this.orderData[this.index_]?.fileInfoResponseList || []
      this.$dialog({
        modal: () => import('./components/FileManage.vue'),
        data: {
          title: this.$t('附件下载'),
          fileList
        }
      })
    },
    fileUpload() {
      let fileList = []
      fileList =
        this.orderData[this.index_]?.expertResultFileInfoResponseList?.length !== 0
          ? this.orderData[this.index_]?.expertResultFileInfoResponseList || []
          : this.orderData[this.index_]?.selfResultFileInfoResponseList || []
      this.$dialog({
        modal: () => import('./components/FileManage.vue'),
        data: {
          title: this.$t('附件上传'),
          fileList
        }
      })
    },
    // 附件打开预览功能
    preview(item) {
      const params = {
        id: item.fileId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    // checkbox 行内选择
    selectChangeEvent(row) {
      this.selectedRow = row.records // 如果table表格是树形，并且选择的数据子数据只有一条的话 ，勾上的 就是爸爸和儿子，如果有多条子数据的话，勾选的儿子就只有自身一个
    },
    // 全选
    checkboxChangeEvent(row) {
      this.selectedRow = row.records
    },
    handleSelectScorer(flag = true) {
      if (flag) {
        if (!this.selectedRow?.length) {
          this.$toast({
            content: this.$t('请勾选数据'),
            type: 'warning'
          })
          return
        }
      }
      this.$dialog({
        modal: () => import('./coms/scorerSelect.vue'),
        data: {
          title: this.$t('选择评分人'),
          returnScorerData: this.returnScorerData
        },
        success: (res) => {
          if (flag) {
            this.scorer = res
          } else {
            this.scoreForm.userName = res.userName
            this.scoreForm.userId = res.userId
          }
          this.submitScorer(flag)
        }
      })
    },
    handleData() {
      let item = this.orderData[this.index_]
      this.isShow = [
        'quality-qpa',
        'RD-rd1',
        'fc-zrvn',
        'odmin-zh',
        'gf-gfzl',
        'gf-gfsw',
        'gf-gfjs',
        'gf-gfzlyf',
        'tx-txyf',
        'tx-txsw',
        'tx-txzl'
      ].includes(item.templateTypeId)
      this.templateId = this.isShow ? item.id : ''
      setTimeout(() => {
        if (item?.templateTypeId === 'quality-qpa') {
          this.scoreForm.userId = this.qpaScoretor?.userId || null
          this.scoreForm.userName = this.qpaScoretor?.userName || null
        }
        if (item?.templateTypeId === 'RD-rd1') {
          this.scoreForm.userId = this.rdScoretor?.userId || null
          this.scoreForm.userName = this.rdScoretor?.userName || null
        }
        if (item?.templateTypeId === 'fc-zrvn') {
          this.scoreForm.userId = this.zrvnScoretor?.userId || null
          this.scoreForm.userName = this.zrvnScoretor?.userName || null
        }
        if (item?.templateTypeId === 'odmin-zh') {
          this.scoreForm.userId = this.odminScoretor?.userId || null
          this.scoreForm.userName = this.odminScoretor?.userName || null
        }
        if (item?.templateTypeId === 'gf-gfzl') {
          this.scoreForm.userId = this.gfzlScoretor?.userId || null
          this.scoreForm.userName = this.gfzlScoretor?.userName || null
        }
        if (item?.templateTypeId === 'gf-gfsw') {
          this.scoreForm.userId = this.gfswScoretor?.userId || null
          this.scoreForm.userName = this.gfswScoretor?.userName || null
        }
        if (item?.templateTypeId === 'gf-gfjs') {
          this.scoreForm.userId = this.gfjsScoretor?.userId || null
          this.scoreForm.userName = this.gfjsScoretor?.userName || null
        }
        if (item?.templateTypeId === 'gf-gfzlyf') {
          this.scoreForm.userId = this.gfzlyfScoretor?.userId || null
          this.scoreForm.userName = this.gfzlyfScoretor?.userName || null
        }
        if (item?.templateTypeId === 'tx-txyf') {
          this.scoreForm.userId = this.txyfScoretor?.userId || null
          this.scoreForm.userName = this.txyfScoretor?.userName || null
        }
        if (item?.templateTypeId === 'tx-txsw') {
          this.scoreForm.userId = this.txswScoretor?.userId || null
          this.scoreForm.userName = this.txswScoretor?.userName || null
        }
        if (item?.templateTypeId === 'tx-txzl') {
          this.scoreForm.userId = this.txzlScoretor?.userId || null
          this.scoreForm.userName = this.txzlScoretor?.userName || null
        }
      }, 800)
      this.tableData = this.flatTableData(this.orderData[this.index_]?.itemResponses || [])
    },
    // 左侧tab切换
    tabClick(index, item) {
      this.index_ = index
      this.tabName = item.name
      let obj = this.orderData[this.index_]
      this.isShow = [
        'quality-qpa',
        'RD-rd1',
        'fc-zrvn',
        'odmin-zh',
        'gf-gfzl',
        'gf-gfsw',
        'gf-gfjs',
        'gf-gfzlyf',
        'tx-txyf',
        'tx-txsw',
        'tx-txzl'
      ].includes(obj?.templateTypeId)
      this.templateId = this.isShow ? item.id : ''
      if (obj?.templateTypeId === 'quality-qpa') {
        this.scoreForm.userId = this.qpaScoretor?.userId || null
        this.scoreForm.userName = this.qpaScoretor?.userName || null
      }
      if (obj?.templateTypeId === 'RD-rd1') {
        this.scoreForm.userId = this.rdScoretor?.userId || null
        this.scoreForm.userName = this.rdScoretor?.userName || null
      }
      if (obj?.templateTypeId === 'fc-zrvn') {
        this.scoreForm.userId = this.zrvnScoretor?.userId || null
        this.scoreForm.userName = this.zrvnScoretor?.userName || null
      }
      if (obj?.templateTypeId === 'odmin-zh') {
        this.scoreForm.userId = this.odminScoretor?.userId || null
        this.scoreForm.userName = this.odminScoretor?.userName || null
      }
      if (obj?.templateTypeId === 'gf-gfzl') {
        this.scoreForm.userId = this.gfzlScoretor?.userId || null
        this.scoreForm.userName = this.gfzlScoretor?.userName || null
      }
      if (obj?.templateTypeId === 'gf-gfsw') {
        this.scoreForm.userId = this.gfswScoretor?.userId || null
        this.scoreForm.userName = this.gfswScoretor?.userName || null
      }
      if (obj?.templateTypeId === 'gf-gfjs') {
        this.scoreForm.userId = this.gfjsScoretor?.userId || null
        this.scoreForm.userName = this.gfjsScoretor?.userName || null
      }
      if (obj?.templateTypeId === 'gf-gfzlyf') {
        this.scoreForm.userId = this.gfzlyfScoretor?.userId || null
        this.scoreForm.userName = this.gfzlyfScoretor?.userName || null
      }
      if (obj?.templateTypeId === 'tx-txyf') {
        this.scoreForm.userId = this.txyfScoretor?.userId || null
        this.scoreForm.userName = this.txyfScoretor?.userName || null
      }
      if (obj?.templateTypeId === 'tx-txsw') {
        this.scoreForm.userId = this.txswScoretor?.userId || null
        this.scoreForm.userName = this.txswScoretor?.userName || null
      }
      if (obj?.templateTypeId === 'tx-txzl') {
        this.scoreForm.userId = this.txzlScoretor?.userId || null
        this.scoreForm.userName = this.txzlScoretor?.userName || null
      }
      this.tableData = this.flatTableData(this.orderData[this.index_]?.itemResponses || [])
      // 清空所有的勾选数据
      this.$refs.xTable.clearCheckboxRow()
      this.selectedRow = []
    },
    flatTableData(list) {
      let arr = []
      list.forEach((item) => {
        arr = arr.concat(item.itemResponses || [])
      })
      const res = arr.concat(list)
      res.forEach((item) => {
        item.unqualified = item.symbol + ' ' + item.score
        item.range = item.scoreEnd ? `${item.scoreBegin || 0}-${item.scoreEnd}` : ''
      })
      return res
    }
  }
}
</script>

<style lang="scss" scoped>
.oreder-warp {
  display: flex;
  .treeView {
    width: 180px;
    margin-right: 20px;
  }
  .table {
    width: calc(100% - 180px);
    flex: 1;
  }
}
::v-deep .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  color: rgba(0, 0, 0, 0.87);
}
::v-deep .e-treeview .e-list-item.e-active > .e-fullrow {
  background-color: #fff;
  border-color: #fff;
  border-bottom: 2px solid #00469c;
}
::v-deep .mt-tree-view .e-treeview .e-fullrow {
  opacity: 1;
}
::v-deep .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  color: #00469c;
}

.left-nav {
  line-height: 40px;
  text-align: center;
  li {
    cursor: pointer;
  }
  .active {
    background: linear-gradient(135deg, rgba(245, 84, 72, 1) 0%, rgba(255, 128, 118, 1) 100%);
    color: #fff;
  }
}

.row-demension-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-decoration: underline;
}
</style>
