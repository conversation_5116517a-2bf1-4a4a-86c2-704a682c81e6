<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel" @open="open">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules">
        <mt-row>
          <mt-col :span="11">
            <mt-form-item prop="buyerOrgId" :label="$t('公司：')">
              <!-- <mt-select
                :disabled="editorIdstatus"
                v-model="formObject.buyerOrgId"
                :data-source="orgIdArr"
                :allow-filtering="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :show-clear-button="true"
                @select="selectCompany"
                :placeholder="$t('请选择公司')"
              ></mt-select> -->
              <RemoteAutocomplete
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                v-model="formInfo.buyerOrgCode"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                @change="selectCompany"
                :title-switch="false"
                :disabled="editorIdstatus"
                :width="414"
                :placeholder="$t('请选择')"
                select-type="administrativeCompany"
              ></RemoteAutocomplete>
            </mt-form-item>
          </mt-col>
          <mt-col :span="11" style="margin-left: 10px">
            <mt-form-item prop="orgIdArr" :label="$t('创建方式')">
              <mt-radio
                v-model="formObject.radioVal"
                :data-source="radioData"
                :disabled="isEdit"
                @input="onchang"
              ></mt-radio>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <!-- 手动板块控制 -->
        <div v-if="oneblake">
          <mt-row>
            <mt-col :span="11">
              <mt-form-item prop="supplierEnterpriseId" :label="$t('选择供应商：')">
                <mt-select
                  :disabled="editorIdstatus"
                  v-model="formObject.supplierEnterpriseId"
                  :data-source="planeArrList"
                  :allow-filtering="true"
                  :fields="{
                    text: 'supplierName',
                    value: 'supplierEnterpriseId'
                  }"
                  :show-clear-button="true"
                  @select="supplierselce"
                  :placeholder="$t('选择供应商')"
                  css-class="d72562-c40d-4933-9a24-98c3298365ac"
                  :item-template="iTemplate"
                  :filtering="filteringSupplier"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :span="11" style="margin-left: 10px">
              <mt-form-item prop="categoryId" :label="$t('选择供应商品类：')">
                <mt-select
                  :disabled="editorIdstatus"
                  v-model="formObject.categoryId"
                  :data-source="categoryList"
                  :fields="{ text: 'categoryName', value: 'categoryId' }"
                  :show-clear-button="true"
                  @select="selectPlan"
                  :placeholder="$t('选择供应商品类：')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-form-item prop="remark" :label="$t('评审原因')">
            <mt-input
              :multiline="true"
              :rows="3"
              type="text"
              :max-length="200"
              :placeholder="$t('请输入文本')"
              v-model="formObject.remark"
            ></mt-input>
          </mt-form-item>
        </div>
        <!-- 第二板块控制 -->
        <div v-if="twoblake">
          <mt-form-item prop="categorytree" :label="$t('请选择评审策略:')">
            <mt-DropDownTree
              @input="selectCategoryas"
              v-model="formObject.categorytree"
              v-if="fields.dataSource.length"
              :fields="fields"
              :show-check-box="true"
              id="checkboxTreeSelect"
              :placeholder="$t('请选择评审策略')"
              :allow-multi-selection="true"
              :auto-check="true"
              :key="fields.key"
            ></mt-DropDownTree>
            <mt-select v-else :placeholder="$t('请选择评审策略')" :data-source="[]"></mt-select>
          </mt-form-item>
          <mt-row>
            <mt-col :span="24">
              <onetable ref="rules" style="width: 100%"></onetable>
            </mt-col>
          </mt-row>
        </div>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import MtRadio from '@mtech-ui/radio'
import onetable from './onetable.vue'
import itemVue from './itemVue'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    MtRadio,
    onetable,
    itemVue,
    RemoteAutocomplete
  },
  data() {
    return {
      iTemplate: function () {
        return {
          template: itemVue
        }
      },
      editorIdstatus: false, //全局禁用 查看专用
      oneblake: true, //控制供应商+品类显示
      twoblake: true, //控制评审策略+表格展示
      // 创建方式选择事件
      radioData: [
        {
          label: this.$t('手动选择'),
          value: '1'
        },
        {
          label: this.$t('评审策略'),
          value: '2'
        }
      ],
      // 创建方式选择事件
      oneradioData: [
        {
          label: this.$t('手动选择'),
          value: '1'
        }
      ],
      // 表单验证
      rules: {
        categorytree: [
          {
            required: true,
            message: this.$t('请选择评审策略'),
            trigger: 'blur'
          }
        ],
        buyerOrgId: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ],
        supplierEnterpriseId: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        categoryId: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        remark: [
          {
            required: true,
            message: this.$t('请输入评审原因'),
            trigger: 'blur'
          }
        ]
      },
      fields: {
        dataSource: [], //评审下拉数组
        value: 'id',
        text: 'strategyName',
        child: 'childrens',
        key: 1
      },
      formInfo: {
        orgId: '', //公司id
        orgCode: '',
        companyId: '', //计划id 取计划模板
        planId: '', //供应商id 取品类
        categorytree: [] //品类树下拉数组
      },
      // 弹窗底部按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      // 新增表单内容
      formObject: {
        partnerRelationId: '', //客户关系id
        // supplierId: "", //供应商id
        supplierCode: '', //供应商code
        supplierName: '', //供应商名字
        partnerArchiveId: '', //客户档案id
        supplierEnterpriseId: '', //供应商企业id
        supplierEnterpriseCode: '', //供应商企业code
        supplierEnterpriseName: '', //供应商企业code
        categoryCode: '', //品类code
        categoryName: '', //品类名称
        supplierProvinceCode: '', //供应商所属省code
        supplierProvinceName: '', //供应商所属省名称
        supplierCityCode: '', //供应商所属市code
        supplierCityName: '', //供应商所属市名称
        buyerEnterpriseId: '', //公司id
        buyerEnterpriseCode: '', //公司code
        buyerEnterpriseName: '', //公司名字
        buyerOrgId: '', //公司id
        buyerOrgCode: '', //公司code
        buyerOrgName: '', //公司名字
        remark: '', //原因或规则描述
        radioVal: '1', //创建方式:1手动选择 2评审策略
        buyerReviewStrategys: []
      },
      editStatus: false,
      planeArrList: [], //供应商下拉数组
      // orgIdArr: [], //公司数组
      categoryList: [] //品类下拉数组
    }
  },

  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  async created() {
    this.oneblake = true
    this.twoblake = false
    // 初始化获取公司列表
    // this.TreeByAccount();
    // 编辑
    if (this.modalData.isEdit) {
      console.log('buyerOrgCodebuyerOrgCode', this.modalData.policyData)
      // this.radioData = this.oneradioData;
      this.editStatus = true
      let emitData = this.modalData.policyData //行内传值数据
      this.formObject = emitData
      // this.formObject.radioVal = "0";
      this.formObject.radioVal = emitData.createType.toString()
      this.formInfo.orgId = emitData.buyerOrgId //获取供应商id
      this.formInfo.buyerOrgCode = emitData.buyerOrgCode //获取供应商id
      this.formInfo.planId = emitData.partnerRelationId // 获取计划品类列表id
      this.queryPlanByCompany(() => {
        this.TreeByPlanIdangeCat(() => {}) // 获取计划品类列表
      }) //获取供应商
      // 先写死
      this.editorIdstatus = true
    }
  },
  methods: {
    filteringSupplier(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          this.planeArrList.filter((item) => {
            if (
              item?.supplierName.indexOf(e.text) > -1 ||
              item?.supplierInternalCode.indexOf(e.text) > -1 ||
              item?.supplierCode.indexOf(e.text) > -1
            ) {
              return true
            } else {
              return false
            }
          })
        )
      } else {
        e.updateData(this.planeArrList)
      }
    },
    // 获取供应商品类下拉列表
    TreeByPlanIdangeCat(callback) {
      this.$API.reviewPlan['getCategoryPartnerRelations']({
        partnerRelationId: this.formInfo.planId
      })
        .then((result) => {
          this.categoryList = result.data
          if (typeof callback === 'function') {
            callback()
          }
        })
        .catch(() => {
          callback()
        })
    },
    // 获取评审策略下拉列表
    queryAlltions() {
      this.$API.reviewPlan['queryAlltions']({
        buyerOrgId: this.formInfo.orgId
      }).then((result) => {
        this.$set(this.fields, 'dataSource', [...result.data])
        this.$set(this.fields, 'key', this.randomString())
      })
    },
    // 评审数组递归去重
    fcin(data, e, arr) {
      data.forEach((item) => {
        if (e.includes(item.id)) {
          arr.push({
            id: item.id,
            ruleDesc: item.ruleDesc,
            buyerOrgId: item.buyerOrgId,
            categoryIdss: item.categoryId
          })
        }
        if (Array.isArray(item.childrens)) {
          this.fcin(item.childrens, e, arr)
        }
      })
    },
    // 评审策略多选下拉
    selectCategoryas(e) {
      this.formObject.buyerReviewStrategys = []
      this.fcin(this.fields.dataSource, e, this.formObject.buyerReviewStrategys)
      //调用子组件列表刷新
      this.$refs.rules.buyerReview(this.formObject.buyerReviewStrategys)
      this.$refs.dialogRef.validateField('categorytree')
    },
    // 创建方式选择事件
    onchang() {
      if (this.formObject.radioVal == '2') {
        this.oneblake = false
        this.twoblake = true
      } else {
        this.oneblake = true
        this.twoblake = false
      }
    },
    // 随机数创造
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    // 选择公司
    selectCompany(e) {
      if (this.formObject.radioVal == '2') {
        this.selectCategoryas('')
        if (this.formObject.categorytree) {
          this.formObject.categorytree = []
        }
      }
      if (e.itemData?.id) {
        let { itemData } = e
        this.formInfo.orgId = itemData.id
        this.formObject.buyerOrgCode = itemData.orgCode
        this.formObject.buyerOrgId = itemData.id
        this.formObject.buyerOrgName = itemData.orgName
        this.queryPlanByCompany() //调用供应商列表
        this.queryAlltions() // 获取评审策略下拉列表
        this.planeArrList = [] // 获取供应商zhikong
        this.categoryList = [] //pinlei清空
      } else {
        this.formInfo.orgId = ''
        this.formObject.buyerOrgCode = ''
        this.formObject.buyerOrgId = ''
        this.formObject.buyerOrgName = ''
        this.$set(this.fields, 'dataSource', []) // 清空评审策略下拉列表
        this.$set(this.fields, 'key', this.randomString())
        this.planeArrList = [] // 获取供应商zhikong
        this.categoryList = [] //pinlei清空
      }
    },

    // 选择品类点击事件
    selectPlan(e) {
      let { itemData } = e
      this.formObject.categoryCode = itemData.categoryCode
      this.formObject.categoryName = itemData.categoryName
    },
    // 选择供应商点击事件
    supplierselce(e) {
      let { itemData } = e
      this.formInfo.planId = itemData.id //获取品类传值id

      // this.formObject.supplierId = itemData.id;
      this.formObject.partnerRelationId = itemData.id
      this.formObject.supplierCode = itemData.supplierCode
      this.formObject.supplierName = itemData.supplierName
      this.formObject.partnerArchiveId = itemData.partnerArchiveId
      this.formObject.supplierEnterpriseId = itemData.supplierEnterpriseId
      this.formObject.supplierEnterpriseCode = itemData.supplierEnterpriseCode
      this.formObject.supplierEnterpriseName = itemData.supplierEnterpriseName
      this.TreeByPlanIdangeCat() // 获取计划品类列表
      this.categoryList = []
    },

    // 获取供应商
    queryPlanByCompany(callback) {
      this.$API.reviewPlan['getOrgPartnerRelations']({
        orgId: this.formInfo.orgId // 传值级联公司id
      })
        .then((result) => {
          this.planeArrList = result.data
          if (typeof callback === 'function') {
            callback()
          }
        })
        .catch(() => {
          callback()
        })
    },
    // 初始化获取公司列表
    // TreeByAccount() {
    //   this.$API.reviewPlan["findSpecif"]({
    //     organizationLevelCodes: ["ORG02", "ORG01"],
    //     orgType: "ORG001PRO",
    //     includeItself: true,
    //   }).then((res) => {
    //     // this.$set(this.fieldsarr, "dataSource", [...res.data]);
    //     this.orgIdArr = res.data;
    //   });
    // },
    // 确认按钮
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let addBuyerAssessPlanCategoryRangeRequest = JSON.parse(JSON.stringify(this.formObject))
          // delete addBuyerAssessPlanCategoryRangeRequest.orgIdArr; // 公司  删除多传值部分
          let updateBuyerAssessPlanCategoryRangeRequest = JSON.parse(
            JSON.stringify(this.formObject) //编辑得
          )
          if (this.editStatus) {
            // console.log("维度设置--编辑");
            this.$API.reviewPlan
              .updateStatus(updateBuyerAssessPlanCategoryRangeRequest)
              .then((res) => {
                if (res.code == 200) {
                  this.$emit('confirm-function') //关闭弹窗
                  this.$refs.templateRef.refreshCurrentGridData() //刷新表格统一方法
                }
              })
          } else {
            if (this.formObject.radioVal == '2') {
              //   "维度设置--新增",
              this.$API.reviewPlan
                .addByStrategy(addBuyerAssessPlanCategoryRangeRequest)
                .then((res) => {
                  if (res.code == 200) {
                    this.$emit('confirm-function') //关闭弹窗
                    this.$refs.templateRef.refreshCurrentGridData() //刷新表格统一方法
                  }
                })
            } else {
              //   "维度设置--新增",
              this.$API.reviewPlan
                .suggestAdd(addBuyerAssessPlanCategoryRangeRequest)
                .then((res) => {
                  if (res.code == 200) {
                    this.$emit('confirm-function') //关闭弹窗
                    this.$refs.templateRef.refreshCurrentGridData() //刷新表格统一方法
                  }
                })
            }
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    open(args) {
      // 在对话框打开时触发，用来阻止默认获取第一个元素的焦点
      args.preventFocus = true
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
// ::v-deep .e-radio-wrapper .e-wrapper {
//   margin-left: 60px;
// }
</style>
<style lang="scss">
.d72562-c40d-4933-9a24-98c3298365ac {
  li {
    display: flex;
    align-items: center;
    & > div {
      flex-shrink: 0;
    }
    & > div:first-child {
      margin-left: 20px;
    }
  }
}
</style>
