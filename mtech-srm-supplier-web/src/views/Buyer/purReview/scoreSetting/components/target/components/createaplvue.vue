<template>
  <mt-dialog ref="dialog" width="1200" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules">
        <mt-row>
          <mt-col :span="12">
            <mt-form-item prop="planName" :label="$t('单据名称')">
              <mt-input
                v-model="formObject.planName"
                :show-clear-button="true"
                :multiline="false"
                :placeholder="$t('请输入单据名称')"
                :max-length="50"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="11" style="margin-left: 20px">
            <mt-form-item prop="buyerOrgId" :label="$t('公司：')">
              <!-- <mt-select
                v-model="formObject.buyerOrgId"
                :data-source="orgIdArr"
                :allow-filtering="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :show-clear-button="true"
                @select="selectCompany"
                :placeholder="$t('请选择公司')"
              ></mt-select> -->
              <MasterdataDropdownSelects
                :remote-search="true"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                v-model="formObject.buyerOrgId"
                :fields="{ text: 'title', value: 'id' }"
                @change="selectCompany"
                :disabled="editorIdstatus"
                :title-switch="false"
                :placeholder="$t('请选择公司')"
                select-type="administrativeCompany"
              ></MasterdataDropdownSelects>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-form-item prop="remark" :label="$t('备注')">
          <mt-input
            :multiline="true"
            :rows="3"
            type="text"
            :placeholder="$t('请输入文本')"
            v-model="formObject.remark"
          ></mt-input>
        </mt-form-item>
        <!-- 新增控制展示 -->
        <mt-row>
          <mt-col :span="24">
            <twotable ref="rules" :seldata="seldata" style="width: 100%; height: 600px"></twotable>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import twotable from './twotable.vue'
export default {
  components: {
    twotable
  },
  data() {
    return {
      // 表单验证
      rules: {
        planName: [
          {
            required: true,
            message: this.$t('请填写单据名称'),
            trigger: 'blur'
          }
        ],
        buyerOrgId: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ],
        remark: [
          {
            required: true,
            message: this.$t('请输入评审原因'),
            trigger: 'blur'
          }
        ]
      },
      fields: {
        dataSource: [], //评审下拉数组
        value: 'categoryCode',
        text: 'categoryName',
        child: 'childrens',
        key: 1
      },

      formInfo: {
        orgId: '', //公司id
        companyId: '' //计划id 取计划模板
      },
      // 弹窗底部按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.submit,
          buttonModel: { content: this.$t('保存') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并申请') }
        }
      ],
      // 新增表单内容
      formObject: {
        reviewPlanItemList: [
          {
            supplierId: '', //供应商id
            supplierCode: '', //供应商code
            supplierName: '', //供应商名字
            planReviewTime: '', //计划评审时间
            suggestCode: '', //建议评审code 1
            supplierEnterpriseId: '', //供应商企业id
            supplierEnterpriseCode: '', //供应商企业code
            supplierEnterpriseName: '', //供应商企业名字
            categoryId: '', //品类id
            categoryCode: '', //品类code
            categoryName: '', //品类名称
            supplierProvinceCode: '', //供应商所属省code
            supplierProvinceName: '', //供应商所属省code
            supplierCityCode: '', //供应商所属市code
            supplierCityName: '', //供应商所属市名称
            remark: '' //原因
          }
        ],
        buyerOrgId: '', //采方组织id
        buyerOrgCode: '', //采方组织code
        buyerOrgName: '', //采方组织名字
        planName: '', //评审计划名称
        planStatus: null, //状态
        // buyerEnterpriseId: "", //采方企业id
        // buyerEnterpriseCode: "", //采方企业code
        // buyerEnterpriseName: "", //采访企业名字
        partnerRelationId: '', //客户关系id
        partnerArchiveId: '' //客户档案id
      },
      editStatus: false,
      planeArrList: [], //计划下拉数组
      categoryList: [], //品类下拉数组
      // orgIdArr: [], //公司数组
      seldata: [],
      trudetaiid: ''
    }
  },

  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    }
    // seldata() {
    //   // 这里是选中全部
    //   // return this.modalData.seldata;
    //   // 下面只保留有id
    //   // let arr = this.modalData.seldata.map((item) => {
    //   //   return item.id;
    //   // });
    //   // return arr || [];
    // },
  },

  mounted() {
    // 编辑
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData.isEdit) {
      this.TreyeCat()
    }
  },
  async created() {
    // 初始化获取公司列表
    // this.TreeByAccount();
  },
  watch: {
    'modalData.seldata': {
      handler(n) {
        this.seldata = JSON.parse(JSON.stringify(n))
        this.seldata.forEach((item) => {
          item.planReviewTime = this.timestampToTime(item.planReviewTime)
        })
        this.trudetaiid = this.seldata[0].id
        this.seldata.splice(0, 1)
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 获取详情
    TreyeCat() {
      this.$API.reviewPlan['strudetaill']({
        id: this.trudetaiid
      }).then((result) => {
        this.formObject = result.data
        this.formObject.reviewPlanItemList.forEach((item) => {
          item.planReviewTime = this.timestampToTime(item.planReviewTime)
        })
        this.seldata = this.formObject.reviewPlanItemList
      })
    },
    // 时间戳转换
    timestampToTime(timestamp) {
      if (timestamp === 0 || timestamp == null) {
        return ''
      } else {
        var date = new Date(Number(timestamp))
        var Y = date.getFullYear() + '-'
        var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
        var D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
        return Y + M + D
      }
    },
    // 选择公司
    selectCompany(e) {
      if (e.itemData) {
        let { itemData } = e
        this.formObject.buyerOrgCode = itemData.orgCode
        this.formObject.buyerOrgId = itemData.id
        this.formObject.buyerOrgName = itemData.name
      }
    },

    // 初始化获取公司列表
    // TreeByAccount() {
    //   this.$API.reviewPlan["findSpecif"]({
    //     organizationLevelCodes: ["ORG02", "ORG01"],
    //     orgType: "ORG001PRO",
    //     includeItself: true,
    //   }).then((res) => {
    //     this.orgIdArr = res.data;
    //   });
    // },
    // 保存按钮
    submit() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          this.seldata.forEach((item) => {
            item.planReviewTime = Date.parse(item.planReviewTime)
          })
          this.formObject.reviewPlanItemList = this.seldata
          this.formObject.planStatus = 10
          let addBuyerAsdata = JSON.parse(JSON.stringify(this.formObject))

          this.$API.reviewPlan.strupdatel(addBuyerAsdata).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function') //关闭弹窗
              this.$refs.templateRef.refreshCurrentGridData() //刷新表格统一方法
            }
          })
        }
      })
    },
    // 确认按钮
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          this.seldata.forEach((item) => {
            item.planReviewTime = Date.parse(item.planReviewTime)
          })
          this.formObject.reviewPlanItemList = this.seldata
          this.formObject.planStatus = 20
          let addBuyerAsdata = JSON.parse(JSON.stringify(this.formObject))
          this.$API.reviewPlan.strupdatel(addBuyerAsdata).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function') //关闭弹窗
              this.$refs.templateRef.refreshCurrentGridData() //刷新表格统一方法
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
::v-deep .e-content {
  height: 100%;
}
</style>
