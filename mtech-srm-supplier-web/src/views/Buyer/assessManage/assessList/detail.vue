<template>
  <div class="full-height">
    <div class="detail-top">
      <div class="detail-card">
        <div class="desc">
          <div class="desc-title-box">
            <span class="title">
              <div class="detail-item">
                <span>{{ $t('考核单编号：') }}</span>
                <span>{{ formObject.claimCode }}</span>
              </div>
            </span>
          </div>
          <div class="desc-detail-box mb_7">
            <div class="detail-item">
              <span>{{ $t('状态：') }}</span>
              <span>{{ statusLabel }}</span>
            </div>
            <div class="detail-item">
              <span>{{ $t('考核维度：') }}</span>
              <span>{{ formObject.claimTypeName }}</span>
            </div>
            <div class="detail-item">
              <span style="display: inline-block">{{ $t('考核月份：') }}</span>
              <span class="reason">{{ formObject.claimMonth }}</span>
            </div>
            <div class="detail-item">
              <span>{{ $t('所属公司：') }}</span>
              <span>{{ formObject.companyName }}</span>
            </div>
            <div class="detail-item">
              <span>{{ $t('供应商名称：') }}</span>
              <span>{{ formObject.supplierName }}</span>
            </div>
          </div>
          <div class="desc-detail-box">
            <div class="detail-item">
              <span>{{ $t('考核品类：') }}</span>
              <span>{{ formObject.itemName }}</span>
            </div>
            <div class="detail-item">
              <span>{{ $t('协议书模板：') }}</span>
              <span>{{ formObject.agreementName }}</span>
            </div>
            <div class="detail-item">
              <span>{{ $t('创建人：') }}</span>
              <span>{{ formObject.createUserName }}</span>
            </div>
            <div class="detail-item">
              <span>{{ $t('创建时间：') }}</span>
              <span>{{ createTime }}</span>
            </div>
            <div class="detail-item link" @click="showDetail" v-if="!!formObject.approvalUrl">
              <span>{{ $t('查看审批记录') }}</span>
            </div>
          </div>
        </div>
        <div class="button-group">
          <span type="primary" @click="cancel">{{ $t('返回') }}</span>
          <span type="info" v-if="queryId" @click="print">{{ $t('协议打印') }}</span>
          <span type="info" v-if="[0, 3, 7].includes(queryType)" @click="save">{{
            $t('保存')
          }}</span>
          <span type="info" v-if="[0, 3, 7].includes(queryType)" @click="saveAndCommit">{{
            $t('保存并提交')
          }}</span>
          <span
            type="info"
            v-if="[12].includes(queryType) && showFileUploadBtn"
            @click="handleUpload"
            >{{ $t('提交附件') }}</span
          >
          <span type="info" v-if="queryType == 17" @click="saveInvoice">{{ $t('保存发票') }}</span>
          <span type="info" v-if="queryType == 10" @click="handleClick">{{ $t('强制扣款') }}</span>
        </div>
      </div>
    </div>
    <mt-tabs
      :tab-id="tabId"
      :e-tab="false"
      :data-source="pageConfig"
      @handleSelectTab="handleSelectTab"
      v-if="!isPerPublish"
    ></mt-tabs>
    <claim-detail
      ref="claimDetail"
      v-if="isAssessment"
      v-show="currentTabIndex === 0"
      :info="formObject"
      @setBtn="setFileUploadBtn"
    ></claim-detail>
    <supplier-feedback
      ref="supplierFeedback"
      v-if="!isPerPublish"
      v-show="currentTabIndex === 1"
      :info="formObject"
    ></supplier-feedback>
    <appeal-history
      ref="appealHistory"
      v-if="!isPerPublish"
      v-show="currentTabIndex === 2"
      :info="formObject"
    ></appeal-history>
  </div>
</template>
<script>
import utils from '@/utils/utils'
import { isEqual } from 'lodash'
import { cloneDeep } from 'lodash'
import claimDetail from './pages/claimDetail.vue'
import supplierFeedback from './pages/supplierFeedback.vue'
import appealHistory from './pages/appealHistory.vue'
import util from '@/utils/utils'

export default {
  components: {
    claimDetail,
    supplierFeedback,
    appealHistory
  },
  data() {
    return {
      uploadUser: null,
      statusList: [
        { code: -1, label: this.$t('已取消') },
        { code: -2, label: this.$t('已作废') },
        { code: 0, label: this.$t('新建') },
        { code: 1, label: this.$t('已提交') },
        { code: 3, label: this.$t('审批拒绝') },
        { code: 5, label: this.$t('索赔协议盖章审批中') },
        { code: 7, label: this.$t('索赔协议盖章审批拒绝') },
        { code: 10, label: this.$t('待反馈') },
        { code: 11, label: this.$t('已反馈') },
        { code: 12, label: this.$t('已确认') },
        { code: 13, label: this.$t('申诉处理审批中') },
        { code: 14, label: this.$t('重新改判') },
        { code: 15, label: this.$t('已改判') },
        { code: 16, label: this.$t('不改判') },
        { code: 17, label: this.$t('已付款') },
        { code: 19, label: this.$t('申诉驳回') }
      ],
      isPerPublish: false,
      isAssessment: false,
      isShow: false,
      tabId: 'assessListDetailTabs',
      formObject: {
        projectCode: null
      },
      currentTabIndex: 0,
      pageConfig: [
        { title: this.$t('单据详情') },
        { title: this.$t('供方反馈') },
        { title: this.$t('申诉历史查看') }
      ],

      showFileUploadBtn: false
    }
  },

  computed: {
    queryId() {
      return this.$route.query.id
    },
    queryType() {
      return this.$route.query.type
    },
    queryCode() {
      return this.$route.query.code ? this.$route.query.code : this.$route.query.form_code
    },
    createTime() {
      return this.formatTime(this.formObject.createTime)
    },
    statusLabel() {
      return this.statusList.find((e) => e.code == this.formObject.status).label
    }
  },
  mounted() {
    if (this.queryCode && !this.queryId) {
      this.$API.assessManage.detailClaimByCode({ code: this.queryCode }).then((res) => {
        this.$router.push({
          // name: 'purchase-assessmanage-assessListDetail',
          query: {
            type: res.data.status,
            id: res.data.id
          }
        })
        if ([0, 1, 3, 7].includes(this.queryType)) {
          this.isPerPublish = true
        }
        if (this.queryType == 17) {
          this.getUserInfoDetail()
        }
        this.getDetail()
      })
    } else {
      if ([0, 1, 3, 7].includes(this.queryType)) {
        this.isPerPublish = true
      }
      if (this.queryType == 17) {
        this.getUserInfoDetail()
      }
      this.getDetail()
    }
  },
  methods: {
    handleClick() {
      let _attachmentList =
        this.$refs.claimDetail.$refs.attachmentRef.getCurrentUsefulRef().ejsRef.ej2Instances
          .currentViewData

      if (_attachmentList && _attachmentList.length === 0) {
        this.$toast({
          content: this.$t('采方附件不能为空'),
          type: 'warning'
        })
        return
      }
      let params = {
        id: this.queryId,
        attachmentList: _attachmentList
      }
      this.$API.assessManage.purchaseConfirmClaimApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.cancel()
        }
      })
    },
    getUserInfoDetail() {
      this.$API.supplierAcc.queryUserInfo().then((res) => {
        this.uploadUser = res.data
      })
    },
    saveInvoice() {
      console.log('saveInvoice-', this.$refs.claimDetail)
      const _fileList = this.$refs.claimDetail.invoiceList
      let _trueList = []
      if (_fileList) {
        _fileList.forEach((e) => {
          _trueList.push({
            attachmentId: e.id,
            attachmentName: e.fileName,
            attachmentSize: e.fileSize,
            attachmentUrl: e.url,
            uploadTime: e?.uploadTime || Number(new Date(e.createTime)),
            uploadUserId: e?.uploadUserId || this.uploadUser.uid,
            uploadUserName: e?.uploadUserName || this.uploadUser.username
          })
        })
      }
      this.$API.assessManage
        .saveClaimInvoice({
          claimId: this.queryId,
          attachmentList: _trueList
        })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
          }
        })
    },
    showDetail() {
      window.open(this.formObject.approvalUrl)
    },
    formatTime(e) {
      if (e) {
        if (e == 0) {
          return (e = '')
        } else if (typeof e == 'object') {
          return utils.formateTime(e)
        } else if (typeof e == 'string') {
          if (e.indexOf('T') != -1) {
            return e.substr(0, 10)
          } else {
            let val = parseInt(e)
            return utils.formateTime(new Date(val))
          }
        } else if (typeof e == 'number') {
          return utils.formateTime(new Date(e))
        } else {
          return e
        }
      } else {
        return e
      }
    },
    handleSelectTab(e) {
      this.currentTabIndex = e
    },
    getDetail() {
      if ([0, 1, 3, 7].includes(this.queryType)) {
        this.$API.assessManage
          .detailClaim({ id: this.queryId })
          .then((res) => {
            if (res.code === 200) {
              const _data = cloneDeep(res.data)
              sessionStorage.setItem('claimTypeCode', _data.claimTypeCode)
              sessionStorage.setItem('claimCompanyCode', _data.companyCode)
              this.formObject = { ..._data }
              this.isAssessment = true
              // this.$refs.claimDetail.$refs.assessIndexTemplateRef.refreshCurrentGridData()
            }
          })
          .catch(() => {
            this.isAssessment = true
          })
      } else {
        this.$API.assessManage
          .detailPublishedClaim({ id: this.queryId })
          .then((res) => {
            if (res.code === 200) {
              const _data = cloneDeep(res.data)
              this.formObject = { ..._data }
              this.isAssessment = true
              if (_data.status == 12 && _data.offlineEnsure) {
                this.isPerPublish = true
              }
              if (!_data.claimAppealDealHistory && !_data.claimAppeal) {
                this.pageConfig = [{ title: this.$t('单据详情') }]
              } else if (!_data.claimAppealDealHistory) {
                this.pageConfig = [{ title: this.$t('单据详情') }, { title: this.$t('供方反馈') }]
              } else if (!_data.claimAppeal) {
                this.pageConfig = [
                  { title: this.$t('单据详情') },
                  { title: this.$t('申诉历史查看') }
                ]
              }
              sessionStorage.setItem('claimTypeCode', _data.claimTypeCode)
              sessionStorage.setItem('claimCompanyCode', _data.companyCode)
              // this.$refs.claimDetail.$refs.assessIndexTemplateRef.refreshCurrentGridData()
            }
          })
          .catch(() => {
            this.isAssessment = true
          })
      }
    },
    cancel() {
      this.$router.push({
        name: 'purchase-assessmanage-assessList'
      })
    },
    async save() {
      // this.$refs.claimDetail.$refs.costCenterTemplateRef
      //   .getCurrentUsefulRef()
      //   .gridRef.ejsRef.endEdit();
      // this.$refs.claimDetail.$refs.assessIndexTemplateRef
      //   .getCurrentUsefulRef()
      //   .gridRef.ejsRef.endEdit()
      // setTimeout(() => {
      let _costCenterList =
        this.$refs.claimDetail.$refs?.costCenterTemplateRef?.getCurrentUsefulRef().ejsRef
          .ej2Instances.currentViewData
      let _standDetailList =
        this.$refs.claimDetail.$refs.assessIndexTemplateRef.getCurrentUsefulRef().ejsRef
          .ej2Instances.currentViewData
      // let _standDetailList = this.$refs.claimDetail.newArr.filter(function (item) {
      //   return Object.keys(item).length > 0
      // })
      // _standDetailList = _standDetailList || this.formObject.standDetailList

      let _attachmentList =
        this.$refs.claimDetail.$refs.attachmentRef.getCurrentUsefulRef().ejsRef.ej2Instances
          .currentViewData

      if (_costCenterList && _costCenterList.length === 0) {
        this.$toast({
          content: this.$t('成本中心不能为空'),
          type: 'warning'
        })
        return
      }
      console.log(
        '_standDetailList_standDetailList_standDetailList',
        this.$refs.claimDetail.$refs.assessIndexTemplateRef
      )
      if (_standDetailList.some((e) => !e.happenTime)) {
        this.$toast({
          content: this.$t('考核指标详情明细行发生时间不能为空'),
          type: 'warning'
        })
        return
      }
      if (_standDetailList.some((e) => !e.claimDesc)) {
        this.$toast({
          content: this.$t('考核指标详情明细行考核说明不能为空'),
          type: 'warning'
        })
        return
      }
      if (!this.$refs.claimDetail.$refs.editorRef.value) {
        this.$toast({
          content: this.$t('原因说明不能为空'),
          type: 'warning'
        })
        return
      }
      let _untaxedPriceCostCenter = _costCenterList?.map((e) => e.untaxedPrice) // 获取成本中心 - 不含税金额字段
      let _untaxedPriceStandDetail = _standDetailList.map((e) => e.untaxedPrice) // 获取考核指标详情 - 不含税金额字段

      let _costCenter = this.sum(_untaxedPriceCostCenter)
      let _standDetail = this.sum(_untaxedPriceStandDetail)
      console.log('成本中心与考核指标详情的不含税金额不相等', _costCenter, _standDetail)
      if (_costCenter && _costCenter.toFixed(5) !== _standDetail.toFixed(5))
        return this.$toast({
          content: this.$t('成本中心与考核指标详情的不含税金额不相等'),
          type: 'warning'
        })
      _standDetailList?.forEach((e) => {
        e.happenTime = Number(new Date(e.happenTime))
      })
      let _tempData = {
        ...this.$refs.claimDetail.$refs.formInfo.model,
        feedbackEndTime: Number(this.$refs.claimDetail.$refs.formInfo.model.feedbackEndTime),
        reasonDesc: this.$refs.claimDetail.$refs.editorRef.value,
        costCenterList: _costCenterList || [],
        // costCenterList: [],
        standDetailList: _standDetailList,
        attachmentList: _attachmentList,
        id: this.queryId
      }
      console.log('saveAssessDetail=', this, _tempData)
      await this.$API.assessManage.completeClaim(_tempData).then((res) => {
        if (res.code === 200) {
          this.isAssessment = false
          this.getDetail()
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
        }
      })
      // }, 100)
    },
    saveAndCommit() {
      // this.$refs.claimDetail.$refs.costCenterTemplateRef
      //   .getCurrentUsefulRef()
      //   .gridRef.ejsRef.endEdit();
      this.$refs.claimDetail.$refs.assessIndexTemplateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.endEdit()
      setTimeout(() => {
        let _costCenterList =
          this.$refs.claimDetail.$refs?.costCenterTemplateRef?.getCurrentUsefulRef().ejsRef
            .ej2Instances.currentViewData
        let _standDetailList =
          this.$refs.claimDetail.$refs.assessIndexTemplateRef.getCurrentUsefulRef().ejsRef
            .ej2Instances.currentViewData
        let _attachmentList =
          this.$refs.claimDetail.$refs.attachmentRef.getCurrentUsefulRef().ejsRef.ej2Instances
            .currentViewData
        // if (!_costCenterList.length) {
        //   this.$toast({
        //     content: this.$t('成本中心分摊比例总计应为100%'),
        //     type: 'warning'
        //   })
        //   return
        // // } else {

        // 考核单指标详情 & 成本中心计算汇总对比
        let _untaxedPriceCostCenter = _costCenterList?.map((e) => e.untaxedPrice) // 获取成本中心 - 不含税金额字段
        let _untaxedPriceStandDetail = _standDetailList.map((e) => e.untaxedPrice) // 获取考核指标详情 - 不含税金额字段

        let _costCenter = this.sum(_untaxedPriceCostCenter)
        let _standDetail = this.sum(_untaxedPriceStandDetail)

        if (_costCenter && _costCenter.toFixed(5) !== _standDetail.toFixed(5))
          return this.$toast({
            content: this.$t('成本中心与考核指标详情的不含税金额不相等'),
            type: 'warning'
          })

        if (!_standDetailList.length) {
          this.$toast({
            content: this.$t('考核指标详情不能为空'),
            type: 'warning'
          })

          return
        } else {
          if (
            sessionStorage.getItem('claimCompanyCode') ===
              util.getSupplierDict('BDComCode')[0].dictCode &&
            _standDetailList.some((e) => !e.costCenterCode)
          ) {
            this.$toast({
              content: this.$t('考核指标详情明细行成本中心不能为空'),
              type: 'warning'
            })
            return
          }
          if (_standDetailList.some((e) => !e.happenTime)) {
            this.$toast({
              content: this.$t('考核指标详情明细行发生时间不能为空'),
              type: 'warning'
            })
            return
          }
          if (_standDetailList.some((e) => !e.claimDesc)) {
            this.$toast({
              content: this.$t('考核指标详情明细行考核说明不能为空'),
              type: 'warning'
            })
            return
          }
        }
        _standDetailList?.forEach((e) => {
          e.happenTime = Number(new Date(e.happenTime))
        })
        let _tempData = {
          ...this.$refs.claimDetail.$refs.formInfo.model,
          feedbackEndTime: Number(this.$refs.claimDetail.$refs.formInfo.model.feedbackEndTime),
          reasonDesc: this.$refs.claimDetail.$refs.editorRef.value,
          costCenterList: _costCenterList || [],
          // costCenterList: [],
          standDetailList: _standDetailList,
          attachmentList: _attachmentList,
          id: this.queryId
        }
        console.log('saveAssessDetail=', this, _tempData)
        this.$API.assessManage.completeAndSubmitClaim(_tempData).then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('保存并提交成功'),
              type: 'success'
            })
            this.cancel()
          }
        })
      }, 100)
    },
    setFileUploadBtn(val) {
      this.showFileUploadBtn = val
    },
    handleUpload() {
      let _attachmentList =
        this.$refs.claimDetail.$refs.attachmentRef.getCurrentUsefulRef().ejsRef.ej2Instances
          .currentViewData

      if (_attachmentList && _attachmentList.length === 0) {
        this.$toast({
          content: this.$t('采方附件不能为空'),
          type: 'warning'
        })
        return
      }

      let params = {
        attachmentList: _attachmentList,
        id: this.queryId
      }
      this.$API.assessManage.saveClaimAttachmentsApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('附件提交成功'),
            type: 'success'
          })
          this.cancel()
        }
      })
    },
    sum(arr) {
      let sumValue = 0
      arr?.forEach((item) => (sumValue = sumValue + Number(item)))
      return sumValue
      //return eval(arr?.join('+'))
    },
    async print() {
      let _standDetailList =
        this.$refs.claimDetail.$refs.assessIndexTemplateRef.getCurrentUsefulRef().ejsRef
          .ej2Instances.currentViewData
      if (this.formObject.standDetailList.length < 1 && _standDetailList.length < 1) {
        this.$toast({
          content: this.$t('请先完善考核指标明细'),
          type: 'warning'
        })
        return
      }
      if (this.formObject.standDetailList.length < 1 && _standDetailList.length > 0) {
        this.$toast({
          content: this.$t('请先保存考核指标明细'),
          type: 'warning'
        })
        return
      }
      console.log(this.formObject.standDetailList, _standDetailList)
      if (!isEqual(this.formObject.standDetailList, _standDetailList)) {
        this.$toast({
          content: this.$t('有新的修改点请先保存考核指标明细'),
          type: 'warning'
        })
        return
      }
      let _id = this.queryId
      let buffer = await this.$API.assessFeedback.purchaserPrintClaim({ id: _id }).catch(() => {})
      buffer?.data && (buffer = buffer.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('claimTypeCode')
    sessionStorage.removeItem('claimCompanyCode')
  }
}
</script>
<style lang="scss" scoped>
.detail-card {
  width: 100%;
  height: 100px;
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 20px;
  display: flex;

  .logo {
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    background: #00469c;
    font-size: 40px;
    font-weight: bold;
    color: #fff;
    border-radius: 50%;
    margin-right: 22px;
  }
  .desc {
    flex: 1;
    height: 57px;
    .desc-title-box {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      .title {
        font-size: 20px;
        font-weight: bold;
        color: #292929;
      }
      .tag {
        font-size: 12px;
        display: inline-block;
        padding: 4px;
        border-radius: 2px;
      }
      .status {
        color: #9a9a9a;
        background: rgba(154, 154, 154, 0.1);
        margin: 0 10px;
      }
      .group {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
    .desc-detail-box {
      display: flex;
      .detail-item {
        font-size: 12px;
        color: #9a9a9a;
        margin-right: 20px;
      }
      .link {
        color: #6386c1;
        cursor: pointer;
      }
    }
  }
  .buttons-box {
    display: flex;
    align-items: center;
    .btn {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      color: #00469c;
      margin-left: 25px;
      cursor: pointer;
    }
    .is-disabled {
      color: #ccc;
    }
  }
}
.full-height {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.operateButton {
  position: absolute;
  right: 20px;
  z-index: 1;
  top: 10px;
}
.mt-tabs {
  width: 100%;
  background-color: #fafafa;
  /deep/.mt-tabs-container {
    width: 100%;
    margin-right: 155px;
  }
}
.reason {
  max-width: 350px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mt-tooptip {
  display: inline-block;
}
.mb_7 {
  margin-bottom: 7px;
}
/deep/ .mt-form-item {
  margin-bottom: 16px;
}
</style>
