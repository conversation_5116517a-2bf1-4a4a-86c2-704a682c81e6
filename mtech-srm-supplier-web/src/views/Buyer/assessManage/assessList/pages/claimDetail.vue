<template>
  <div>
    <div
      slot="slot-filter"
      :class="['top-filter', !basicExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('基本信息') }}</div>
      <div class="sort-box" @click="basicExpand = !basicExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="basicExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="form-box">
        <mt-form ref="formInfo" :model="basicInfo" :rules="rules" :auto-complete="false">
          <mt-form-item class="form-item" :label="$t('币种')" label-style="top" prop="currencyCode">
            <mt-select
              :disabled="![0, 3, 7].includes(queryType)"
              v-model="basicInfo.currencyCode"
              :data-source="currencyList"
              :show-clear-button="true"
              :allow-filtering="true"
              :filtering="(e) => filteringResource(e, currencyList, 'currencyName')"
              :placeholder="$t('请选择')"
              :fields="{ text: 'currencyName', value: 'currencyCode' }"
              @change="changeCurrencyCode"
              width="300"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('反馈截止时间')"
            label-style="top"
            prop="feedbackEndTime"
          >
            <!-- :disabled="queryType != 0 && queryType != 3"  //原来逻辑 -->
            <mt-date-time-picker
              :disabled="true"
              :width="300"
              v-model="basicInfo.feedbackEndTime"
              :placeholder="$t('选择日期和时间')"
            ></mt-date-time-picker>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('关联单据')"
            label-style="top"
            prop="referenceClaimCode"
          >
            <mt-select
              :disabled="![0, 3, 7].includes(queryType) || basicInfo.claimTypeCode != 'S05'"
              :width="300"
              :data-source="referenceList"
              v-model="basicInfo.referenceClaimCode"
              :fields="{ text: 'claimCode', value: 'claimCode' }"
              :placeholder="$t('请选择')"
              @change="referenceClaimChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('索赔总额')"
            label-style="top"
            prop="claimTotalAmount"
          >
            <mt-input
              :disabled="true"
              :width="300"
              v-model="basicInfo.claimTotalAmount"
              :min="0"
              :placeholder="$t('请输入')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('是否已线下确认')"
            label-style="top"
            prop="offlineEnsure"
          >
            <mt-select
              :disabled="![0, 3, 7].includes(queryType)"
              :width="300"
              :data-source="booleanList"
              v-model="basicInfo.offlineEnsure"
              :placeholder="$t('请选择')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('申诉是否必须上传附件')"
            label-style="top"
            prop="needAttachment"
          >
            <mt-select
              :disabled="![0, 3, 7].includes(queryType)"
              :width="300"
              :data-source="complaintAccessory"
              v-model="basicInfo.needAttachment"
              :placeholder="$t('请选择')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('上传发票')"
            label-style="top"
            v-if="queryType == 17"
          >
            <mt-common-uploader
              :is-single-file="false"
              :save-url="saveUrl"
              type="line"
              v-model="invoiceList"
            ></mt-common-uploader>
          </mt-form-item>
          <mt-form-item
            v-if="isShowCostCenter && info.claimTypeCode === 'S01'"
            class="form-item"
            :label="$t('组别')"
            label-style="top"
            prop="type"
          >
            <mt-select
              :width="300"
              :data-source="claimGroupTypeOption"
              v-model="basicInfo.type"
              :placeholder="$t('请选择')"
              :disabled="![0, 3, 7].includes(queryType)"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            v-if="showOdminField"
            class="form-item"
            :label="$t('是否逆向')"
            label-style="top"
            prop="reverse"
          >
            <mt-select
              :width="300"
              :data-source="reverseOption"
              v-model="basicInfo.reverse"
              :placeholder="$t('请选择')"
              :disabled="![0, 3, 7].includes(queryType)"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !handleAssessExpand && 'top-filter-small']"
      class="top-filter"
      v-if="showAppealDeal"
    >
      <div class="accordion-title">{{ $t('申诉处理') }}</div>
      <div class="sort-box" @click="handleAssessExpand = !handleAssessExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="handleAssessExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="form-box">
        <mt-form ref="formInfoHistroy" :model="formInfo" :auto-complete="false">
          <mt-form-item
            class="form-item"
            :label="$t('申诉处理')"
            label-style="top"
            prop="appealDeal"
          >
            <mt-select
              :disabled="![0, 3, 7].includes(queryType)"
              v-model="basicInfo.appealDeal"
              :data-source="handleClaimList"
              width="300"
            ></mt-select>
          </mt-form-item>
          <mt-form-item class="form-item full-width" :label="$t('决议说明')" label-style="top">
            <mt-input
              :disabled="![0, 3, 7].includes(queryType)"
              v-model="basicInfo.appealDealDesc"
              :multiline="true"
              :rows="2"
              maxlength="200"
              float-label-type="Never"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !assessIndexExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('考核指标详情') }}</div>
      <div class="sort-box" @click="assessIndexExpand = !assessIndexExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="assessIndexExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="assessIndexTemplateRef"
          :template-config="assessmentIndexConfig"
          @actionBegin="assessIndexActionBegin"
          @actionComplete="assessIndexActionComplete"
          @handleClickToolBar="handleClickAssessToolBar"
          @selectedChanged="assessIndexSelectedChanged"
        ></mt-template-page>
      </div>
    </div>
    <!-- v-if="1 == 2" -->
    <div
      v-if="isShowCostCenter"
      slot="slot-filter"
      :class="['top-filter', !costCenterExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('成本中心分摊') }}</div>
      <div class="sort-box" @click="costCenterExpand = !costCenterExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="costCenterExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="costCenterTemplateRef"
          :template-config="costCenterConfig"
          @actionBegin="costCenterActionBegin"
          @actionComplete="costCenterActionComplete"
          @handleClickToolBar="handleClickCostCenterToolBar"
          @selectedChanged="costCenterSelectedChanged"
        ></mt-template-page>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !reasonExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">
        <span style="color: red">{{ '*' }}&nbsp;</span>
        {{ $t('原因说明') }}
      </div>
      <div class="sort-box" @click="reasonExpand = !reasonExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="reasonExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box reason-desc">
        <mt-input
          :disabled="![0, 3, 7].includes(queryType)"
          ref="editorRef"
          v-model="templateText"
          :multiline="true"
          :rows="2"
          maxlength="5000"
          float-label-type="Never"
          :placeholder="$t('字数不超过5000字')"
        ></mt-input>
      </div>
    </div>
    <!-- 采方附件 -->
    <div
      slot="slot-filter"
      :class="['top-filter', !purAttachExpand && 'top-filter-small', 'mb-30']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('采方附件') }}</div>
      <div class="sort-box" @click="purAttachExpand = !purAttachExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="purAttachExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="attachmentRef"
          :template-config="purchaseAttachmentConfig"
          @handleClickToolBar="handleClickPurchaseAttachmentToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        ></mt-template-page>
      </div>
    </div>
  </div>
</template>
<script>
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
import { download } from '@/utils/utils'
import util from '@/utils/utils'

import {
  costCenterColumn,
  assessmentIndexColumn,
  attachmentColumn,
  editSettings,
  QMS,
  costCenterBDCol
} from './config/index'
import commonData from '@/utils/constant'
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    queryId() {
      return this.$route.query.id
    },
    queryType() {
      return this.$route.query.type
    },
    showOdminField() {
      return (
        this.info.companyCode === '1503' &&
        this.odminCategoryList.some((v) => v.itemCode === this.info.itemCode) &&
        localStorage.getItem('currentBu') !== 'GF'
      )
    },
    isPv() {
      return (
        localStorage.getItem('currentBu') === 'GF' &&
        ['S01', 'S08'].includes(this.info.claimTypeCode)
      )
    }
  },
  mounted() {
    console.log('inspectionSheetCompany', this.info)
    let params = {
      companyCode: this.info.companyCode,
      companyName: this.info.companyName
    }
    sessionStorage.setItem('inspectionSheetCompany', JSON.stringify(params))
    if (
      ((this.queryType != 0 &&
        this.queryType != 1 &&
        this.queryType != 3 &&
        this.queryType != 7 &&
        this.queryType != 12) ||
        (this.queryType == 12 && !this.info.offlineEnsure)) &&
      this?.info?.claimAppeal?.appealFlag &&
      this?.info?.claimAppealDeal
    ) {
      this.showAppealDeal = true
    }
    if (this.queryType == 17) {
      this.$API.assessManage.listClaimInvoice({ id: this.queryId }).then((res) => {
        this.invoiceList.length = 0
        if (res.data.length > 0) {
          res.data.forEach((e) => {
            this.invoiceList.push({
              id: e.attachmentId,
              fileName: e.attachmentName,
              fileSize: e.attachmentSize,
              url: e.attachmentUrl,
              uploadTime: e.uploadTime,
              uploadUserId: e.uploadUserId,
              uploadUserName: e.uploadUserName
            })
          })
        }
      })
    }
    this.getCurrencyList()
    this.listAvailableClaim()

    // if (this.info.status == 0) {
    //   this.basicInfo.currencyName = this.currencyList[0].currencyName
    // }
    this.basicInfo = {
      ...this.info,
      feedbackEndTime:
        this.info.feedbackEndTime && this.info.feedbackEndTime !== '0'
          ? new Date(Number(this.info.feedbackEndTime))
          : null,
      offlineEnsure: this?.info?.offlineEnsure?.toString() || 'false',
      needAttachment: this?.info?.needAttachment?.toString() || 'false',
      appealDealDesc: this?.info?.claimAppealDeal?.appealDealDesc,
      appealDeal: this?.info?.claimAppealDeal?.appealDeal
    }
    if (this.basicInfo.claimTypeCode == 'S05') {
      this.basicInfo.referenceClaimCode = this?.info?.referenceClaim?.claimCode
    } else if (this.basicInfo.claimTypeCode == 'S03') {
      this.basicInfo.referenceClaimCode = this?.info?.referenceOrder?.claimCode
    }
    this.totalUntaxedPrice = 0
    this.info.standDetailList.forEach((e) => {
      e.happenTime = utils.formateTime(new Date(Number(e.happenTime)), 'yyyy-MM-dd hh:mm')
      if (e.taxInclusive && e?.taxedPrice) {
        this.totalUntaxedPrice = this.totalUntaxedPrice + Number(e.untaxedPrice)
      } else if (!e.taxInclusive && e?.untaxedPrice) {
        this.totalUntaxedPrice = this.totalUntaxedPrice + Number(e.untaxedPrice)
      }
    })
    // this.info.costCenterList.forEach((e) => (e.costCenterAddId = this.costCenterAddId++))
    this.info.standDetailList.forEach((e) => (e.addId = this.addId++))
    this.costCenterConfig[0].grid.dataSource = this.info.costCenterList
    setTimeout(() => {
      this.assessmentIndexConfig[0].grid.dataSource = this.info.standDetailList
      this.purchaseAttachmentConfig[0].grid.dataSource = this.info.attachmentList
      this.templateText = this.info.reasonDesc
    }, 0.17)
    this.getCompanyCode()
  },
  data() {
    return {
      newArr: [],
      isBDcompany: false,
      isShowCostCenter: false,
      invoiceList: [],
      saveUrl: commonData.publicFileUrl,
      currencyFields: {
        text: 'title',
        value: 'currencyCode'
      },
      showAppealDeal: false,
      costCenterNowEditRowFlag: '', //当前编辑的行id
      costCenterAddId: 1,
      totalUntaxedPrice: 0,
      nowEditRowFlag: '', //当前编辑的行id
      addId: 1,
      templateText: null,
      selectedOtherInfo: {},
      selectedAssessIndexOtherInfo: {},
      isInner: '',
      booleanList: [
        { text: this.$t('否'), value: 'false' },
        { text: this.$t('是'), value: 'true' }
      ],
      complaintAccessory: [
        { text: this.$t('否'), value: 'false' },
        { text: this.$t('是'), value: 'true' }
      ],
      claimGroupTypeOption: [
        { text: this.$t('背光'), value: this.$t('背光') },
        { text: this.$t('电子'), value: this.$t('电子') },
        { text: this.$t('结构'), value: this.$t('结构') }
        // { text: this.$t('小件(PMC用)'), value: 'sp' },
        // { text: this.$t('包材(PMC用)'), value: 'pm' }
      ],
      reverseOption: [
        { text: this.$t('否'), value: 0 },
        { text: this.$t('是'), value: 1 }
      ],
      isPerPublish: false,
      referenceList: [],
      currencyList: [],
      basicExpand: true,
      handleAssessExpand: true,
      costCenterExpand: true,
      assessIndexExpand: true,
      reasonExpand: true,
      purAttachExpand: true,
      billPurAttachExpand: true,
      basicInfo: {},
      formInfo: {},
      rules: {
        currencyCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        feedbackEndTime: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        offlineEnsure: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        referenceClaimCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        type: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      handleClaimList: [
        {
          text: this.$t('维持原判'),
          value: 0
        },
        {
          text: this.$t('减免金额'),
          value: 1
        },
        {
          text: this.$t('改判指标'),
          value: 2
        },
        {
          text: this.$t('取消考核'),
          value: 3
        }
      ],
      costCenterConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar  endEdit
            tools: [['Add', 'Delete'], []]
          },
          useToolTemplate: false,
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            lineIndex: 1,
            columnData: costCenterColumn,
            height: 'auto',
            dataSource: []
          }
        }
      ],
      assessmentIndexConfig: [
        {
          isUseCustomEditor: true,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [['Add', 'Delete'], []]
          },
          useToolTemplate: false,
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top',
              allowEditOnDblClick: true
            },
            allowSorting: false,
            allowPaging: false,
            lineIndex: 1,
            columnData:
              this?.info?.createType == 1
                ? [...assessmentIndexColumn(this.info), ...QMS]
                : assessmentIndexColumn(this.info),
            height: 'auto',
            dataSource: []
          }
        }
      ],
      purchaseAttachmentConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [['Add', 'Edit', 'Delete'], []]
          },
          useToolTemplate: false,
          grid: {
            allowEditing: true,
            editSettings: editSettings,
            allowPaging: false,
            lineIndex: 1,
            columnData: attachmentColumn,
            height: 'auto',
            dataSource: []
          }
        }
      ],

      odminCategoryList: [],

      showFileUpload: false
    }
  },
  created() {
    this.getOdminCategoryList()
    if (![0, 3, 7].includes(Number(this.queryType))) {
      delete this.assessmentIndexConfig[0].toolbar
    }

    if (this.info.claimTypeCode != 'S05') {
      delete this.rules.referenceClaimCode
    }
    this.isBDcompany = this.info.companyCode === util.getSupplierDict('BDComCode')[0].dictCode

    if (
      this.isBDcompany &&
      !this.assessmentIndexConfig[0].grid.columnData.some(
        (column) => column.field === 'costCenterCode'
      )
    ) {
      // this.assessmentIndexConfig[0].grid.columnData.push(...costCenterBDCol)
      this.$set(
        this.assessmentIndexConfig[0].grid.columnData,
        this.assessmentIndexConfig[0].grid.columnData.length,
        ...costCenterBDCol
      )
    } else if (!this.isBDcompany) {
      const index = this.assessmentIndexConfig[0].grid.columnData.findIndex(
        (column) => column.field === 'costCenterCode'
      )
      if (index !== -1) {
        this.assessmentIndexConfig[0].grid.columnData.splice(index, 1)
      }
    }
  },
  provide() {
    return {
      assessIndexInfo: this.selectedAssessIndexOtherInfo
    }
  },
  methods: {
    getOdminCategoryList() {
      this.$API.assessManage
        .getDictCode({
          dictCode: 'ODMIN_CATEGORY'
        })
        .then((res) => {
          if (res.code === 200) {
            this.odminCategoryList = res.data
          }
          this.setFileUploadBtn()
        })
    },
    setFileUploadBtn() {
      this.showFileUpload =
        this.showOdminField && Number(this.queryType) == 12 && this.basicInfo.reverse == 1

      if (![0, 3, 7, 10].includes(Number(this.queryType))) {
        if (!this.showFileUpload) {
          delete this.purchaseAttachmentConfig[0].toolbar
        }
      }
      this.$emit('setBtn', this.showFileUpload)
    },
    // 获取公司代码（TV）
    getCompanyCode() {
      // const KT = util.getSupplierDict('TVComCode') || []
      // const BD = util.getSupplierDict('BDComCode') || []
      // const companyArr = KT.concat(BD)
      const TVCodeArr = util.getSupplierDict('TVComCode') || []
      TVCodeArr.forEach((e) => {
        if (this.info.companyCode === e.dictCode) {
          this.isShowCostCenter = true
        }
      })
    },
    handleClickCellTitle(e) {
      if (e.field == 'attachmentName') {
        this.preview(e.data)
      }
    },
    // billHandleClickCellTitle(e) {
    //   if (e.field == 'attachmentName') {
    //     this.preview(e.data)
    //   }
    // },
    preview(item) {
      let params = {
        id: item.attachmentId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    // 模糊搜索，不区分大小写模糊搜索
    filteringResource(e, dataSource, key) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          dataSource?.filter((f) => f[key]?.toUpperCase().includes(e?.text?.toUpperCase()))
        )
      } else {
        e.updateData(dataSource)
      }
    },
    // 下载文件
    handleDownloadFile(data) {
      this.$API.fileService
        .downloadPublicFile({
          id: data.attachmentId
        })
        .then((res) => {
          download({
            fileName: data.attachmentName,
            blob: res.data
          })
        })
    },
    listAvailableClaim() {
      this.$API.assessManage.listAvailableClaim({ id: this.queryId }).then((res) => {
        this.referenceList = res.data
      })
    },
    referenceClaimChange(e) {
      if (e.value) {
        this.basicInfo.referenceClaim = {
          claimCode: e.value,
          id: e.itemData.id
        }
      }
    },
    changeCurrencyCode(e) {
      if (e.value && e?.itemData?.currencyName) {
        this.basicInfo.currencyName = e.itemData.currencyName
      }
    },
    costCenterSelectedChanged(val) {
      this.isInner = val.itemInfo.isInner
      Object.assign(this.selectedOtherInfo, val.itemInfo || {})
      console.log(this.selectedOtherInfo, 'costCenterSelectedChanged最新的额外数据导入的')
    },
    assessIndexSelectedChanged(val) {
      this.isInner = val.itemInfo.isInner
      Object.assign(this.selectedAssessIndexOtherInfo, val.itemInfo || {})
      console.log(
        this.selectedAssessIndexOtherInfo,
        'assessIndexSelectedChanged最新的额外数据导入的'
      )
    },
    costCenterActionBegin(args) {
      let { data, requestType } = args
      console.log('costCenterActionBegin=', args)
      this.selectedAssessIndexOtherInfo.totalUntaxedPrice = this.totalUntaxedPrice
      // 行内数据新增
      if (requestType == 'add') {
        args.data.costCenterAddId = this.costCenterAddId++
        this.costCenterNowEditRowFlag = args.data.costCenterAddId
      } else if (requestType == 'beginEdit') {
        // console.log('costCenterActionBegin', args)
        this.costCenterNowEditRowFlag = args.rowData.costCenterAddId
        Object.assign(this.selectedAssessIndexOtherInfo, {
          totalUntaxedPrice: this.totalUntaxedPrice
        })
      } else if (requestType == 'save') {
        data.isInner = this.isInner
      }
    },
    assessIndexActionBegin(args) {
      let { data, requestType } = args
      console.log('assessIndexActionBegin=', args)
      // 行内数据新增
      if (requestType == 'add') {
        // for (let key in this.selectedAssessIndexOtherInfo) {
        //   this.selectedAssessIndexOtherInfo[key] = ''
        // }
        // args.data.addId = this.addId++
        // this.selectedAssessIndexOtherInfo.addId = args.data.addId
        // this.nowEditRowFlag = args.data.addId
        ;(args.data.taxInclusiveName = this.$t('否')), (args.data.taxInclusive = false)

        if (this.isPv) {
          // 光伏新增默认天数为1
          args.data.days = 1
          args.data.taxInclusive = true // 光伏必须含税
          args.data.taxInclusiveName = this.$t('是')
          args.data.isPv = true // 光伏行内编辑校验
        }
      } else if (requestType == 'beginEdit') {
        if (this.isPv) {
          args.data.isPv = true // 光伏行内编辑校验
        }
        if (args.rowData.taxInclusiveName === this.$t('是')) {
          args.rowData.taxInclusive = true
        }
        if (this.isBDcompany) {
          console.log('assessIndexActionBegin123=', args)

          // args.rowData['costCenterCode'] = null
        }
        let _tempData = cloneDeep(args)
        Object.assign(this.selectedAssessIndexOtherInfo, _tempData.rowData)
        delete this.selectedAssessIndexOtherInfo.happenTime
        delete this.selectedAssessIndexOtherInfo.quantity
        delete this.selectedAssessIndexOtherInfo.unitPrice
        delete this.selectedAssessIndexOtherInfo.claimDesc
        delete this.selectedAssessIndexOtherInfo.refRes
        delete this.selectedAssessIndexOtherInfo.refResUnitName
        delete this.selectedAssessIndexOtherInfo.refResQuantity
        delete this.selectedAssessIndexOtherInfo.refAmount
        delete this.selectedAssessIndexOtherInfo.remark
        this.nowEditRowFlag = args.rowData.addId
      } else if (requestType == 'save') {
        console.log('costCenterNamecostCenterName', args)
        if (args.data.taxInclusiveName === this.$t('是')) {
          args.data.taxInclusive = true
        } else if (args.data.taxInclusiveName === this.$t('否')) {
          args.data.taxInclusive = false
        }
        data.isInner = this.isInner
      } else if (requestType === 'refresh') {
        const dataSource = this.$refs.assessIndexTemplateRef
          .getCurrentUsefulRef()
          .gridRef.ejsRef.getCurrentViewRecords()
        // 仅一行新增数据时，失去编辑状态后会触发refresh，导致编辑的数据丢失
        if (dataSource.length === 1 && !dataSource[0].id) {
          args.cancel = true
        }
      }
    },
    costCenterActionComplete(item) {
      let { data, requestType } = item
      if (requestType == 'save') {
        // 验证必输
        if (!data.costCenterCode) {
          this.$toast({
            content: this.$t('有字段未输入'),
            type: 'warning'
          })
        }
      }
      // let row = this.getCostCenterRow()
      // console.log('actionCompleteactionCompleteactionComplete', item, row)
      this.$refs.costCenterTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
    },
    assessIndexActionComplete(item) {
      let { requestType } = item
      if (requestType === 'save') {
        this.newArr = this.$refs.assessIndexTemplateRef
          .getCurrentUsefulRef()
          .gridRef.ejsRef.getCurrentViewRecords()

        this.getAssessIndexRow(this.newArr)
      }

      this.$refs.assessIndexTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
    },
    getCostCenterRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.costCenterTemplateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.costCenterAddId == this.costCenterNowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    getAssessIndexRow(currentRecords = []) {
      //获取编辑或者新增的数据
      // let currentRecords = this.$refs.assessIndexTemplateRef
      //   .getCurrentUsefulRef()
      //   .gridRef.ejsRef.getCurrentViewRecords()

      let _tempNum = 0
      this.totalUntaxedPrice = 0
      for (let i = 0; i < currentRecords.length; i++) {
        if (currentRecords[i].taxInclusive && currentRecords[i]?.taxedPrice) {
          _tempNum = _tempNum + Number(currentRecords[i].taxedPrice)
          this.totalUntaxedPrice = this.totalUntaxedPrice + Number(currentRecords[i].untaxedPrice)
        } else if (!currentRecords[i].taxInclusive && currentRecords[i]?.untaxedPrice) {
          _tempNum = _tempNum + Number(currentRecords[i].untaxedPrice)
          this.totalUntaxedPrice = this.totalUntaxedPrice + Number(currentRecords[i].untaxedPrice)
        }
      }
      this.basicInfo.claimTotalAmount = _tempNum
    },
    handleClickCostCenterToolBar(args) {
      const { toolbar, grid } = args
      console.log('handleClickCostCenterToolBar=', this, args)
      if (toolbar.id == 'Add') {
        // 新增
        this.$refs.costCenterTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'Delete') {
        if (this.type == 'edit') {
          this.$toast({
            content: this.$t('编辑状态下不可删除行数据'),
            type: 'warning'
          })
        } else {
          // 选中的数据
          let selectedRecords = grid.getSelectedRecords()
          if (selectedRecords.length > 0) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认删除选中的数据？')
              },
              success: () => {
                this.$refs.costCenterTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
              }
            })
          } else {
            this.$toast({
              content: this.$t('请先选择一行'),
              type: 'warning'
            })
          }
        }
      } else if (toolbar.id == 'endEdit') {
        this.$refs.costCenterTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    handleClickAssessToolBar(args) {
      const { toolbar, grid } = args
      console.log('handleClickAssessToolBar=', this, args)
      if (toolbar.id == 'Add') {
        // 新增
        this.$refs.assessIndexTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'Delete') {
        if (this.type == 'edit') {
          this.$toast({
            content: this.$t('编辑状态下不可删除行数据'),
            type: 'warning'
          })
        } else {
          // 选中的数据
          let selectedRecords = grid.getSelectedRecords()
          if (selectedRecords.length > 0) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认删除选中的数据？')
              },
              success: () => {
                this.$refs.assessIndexTemplateRef
                  .getCurrentUsefulRef()
                  .gridRef.ejsRef.deleteRecord()
              }
            })
          } else {
            this.$toast({
              content: this.$t('请先选择一行'),
              type: 'warning'
            })
          }
        }
      } else if (toolbar.id == 'endEdit') {
        this.$refs.assessIndexTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    getCurrencyList() {
      this.$API.masterData.queryAllCurrency().then((res) => {
        res.data.forEach((item) => {
          item.currencyName = item.currencyCode + '-' + item.currencyName
        })
        this.currencyList = res.data
        if (this.info.status == 0) {
          this.basicInfo.currencyName = this.currencyList[0].currencyName
          this.basicInfo.currencyCode = this.currencyList[0].currencyCode
        }
      })
    },
    handleClickPurchaseAttachmentToolBar(e) {
      let sltList = e.grid.getSelectedRecords()
      let selectedRowIndexes = e.grid.getSelectedRowIndexes()
      if (
        (!sltList || sltList.length <= 0) &&
        (e.toolbar.id == 'Edit' || e.toolbar.id == 'Delete')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      console.log('handleClickPurchaseAttachmentToolBar=', e)
      if (e.toolbar.id == 'Add') {
        this.$dialog({
          modal: () => import('./editComponents/uploadDialog.vue'),
          data: {
            fileData: [],
            isView: false, // 是否为预览
            required: false, // 是否必须
            title: this.$t('新增采方附件')
          },
          success: (res) => {
            this.handleUploadFiles(res)
          }
        })
      } else if (e.toolbar.id == 'Edit') {
        if (sltList.length > 1) {
          this.$toast({
            content: this.$t('不能同时编辑多行'),
            type: 'warning'
          })
          return
        }
        this.$dialog({
          modal: () => import('./editComponents/uploadDialog.vue'),
          data: {
            title: this.$t('编辑采方附件'),
            isEdit: true,
            info: {
              ...sltList[0],
              fileName: sltList[0].attachmentName,
              fileSize: sltList[0].attachmentSize,
              url: sltList[0].attachmentUrl
            },
            index: selectedRowIndexes[0]
          },
          success: (data) => {
            let _tempData = cloneDeep(this.purchaseAttachmentConfig[0].grid.dataSource)
            _tempData[selectedRowIndexes[0]] = data
            this.$set(this.purchaseAttachmentConfig[0].grid, 'dataSource', _tempData)
            this.$refs.attachmentRef.refreshCurrentGridData()
          }
        })
      } else if (e.toolbar.id == 'Delete') {
        let _tempData = cloneDeep(this.purchaseAttachmentConfig[0].grid.dataSource)
        let _fileIds = sltList.map((x) => x.id)
        let _newData = _tempData.filter((element) => !_fileIds.includes(element.id))
        this.$set(this.purchaseAttachmentConfig[0].grid, 'dataSource', _newData)
      }
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'download') {
        this.handleDownloadFile(data)
      }
    },
    handleUploadFiles(data) {
      console.log('handleUploadFiles=', data)

      let _tempData = {
        ...data,
        attachmentId: data.id,
        attachmentName: data.fileName,
        attachmentSize: data.fileSize,
        attachmentUrl: data.url
      }
      this.purchaseAttachmentConfig[0].grid.dataSource.push(_tempData)
    }
  }
}
</script>
<style lang="scss" scoped>
.e-content {
  padding: 20px 0 20px 0;
}
.top-filter {
  background: #fff;
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
// .accordion-title {
//   float: left;
//   font-size: 14px;
//   margin-left: 20px;
//   font-family: PingFangSC;
//   font-weight: 500;
//   color: rgba(41, 41, 41, 1);
//   text-indent: 10px;
//   border-left: 5px solid #00469c;
//   margin-bottom: 20px;
//   border-radius: 2px 0 0 2px;
// }

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 15px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
.form-box {
  width: 100%;
  display: flex;
  .mt-form {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    margin: 10px 0 10px 28px;
    .mt-form-item {
      margin-right: 20px;
      /deep/ textarea {
        color: #000000de !important;
        -webkit-text-fill-color: #000000de;
      }
    }
  }
}
.full-width {
  width: 100%;
}
.table-box {
  margin: 0 20px;
}
.reason-desc {
  margin-top: 5px;
  /deep/ .mt-input {
    textarea {
      border: 1px solid #e0e0e0;
      color: #000000de !important;
      -webkit-text-fill-color: #000000de !important;
    }
  }
}
/deep/ .mt-rich-text-editor {
  margin: 30px 10px 0 10px;
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-toolbar-items {
    margin-left: -59px;
  }
  .e-rte-content {
    height: 300px !important;
  }
}
.mb-30 {
  // margin-bottom: 30px;
}

::v-deep .mt-data-grid {
  .e-grid {
    .e-gridcontent {
      min-height: 100px;
    }
  }
}
</style>
