<template>
  <div class="full-height" style="background-color: #fff">
    <!-- 顶部按钮 -->
    <div class="btnGroups">
      <icon-button
        icon="icon_solid_restart"
        :type="!isAllowEdit ? 'info' : 'primary'"
        :text="$t('返回')"
        @click="goBack"
      />
      <icon-button
        type="info"
        icon="icon_solid_Save"
        v-if="!isAllowEdit"
        :text="$t('保存')"
        @click="saveCompanyInfo(0)"
      />
      <icon-button
        type="primary"
        icon="icon_solid_Save"
        v-if="!isAllowEdit"
        :text="$t('保存并提交')"
        @click="saveCompanyInfo(1)"
      />
    </div>

    <div style="margin: 0 10px">
      <!-- 基础信息选择 -->
      <mt-form ref="ruleForm" class="" :model="formData" :rules="rules">
        <mt-row :gutter="12">
          <mt-col :span="6">
            <mt-form-item prop="code" :label="$t('清单编码')" label-style="top">
              <mt-input v-model="formData.code" disabled></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="oaTitle" :label="$t('呆料清单标题')" label-style="top">
              <mt-input
                v-model="formData.oaTitle"
                :show-clear-button="true"
                :disabled="isAllowEdit"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="statusDesc" :label="$t('状态')" label-style="top">
              <mt-input v-model="formData.statusDesc" disabled></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="emergencyDegree" :label="$t('紧急程度')" label-style="top">
              <mt-select
                v-model="formData.emergencyDegree"
                :data-source="exigencyOptions"
                :show-clear-button="true"
                :fields="{ text: 'text', value: 'value' }"
                :disabled="isAllowEdit"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="12">
          <mt-col :span="6">
            <mt-form-item prop="typeName" :label="$t('类型')" label-style="top">
              <vxe-pulldown ref="pulldownRefType" destroy-on-close>
                <template #default>
                  <vxe-input
                    v-model="formData.typeName"
                    :placeholder="$t('请输入类型或选择类型')"
                    suffix-icon="vxe-icon-arrow-down"
                    @suffix-click="togglePanel('pulldownRefType')"
                    :disabled="isAllowEdit"
                  ></vxe-input>
                </template>
                <template #dropdown>
                  <vxe-list height="170" class="my-dropdown2" :data="typeNameOption" auto-resize>
                    <template #default="{ items }">
                      <div
                        class="list-item2"
                        v-for="item in items"
                        :key="item.value"
                        @click="selectEvent(item, 'typeName', 'pulldownRefType')"
                      >
                        <span>{{ item.text }}</span>
                      </div>
                    </template>
                  </vxe-list>
                </template>
              </vxe-pulldown>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="categName" :label="$t('类别')" label-style="top">
              <vxe-pulldown ref="pulldownRefCateg" destroy-on-close>
                <template #default>
                  <vxe-input
                    v-model="formData.categName"
                    :placeholder="$t('请输入类别或选择类别')"
                    suffix-icon="vxe-icon-arrow-down"
                    @suffix-click="togglePanel('pulldownRefCateg')"
                    :disabled="isAllowEdit"
                  ></vxe-input>
                </template>
                <template #dropdown>
                  <vxe-list height="170" class="my-dropdown2" :data="categNameOption" auto-resize>
                    <template #default="{ items }">
                      <div
                        class="list-item2"
                        v-for="item in items"
                        :key="item.value"
                        @click="selectEvent(item, 'categName', 'pulldownRefCateg')"
                      >
                        <span>{{ item.text }}</span>
                      </div>
                    </template>
                  </vxe-list>
                </template>
              </vxe-pulldown>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item
              prop="companyCode"
              class="form-item"
              label-style="top"
              :label="$t('所属公司')"
            >
              <RemoteAutocomplete
                :remote-search="true"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                v-model="formData.companyCode"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                @change="orgChange"
                :disabled="isAllowEdit"
                :width="340"
                :placeholder="$t('请选择所属公司')"
                select-type="administrativeCompany"
              ></RemoteAutocomplete>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="centerCode" :label="$t('中心')" label-style="top">
              <mt-select
                v-model="formData.centerCode"
                :data-source="centerOption"
                :show-clear-button="true"
                :fields="{ text: 'text', value: 'value' }"
                :disabled="isAllowEdit"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="12">
          <mt-col :span="6">
            <mt-form-item
              prop="machineModel"
              class="form-item"
              label-style="top"
              :label="$t('机型/机芯')"
            >
              <mt-input
                maxlength="80"
                v-model="formData.machineModel"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
                :disabled="isAllowEdit"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="reason" :label="$t('原因分类')" label-style="top">
              <vxe-pulldown ref="pulldownRefReason" destroy-on-close>
                <template #default>
                  <vxe-input
                    v-model="formData.reason"
                    :placeholder="$t('请输入原因或选择原因')"
                    suffix-icon="vxe-icon-arrow-down"
                    @suffix-click="togglePanel('pulldownRefReason')"
                    :disabled="isAllowEdit"
                  ></vxe-input>
                </template>
                <template #dropdown>
                  <vxe-list height="220" class="my-dropdown2" :data="reasonOption" auto-resize>
                    <template #default="{ items }">
                      <div
                        class="list-item2"
                        v-for="item in items"
                        :key="item.value"
                        @click="selectEvent(item, 'reason', 'pulldownRefReason')"
                      >
                        <span>{{ item.text }}</span>
                      </div>
                    </template>
                  </vxe-list>
                </template>
              </vxe-pulldown>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="auditMan" :label="$t('审核人')" label-style="top">
              <mt-select
                v-model="formData.auditMan"
                :data-source="auditManList"
                :fields="{
                  text: 'employeeName',
                  value: 'employeeName'
                }"
                :placeholder="$t('请选择审核人')"
                popup-width="370"
                :allow-filtering="true"
                :filtering="getAuditMan"
                :open-dispatch-change="true"
                :disabled="isAllowEdit"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="respUserAcct" :label="$t('部门负责人')" label-style="top">
              <mt-multi-select
                v-model="formData.respUserAcct"
                id="dropDownTreeCom"
                style="width: 100%"
                :fields="{
                  text: 'employeeName',
                  value: 'employeeName'
                }"
                popup-width="370"
                :data-source="userArrList"
                filter-bar-:placeholder="$t('Search')"
                :allow-filtering="!isAllowEdit"
                :filtering="getRespUser"
                :placeholder="$t('请选择部门负责人')"
                :filter-bar-placeholder="$t('请输入用户名称进行搜索')"
                :no-records-template="noRecordsTemplate"
              ></mt-multi-select>
              <!-- <PersonnelSelector
                :disabled="isAllowEdit"
                :default-value="formData.respUserAcct"
                @change="(e) => (formData.respUserAcct = e)"
              /> -->
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="12">
          <mt-col :span="6">
            <mt-form-item prop="ccEmail" :label="$t('抄送相关人员')" label-style="top">
              <mt-multi-select
                v-model="formData.ccEmail"
                id="dropDownTreeCom"
                style="width: 100%"
                :fields="{
                  text: 'employeeName',
                  value: 'employeeName'
                }"
                popup-width="370"
                :data-source="ccEmailuserArrList"
                filter-bar-:placeholder="$t('Search')"
                :allow-filtering="!isAllowEdit"
                :filtering="getCCEmail"
                :placeholder="$t('请选择抄送相关人员')"
                :filter-bar-placeholder="$t('请输入用户名称进行搜索')"
                :no-records-template="noRecordsTemplate"
              ></mt-multi-select>
              <!-- <PersonnelSelector
                :disabled="isAllowEdit"
                :default-value="formData.ccEmail"
                @change="(e) => (formData.ccEmail = e)"
              /> -->
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="purchaseRespUser" :label="$t('采购部审核')" label-style="top">
              <mt-multi-select
                v-model="formData.purchaseRespUser"
                id="dropDownTreeCom"
                style="width: 100%"
                :fields="{
                  text: 'employeeName',
                  value: 'employeeName'
                }"
                popup-width="370"
                :data-source="purchaseRespList"
                filter-bar-:placeholder="$t('Search')"
                :allow-filtering="!isAllowEdit"
                :filtering="getPurchaseResp"
                :placeholder="$t('请选择采购部审核人员')"
                :filter-bar-placeholder="$t('请输入用户名称进行搜索')"
                :no-records-template="noRecordsTemplate"
              ></mt-multi-select>
              <!-- <PersonnelSelector
                :disabled="isAllowEdit"
                :default-value="formData.purchaseRespUser"
                @change="(e) => (formData.purchaseRespUser = e)"
              /> -->
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="totalAmt" :label="$t('呆滞料总金额')" label-style="top">
              <mt-input v-model="formData.totalAmt" disabled></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="claimTotalAmt" :label="$t('索赔总额')" label-style="top">
              <mt-input v-model="formData.claimTotalAmt" disabled></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="12">
          <mt-col :span="6">
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input v-model="formData.createUserName" disabled></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="createDate" :label="$t('创建时间')" label-style="top">
              <mt-input v-model="formData.createDate" disabled></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="12">
          <mt-col :span="24">
            <mt-form-item
              class="form-item full-width"
              :label="
                $t(
                  '事由（务必填写机型/机芯/配屏、原因、导致呆滞料金额、事件经过、申请内容、责任单位和成本中心代码）'
                )
              "
              label-style="top"
              prop="eventDesc"
            >
              <rich-text-editor
                style="margin-top: 10px"
                :toolbar="clientToolbar"
                :enable-resize="true"
                :height="200"
                ref="editor"
                v-model="formData.eventDesc"
                :readonly="isAllowEdit"
              ></rich-text-editor>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>

    <!-- 呆料清单详情表格 -->
    <div style="margin-top: 24px">
      <div class="accordion-title">{{ $t('呆料清单详情') }}</div>
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        :use-tool-template="false"
        @handleClickToolBar="handleClickToolBar"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
      ></mt-template-page>
    </div>

    <!-- 附件上传 -->
    <div style="margin-top: 24px">
      <div class="accordion-title">{{ $t('附件') }}</div>
      <div class="table-box">
        <mt-template-page
          ref="attachmentRef"
          :template-config="purchaseAttachmentConfig"
          @handleClickToolBar="handleClickPurchaseAttachmentToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        ></mt-template-page>
      </div>
    </div>
  </div>
</template>
<script>
import { deadmaterialDetailCols, editSettings, attachmentColumn } from './config/index'
import iconButton from '@/components/iconButton/index.vue'
// import { utils } from '@mtech-common/utils'
import RichTextEditor from '@/components/RichTextEditor/RichTextEditor.vue'
import { cloneDeep } from 'lodash'
import { download } from '@/utils/utils'
import utils from '@/utils/utils'
import Vue from 'vue'
import debounce from 'lodash.debounce'
import { Input, Pulldown, List } from 'vxe-table'
Vue.use(Input).use(Pulldown).use(List)
// import PersonnelSelector from '@/components/PersonnelSelector'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    iconButton,
    RichTextEditor,
    // PersonnelSelector,
    RemoteAutocomplete
  },
  data() {
    const deadStockFactory = utils.getSupplierDict('IDLE_MATERIAL_PROFIT_FACTORY_MAP') || []

    return {
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        text: 'supplierName',
        value: 'supplierCode'
      },
      companyId: '',
      // 富文本快速工具栏
      clientToolbar: `bold italic underline | formatselect alignleft aligncenter alignright alignjustify bullist numlist | table link image backcolor | code undo redo`,
      // 富文本插件
      clientPluginsContent: `lists, advlist autolink autosave charmap directionality fullscreen hr image link nonbreaking pagebreak preview quickbars searchreplace tabfocus table image code wordcount anchor print autoresize`,
      deadStockFactory,
      //部门审核人下拉
      respUserAcctList: {
        dataSource: [], //部门审核人数组
        value: 'value',
        text: 'text',
        child: 'childrens',
        key: 'E6BPdKfEj5Ky2NwbH2F6Nkrhj6W8MamF'
      },
      //抄送人下拉
      ccEmailList: {
        dataSource: [], //部门审核人数组
        value: 'value',
        text: 'text',
        child: 'childrens',
        key: 'E6BPdKfEj5Ky2NwbH2F6Nkrhj6W8MaND'
      },
      purchaseRespUserList: {
        dataSource: [], //采购审核人数组
        value: 'value',
        text: 'text',
        child: 'childrens',
        key: 'E6BPdKfEj5Ky2NwbH2F6Nkrhj64rMaND'
      },
      demandDesc: '',
      formData: {
        emergencyDegree: '',
        typeName: '',
        categName: '',
        approvalNo: '',
        centerCode: '',
        machineModel: '',
        reason: '',
        companyCode: '',
        companyName: '',
        respUserAcct: '',
        auditMan: '',
        ccEmail: '',
        purchaseRespUser: '',
        totalAmt: '',
        eventDesc: '' // 事由
      },
      // 下拉枚举值
      perEvaluationOptions: [],
      employeeInfoOption: [],
      typeNameOption: [
        { text: this.$t('报呆'), value: this.$t('报呆'), id: 1 },
        { text: this.$t('变卖'), value: this.$t('变卖'), id: 2 },
        { text: this.$t('1693报废'), value: this.$t('1693报废'), id: 3 },
        { text: this.$t('P0折价'), value: this.$t('P0折价'), id: 4 },
        { text: this.$t('商返业务'), value: this.$t('商返业务'), id: 5 }
      ],
      categNameOption: [
        { text: this.$t('屏'), value: this.$t('屏') },
        { text: this.$t('非屏'), value: this.$t('非屏') },
        { text: this.$t('整机'), value: this.$t('整机') },
        { text: this.$t('模组'), value: this.$t('模组') },
        { text: this.$t('塑胶件'), value: this.$t('塑胶件') }
      ],
      reasonOption: [
        { text: this.$t('ECN更改'), value: this.$t('ECN更改') },
        { text: this.$t('需求调整'), value: this.$t('需求调整') },
        { text: this.$t('产品淘汰'), value: this.$t('产品淘汰') },
        { text: this.$t('屏供应'), value: this.$t('屏供应') },
        { text: this.$t('非屏供应'), value: this.$t('非屏供应') },
        { text: this.$t('标包'), value: this.$t('标包') },
        { text: this.$t('产品规划'), value: this.$t('产品规划') },
        { text: this.$t('产品质量'), value: this.$t('产品质量') },
        { text: 'NPI', value: 'NPI' },
        { text: this.$t('降本'), value: this.$t('降本') }
      ],
      centerOption: [
        { text: this.$t('运营中心'), value: 1 },
        { text: this.$t('研发中心'), value: 2 }
      ],
      exigencyOptions: [
        { text: this.$t('紧急'), value: 1 },
        { text: this.$t('普通'), value: 2 }
      ],
      statusList: [
        { code: 1, label: this.$t('新建') },
        { code: 2, label: this.$t('待审批') },
        { code: 3, label: this.$t('已驳回') },
        { code: 4, label: this.$t('已通过') },
        { code: 5, label: this.$t('已废弃') }
      ],
      companyList: [],
      pageConfig: [
        {
          gridId: '7740e002-7a92-4fa6-bfbf-a6b8b65d0995',
          isUseCustomEditor: true,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], ['Setting']]
          },
          useToolTemplate: false,
          grid: {
            columnData: deadmaterialDetailCols,
            editSettings,
            height: 'auto',
            asyncConfig: {
              url: '/analysis/tenant/idleMaterialVoucher/itemPageQuery',
              defaultRules: [
                {
                  field: 'headerId',
                  operator: 'equal',
                  value: ''
                }
              ],
              serializeList: (list) => {
                console.log(list)
                list?.forEach((e) => {
                  e.qtyText = e.qty
                  e.invQtyText = e.invQty
                  e.transitQtyText = e.transitQty
                  e.unitPriceText = e.unitPrice
                  e.actualAmtText = e.actualAmt
                  // 传入单头的公司id
                  e.companyCode = this.companyId
                })
                return list
              }
            }
          }
        }
      ],
      purchaseAttachmentConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], []]
          },
          useToolTemplate: false,
          grid: {
            allowEditing: true,
            editSettings,
            allowPaging: false,
            lineIndex: 1,
            columnData: attachmentColumn,
            // height: 350,
            dataSource: []
          }
        }
      ],
      headerId: '',
      isReload: true,
      noRecordsTemplate: this.$t('请输入用户名称进行搜索'),
      userArrList: [], // 部门审核人可选列表
      ccEmailuserArrList: [], // 抄送相关负责人列表
      purchaseRespList: [], // 采购部审核人列表
      auditManList: [] // 审核人列表
    }
  },
  computed: {
    // 不要看命名，返回true是禁止编辑，false是允许编辑（定义就很奇怪）
    isAllowEdit() {
      if (this.$route.query.id && JSON.parse(sessionStorage.getItem('deadMaterialsListInfo'))) {
        const rowStatus = JSON.parse(sessionStorage.getItem('deadMaterialsListInfo'))
        if (
          this.isSameUser &&
          (rowStatus.status === 1 || rowStatus.status === 3 || rowStatus.status === 5)
        ) {
          return false
        }
        return true
      } else if (!this.$route.query.id) {
        return false
      } else {
        return false
      }
    },
    isSameUser() {
      // 当前用户与创建用户是否一致
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      return userInfo.accountId === this.formData.createUserId
    },
    rules() {
      return {
        oaTitle: [
          {
            required: this.headerId,
            message: this.$t('请输入呆料清单标题'),
            trigger: 'blur'
          }
        ],
        emergencyDegree: [
          {
            required: true,
            message: this.$t('请选择紧急程度'),
            trigger: 'blur'
          }
        ],
        typeName: [
          {
            required: true,
            message: this.$t('请选择或输入类型'),
            trigger: 'blur'
          }
        ],
        categName: [
          {
            required: true,
            message: this.$t('请选择或输入类别'),
            trigger: 'blur'
          }
        ],
        companyCode: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ],
        centerCode: [
          {
            required: true,
            message: this.$t('请选择中心'),
            trigger: 'blur'
          }
        ],
        machineModel: [
          {
            required: true,
            message: this.$t('请输入机型/机芯'),
            trigger: 'blur'
          }
        ],
        reason: [
          {
            required: true,
            message: this.$t('请选择原因'),
            trigger: 'blur'
          }
        ],
        respUserAcct: [
          {
            required: true,
            message: this.$t('请选择部门负责人'),
            trigger: 'blur'
          }
        ],
        auditMan: [
          {
            required: true,
            message: this.$t('请选择审核人'),
            trigger: 'blur'
          }
        ],
        eventDesc: [
          {
            required: true,
            message: this.$t('请输入事由'),
            trigger: 'blur'
          }
        ],
        orgId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        deptCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        categoryCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        // supplierCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        demandUserId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        demandDesc: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }]
      }
    }
  },
  created() {
    this.init()
    // 获取呆料清单详情
    this.getRespUser = debounce(this.getRespUser, 1000)
    this.getCCEmail = debounce(this.getCCEmail, 1000)
    this.getPurchaseResp = debounce(this.getPurchaseResp, 1000)
    this.getAuditMan = debounce(this.getAuditMan, 1000)
  },
  mounted() {},
  methods: {
    togglePanel(ref) {
      this.$refs[ref].togglePanel()
    },
    selectEvent(item, field, ref) {
      const $pulldown = this.$refs[ref]
      if ($pulldown) {
        this.formData[field] = item.text
        $pulldown.hidePanel()
      }
    },
    init() {
      if (this.$route.query.id) {
        this.headerId = this.$route.query.id
      }
      // 获取头表获取的行数据信息
      if (this.$route.query.id && sessionStorage.getItem('deadMaterialsListInfo')) {
        const formData = JSON.parse(sessionStorage.getItem('deadMaterialsListInfo'))

        const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
        const isSame = userInfo.accountId === formData.createUserId

        this.$set(this.pageConfig[0].grid.editSettings, 'allowEditing', isSame)
        this.$set(this.purchaseAttachmentConfig[0].grid.editSettings, 'allowEditing', isSame)

        // 获取当前状态
        if (isSame && (formData.status === 1 || formData.status === 3 || formData.status === 5)) {
          const tools = [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder ',
              title: this.$t('新增')
            },
            {
              id: 'Delete',
              icon: 'icon_solid_Delete ',
              title: this.$t('删除')
            },
            {
              id: 'Import',
              icon: 'icon_solid_Import ',
              title: this.$t('导入')
            }
          ]
          const purchaseAttachmentTools = ['Add', 'Delete']

          this.purchaseAttachmentConfig[0].toolbar.tools[0] = purchaseAttachmentTools
          this.pageConfig[0].toolbar.tools[0] = tools
        }
        // 判断路由信息的id与头表传递的id是否相同
        if (formData.id === this.headerId) {
          // 审核人
          const auditUser =
            JSON.parse(formData.auditMan).code +
            '-' +
            JSON.parse(formData.auditMan).name +
            '-' +
            JSON.parse(formData.auditMan).departmentCode +
            '-' +
            JSON.parse(formData.auditMan).departmentName
          this.auditManList = [
            {
              employeeName: auditUser
            }
          ]

          // 部门负责人
          const respUserAcct = JSON.parse(formData.respUserAcct)?.map((item) => {
            return (
              item.code + '-' + item.name + '-' + item.departmentCode + '-' + item.departmentName
            )
          })
          this.userArrList = respUserAcct.map((item) => {
            return {
              employeeName: item
            }
          })
          let ccEmail = []
          // 抄送人
          if (formData.ccEmail) {
            ccEmail = JSON.parse(formData.ccEmail)?.map((item) => {
              return (
                item.code + '-' + item.name + '-' + item.departmentCode + '-' + item.departmentName
              )
            })
            this.ccEmailuserArrList = ccEmail.map((item) => {
              return {
                employeeName: item
              }
            })
          }
          let purchaseRespUser = []
          if (formData.purchaseRespUser) {
            // 采购部审核人
            purchaseRespUser = JSON.parse(formData.purchaseRespUser)?.map((item) => {
              return (
                item.code + '-' + item.name + '-' + item.departmentCode + '-' + item.departmentName
              )
            })
            this.purchaseRespList = purchaseRespUser.map((item) => {
              return {
                employeeName: item
              }
            })
          }

          this.formData = {
            ...formData,
            companyName: formData.companyName,
            respUserAcct,
            ccEmail,
            purchaseRespUser,
            auditMan: auditUser
          }
          if (formData.attachmentList) {
            this.$set(this.purchaseAttachmentConfig[0].grid, 'dataSource', formData.attachmentList)
          } else {
            this.purchaseAttachmentConfig[0].grid.dataSource = []
          }
        }
        // this.pageConfig[0].grid.asyncConfig.url =
        //   '/analysis/tenant/idleMaterialVoucher/itemPageQuery'
        // this.pageConfig[0].grid.asyncConfig.defaultRules = [
        //   {
        //     field: 'headerId',
        //     operator: 'equal',
        //     value: this.headerId
        //   }
        // ]
        this.pageConfig[0].grid.asyncConfig.defaultRules[0].value = this.headerId
        this.getIdleMaterialDetail(this.$route.query.id)
      } else if (!this.$route.query.id) {
        const tools = [
          {
            id: 'Add',
            icon: 'icon_solid_Createorder ',
            title: this.$t('新增')
          },
          {
            id: 'Delete',
            icon: 'icon_solid_Delete ',
            title: this.$t('删除')
          },
          {
            id: 'Import',
            icon: 'icon_solid_Import ',
            title: this.$t('导入')
          }
        ]
        const purchaseAttachmentTools = ['Add', 'Delete']

        this.purchaseAttachmentConfig[0].toolbar.tools[0] = purchaseAttachmentTools
        this.pageConfig[0].toolbar.tools[0] = tools
      }
    },

    // 获取呆料清单详情
    getIdleMaterialDetail(id) {
      this.$API.deadMaterials.getIdleMaterialDetail(id).then((res) => {
        const {
          oaTitle,
          eventDesc,
          totalAmt,
          claimTotalAmt,
          code,
          status,
          createUserName,
          createDate
        } = res?.data
        this.formData = {
          ...this.formData,
          oaTitle,
          eventDesc,
          totalAmt,
          claimTotalAmt,
          code,
          status,
          createUserName,
          createDate,
          statusDesc: this.statusList.find((e) => e.code == status)?.label
        }
      })
    },
    // 获取审核人列表
    getAuditMan(val) {
      let params = {
        page: {
          current: 1,
          size: 20
        },
        subjectType: 0
      }
      if (val) {
        params['condition'] = 'or'
        params['rules'] = [
          {
            label: this.$t('账号'),
            field: 'externalCode',
            type: 'string',
            operator: 'contains',
            value: val.text
          },
          {
            label: this.$t('姓名'),
            field: 'employeeName',
            type: 'string',
            operator: 'contains',
            value: val.text
          }
        ]
      }
      this.$API.purChangeRequest.employee(params).then((res) => {
        if (res.code == 200 && res.data.records != null) {
          const auditManList = res.data.records.map((item) => {
            item.employeeName =
              item.externalCode +
              '-' +
              item.employeeName +
              '-' +
              item.departmentCode +
              '-' +
              item.departmentName
            return item
          })
          const newArr = auditManList.concat(this.auditManList)
          let map = new Map()
          for (let item of newArr) {
            map.set(item.externalCode, item)
          }
          this.auditManList = [...map.values()]
          this.noRecordsTemplate = this.$t('没有找到记录')
        }
      })
    },
    // 获取部门负责人列表
    getRespUser(val) {
      let params = {
        page: {
          current: 1,
          size: 20
        },
        subjectType: 0
      }
      if (val) {
        params['condition'] = 'or'
        params['rules'] = [
          {
            label: this.$t('账号'),
            field: 'externalCode',
            type: 'string',
            operator: 'contains',
            value: val.text
          },
          {
            label: this.$t('姓名'),
            field: 'employeeName',
            type: 'string',
            operator: 'contains',
            value: val.text
          }
        ]
      }
      this.$API.purChangeRequest.employee(params).then((res) => {
        if (res.code == 200 && res.data.records != null) {
          const userArrList = res.data.records.map((item) => {
            item.employeeName =
              item.externalCode +
              '-' +
              item.employeeName +
              '-' +
              item.departmentCode +
              '-' +
              item.departmentName
            return item
          })
          const newArr = userArrList.concat(this.userArrList)
          let map = new Map()
          for (let item of newArr) {
            map.set(item.externalCode, item)
          }
          this.userArrList = [...map.values()]
          this.noRecordsTemplate = this.$t('没有找到记录')
        }
      })
    },
    // 获取抄送相关负责人
    getCCEmail(val) {
      let params = {
        page: {
          current: 1,
          size: 20
        },
        subjectType: 0
      }
      if (val) {
        params['condition'] = 'or'
        params['rules'] = [
          {
            label: this.$t('账号'),
            field: 'externalCode',
            type: 'string',
            operator: 'contains',
            value: val.text
          },
          {
            label: this.$t('姓名'),
            field: 'employeeName',
            type: 'string',
            operator: 'contains',
            value: val.text
          }
        ]
      }
      this.$API.purChangeRequest.employee(params).then((res) => {
        if (res.code == 200 && res.data.records != null) {
          const ccEmailuserArrList = res.data.records.map((item) => {
            item.employeeName =
              item.externalCode +
              '-' +
              item.employeeName +
              '-' +
              item.departmentCode +
              '-' +
              item.departmentName
            return item
          })
          const newArr = ccEmailuserArrList.concat(this.ccEmailuserArrList)
          let map = new Map()
          for (let item of newArr) {
            map.set(item.externalCode, item)
          }
          this.ccEmailuserArrList = [...map.values()]
          this.noRecordsTemplate = this.$t('没有找到记录')
        }
      })
    },
    // 获取采购部审核人
    getPurchaseResp(val) {
      let params = {
        page: {
          current: 1,
          size: 20
        },
        subjectType: 0
      }
      if (val) {
        params['condition'] = 'or'
        params['rules'] = [
          {
            label: this.$t('账号'),
            field: 'externalCode',
            type: 'string',
            operator: 'contains',
            value: val.text
          },
          {
            label: this.$t('姓名'),
            field: 'employeeName',
            type: 'string',
            operator: 'contains',
            value: val.text
          }
        ]
      }
      this.$API.purChangeRequest.employee(params).then((res) => {
        if (res.code == 200 && res.data.records != null) {
          const purchaseRespList = res.data.records.map((item) => {
            item.employeeName =
              item.externalCode +
              '-' +
              item.employeeName +
              '-' +
              item.departmentCode +
              '-' +
              item.departmentName
            return item
          })
          const newArr = purchaseRespList.concat(this.purchaseRespList)
          let map = new Map()
          for (let item of newArr) {
            map.set(item.externalCode, item)
          }
          this.purchaseRespList = [...map.values()]
          this.noRecordsTemplate = this.$t('没有找到记录')
        }
      })
    },

    // 保存接口
    saveCompanyInfo(status) {
      console.log('formDataformData', this.formData)
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 如果当前是提交状态的话 ，校验公司
          if (status === 1) {
            const curFactory =
              this.$refs?.templateRef.getCurrentUsefulRef().ejsRef.ej2Instances.currentViewData
            for (let index = 0; index < curFactory.length; index++) {
              const item = curFactory[index]
              if (
                item.isLaunchProcess === 1 &&
                this.deadStockFactory.every((tar) => tar.dictCode !== item.factoryCode)
              ) {
                this.$toast({
                  content: this.$t('第') + Number(index + 1) + this.$t('行所选工厂不符合要求'),
                  type: 'warning'
                })
                return
              }
            }
          }

          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认保存数据？')
            },
            success: () => {
              const _attachmentList =
                this.$refs?.attachmentRef.getCurrentUsefulRef().ejsRef.ej2Instances.currentViewData
              // const _itemList =
              //   this.$refs?.templateRef.getCurrentUsefulRef().ejsRef.ej2Instances.currentViewData

              const formData = cloneDeep(this.formData)

              // 审核人
              formData.auditMan = JSON.stringify({
                name: formData.auditMan.split('-')[1],
                code: formData.auditMan.split('-')[0],
                departmentCode: formData.auditMan.split('-')[2],
                departmentName: formData.auditMan.split('-')[3]
              })

              if (Array.isArray(formData.ccEmail)) {
                // 抄送人
                formData.ccEmail = JSON.stringify(
                  formData?.ccEmail.map((item) => {
                    return {
                      name: item.split('-')[1],
                      code: item.split('-')[0],
                      departmentCode: item.split('-')[2],
                      departmentName: item.split('-')[3]
                    }
                  })
                )
              }
              if (Array.isArray(formData.respUserAcct)) {
                // 部门负责人
                formData.respUserAcct = JSON.stringify(
                  formData?.respUserAcct.map((item) => {
                    return {
                      name: item.split('-')[1],
                      code: item.split('-')[0],
                      departmentCode: item.split('-')[2],
                      departmentName: item.split('-')[3]
                    }
                  })
                )
              }
              if (Array.isArray(formData.purchaseRespUser)) {
                // 采购部审核人
                formData.purchaseRespUser = JSON.stringify(
                  formData?.purchaseRespUser.map((item) => {
                    return {
                      name: item.split('-')[1],
                      code: item.split('-')[0],
                      departmentCode: item.split('-')[2],
                      departmentName: item.split('-')[3]
                    }
                  })
                )
              }
              const params = { ...formData, attachmentList: _attachmentList }
              if (this.headerId) {
                params.id = this.headerId
              }
              params.saveOrSubmit = status
              this.getSaveHeaderApi()(params).then((res) => {
                const { code, data } = res
                if (code === 200) {
                  if (status === 0) {
                    this.$toast({
                      content: this.$t('保存成功'),
                      type: 'success'
                    })
                    this.headerId = data
                    this.getIdleMaterialDetail(this.headerId)
                    this.isReload = false
                    this.pageConfig[0].grid.asyncConfig.url =
                      '/analysis/tenant/idleMaterialVoucher/itemPageQuery'
                    this.pageConfig[0].grid.asyncConfig.defaultRules = [
                      {
                        field: 'headerId',
                        operator: 'equal',
                        value: data
                      }
                    ]
                    setTimeout(() => {
                      this.$refs.templateRef.refreshCurrentGridData()
                    }, 1000)
                    sessionStorage.setItem(
                      'deadMaterialsListInfo',
                      JSON.stringify({ id: data, ...this.formData })
                    )
                    this.$nextTick(() => {
                      this.isReload = true
                    })
                  }
                  if (status === 1) {
                    this.$toast({
                      content: this.$t('保存并提交成功'),
                      type: 'success'
                    })
                    this.$router.go(-1)
                  }
                }
              })
            }
          })
        }
      })
    },
    getSaveHeaderApi() {
      if (this.headerId) {
        return this.$API.deadMaterials.updateHeader
      } else {
        return this.$API.deadMaterials.addHeader
      }
    },
    orgChange(e) {
      const curFactory =
        this.$refs?.templateRef.getCurrentUsefulRef().ejsRef.ej2Instances.currentViewData
      console.log('orgChangeorgChangeorgChange', curFactory)
      const { itemData } = e
      this.formData.companyName = itemData.orgName
      this.formData.companyId = itemData.id
      sessionStorage.setItem('companyId', itemData.id)
      curFactory.forEach((item) => {
        item.companyCode = itemData.id
      })
    },
    auditManChange(e) {
      this.auditManJson = JSON.stringify({
        name: e.itemData.employeeName,
        code: e.itemData.employeeCode
      })
    },
    handleClickToolBar(e) {
      let records = e.data ? [e.data] : e.grid.getSelectedRecords()
      if (records.length <= 0 && (e.toolbar.id === 'Delete' || e.toolbar.id === 'Create')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // if (
      //   !this.headerId &&
      //   (e.toolbar.id === 'Add' || e.toolbar.id === 'Import' || e.toolbar.id === 'Create')
      // ) {
      //   this.$toast({ content: this.$t('请先保存OA审批号及所属公司'), type: 'warning' })
      //   return
      // }
      if (e.toolbar.id === 'Add') {
        if (!this.headerId)
          return this.$toast({ content: this.$t('请先保存基础信息'), type: 'warning' })
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
        return
      }
      if (e.toolbar.id === 'Delete') {
        this.handleDelete(records)
        return
      }
      if (e.toolbar.id === 'Create') {
        this.handleCreate(records)
        return
      }
      if (e.toolbar.id === 'Import') {
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('上传/导入'),
            importApi: this.$API.deadMaterials.importItemExcel,
            downloadTemplateApi: this.$API.deadMaterials.getItemImportTemplate,
            asyncParams: {
              headerId: this.headerId
            }
          },
          success: () => {
            console.log('componentscomponentscomponents', this.$refs.templateRef)
            this.$nextTick(() => {
              this.$refs.templateRef.refreshCurrentGridData()
              this.getIdleMaterialDetail(this.headerId)
            })
          }
        })
        return
      }
    },
    handleDelete(records) {
      const idList = []
      records.forEach((item) => idList.push(item.id))
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.deadMaterials.removeItem(idList).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })

            this.$refs.templateRef.refreshCurrentGridData()
          })
          // this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
        }
      })
    },
    handleCreate(records) {
      const itemIdList = []
      for (let i = 0; i < records.length; i++) {
        const item = records[i]
        if (!item.id) {
          this.$toast({
            content: this.$t('勾选的数据存在未保存的数据，请先保存'),
            type: 'warning'
          })
          return
        }
        itemIdList.push(item.id)
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认创建数据？')
        },
        success: () => {
          const params = {
            createType: 2,
            headerId: this.headerId,
            itemIdList
          }
          this.$API.deadMaterials.createClaimVoucher(params).then(() => {
            this.$toast({
              content: this.$t('创建成功'),
              type: 'success'
            })

            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    actionBegin(args) {
      const { requestType, data } = args
      const formData = JSON.parse(sessionStorage.getItem('deadMaterialsListInfo'))
      if (requestType === 'beginEdit') {
        if (
          !this.isSameUser ||
          (this.$route.query.id &&
            formData.status !== 1 &&
            formData.status !== 3 &&
            formData.status !== 5)
        ) {
          args.cancel = true
        }
      }

      if (requestType === 'save') {
        const validateMap = {
          factoryCode: {
            value: data.factoryCode,
            msg: this.$t('请选择工厂')
          },
          // supplierCode: {
          //   value: data.supplierCode,
          //   msg: this.$t('请选择供应商')
          // },
          materialCode: {
            value: data.materialCode,
            msg: this.$t('请选择物料')
          },
          plannerName: {
            value: data.plannerName,
            msg: this.$t('请选择计划员')
          },
          qtyText: {
            value: data.qtyText,
            msg: this.$t('请输入数量')
          },
          currency: {
            value: data.currency,
            msg: this.$t('请选择币种')
          },
          unitPriceText: {
            value: data.unitPriceText,
            msg: this.$t('请输入单价')
          },
          // actualAmtText: {
          //   value: data.qtyText,
          //   msg: this.$t('请输入实际损失金额')
          // },
          // purchaserPercent: {
          //   value: data.purchaserPercent,
          //   msg: this.$t('请输入TCL承担占比')
          // },
          // supplierPercent: {
          //   value: data.supplierPercent,
          //   msg: this.$t('请输入供方承担占比')
          // },
          costCenter: {
            value: data.costCenter,
            msg: this.$t('请输入成本中心')
          }
        }
        if (data.transitQtyText !== 0) {
          validateMap['supplierCode'] = {
            value: data.supplierCode,
            msg: this.$t('请选择供应商')
          }
        }
        for (const key in validateMap) {
          if (Object.hasOwnProperty.call(validateMap, key)) {
            const element = validateMap[key]
            if (!element.value) {
              this.$toast({ content: element.msg, type: 'warning' })
              args.cancel = true
              break
            }
          }
        }
        if (data.transitQtyText === 0 && !data.supplierCode && data.isLaunchProcess === 1) {
          this.$toast({ content: this.$t('供应商为空的情况下，不允许发起索赔'), type: 'warning' })
          args.cancel = true
        }
      }
      // else if (requestType === 'beginEdit') {
      //   if (args.data.voucherStatus == 2) {
      //     args.cancel = true
      //   }
      //   // else if (args.data.voucherStatus == 1) {
      //   //   this.$set(this.pageConfig[0].grid.columnData.purchaseAmt, 'allowEditing', true)
      //   // }
      // }
    },
    actionComplete(args) {
      const { requestType, data } = args
      if (requestType == 'save') {
        // 调保存接口
        this.save(data)
      }
    },
    save(rowData) {
      const params = { ...rowData }
      if (this.headerId) {
        params.headerId = this.headerId
      }
      this.getSaveItemApi(rowData)(params).then((res) => {
        if (res.code === 200) {
          this.$refs.templateRef.refreshCurrentGridData()
          this.getIdleMaterialDetail(this.headerId)
        }
      })
    },
    getSaveItemApi(data) {
      if (data.id) {
        return this.$API.deadMaterials.updateItem
      } else {
        return this.$API.deadMaterials.addItem
      }
    },
    goBack() {
      this.$router.push({
        name: 'idle-material-claims'
      })
    },
    // 附件上传的按钮事件
    handleClickPurchaseAttachmentToolBar(e) {
      let sltList = e.grid.getSelectedRecords()
      let selectedRowIndexes = e.grid.getSelectedRowIndexes()
      if (
        (!sltList || sltList.length <= 0) &&
        (e.toolbar.id == 'Edit' || e.toolbar.id == 'Delete')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      console.log('handleClickPurchaseAttachmentToolBar=', e)
      if (e.toolbar.id == 'Add') {
        this.$dialog({
          modal: () => import('./coms/uploadDialog.vue'),
          data: {
            fileData: [],
            isView: false, // 是否为预览
            required: false, // 是否必须
            title: this.$t('新增采方附件')
          },
          success: (res) => {
            this.handleUploadFiles(res)
          }
        })
      } else if (e.toolbar.id == 'Edit') {
        if (sltList.length > 1) {
          this.$toast({
            content: this.$t('不能同时编辑多行'),
            type: 'warning'
          })
          return
        }
        this.$dialog({
          modal: () => import('./coms/uploadDialog.vue'),
          data: {
            title: this.$t('编辑采方附件'),
            isEdit: true,
            info: {
              ...sltList[0],
              fileName: sltList[0].attachmentName,
              fileSize: sltList[0].attachmentSize,
              url: sltList[0].attachmentUrl
            },
            index: selectedRowIndexes[0]
          },
          success: (data) => {
            let _tempData = cloneDeep(this.purchaseAttachmentConfig[0].grid.dataSource)
            _tempData[selectedRowIndexes[0]] = data
            this.$set(this.purchaseAttachmentConfig[0].grid, 'dataSource', _tempData)

            this.$refs.attachmentRef.refreshCurrentGridData()
          }
        })
      } else if (e.toolbar.id == 'Delete') {
        let _tempData = cloneDeep(this.purchaseAttachmentConfig[0].grid.dataSource)
        let _fileIds = sltList.map((x) => x.id)
        let _newData = _tempData.filter((element) => !_fileIds.includes(element.id))
        this.$set(this.purchaseAttachmentConfig[0].grid, 'dataSource', _newData)
      }
    },
    handleUploadFiles(data) {
      let _tempData = {
        ...data,
        attachmentId: data.id,
        attachmentName: data.fileName,
        attachmentSize: data.fileSize,
        attachmentUrl: data.url,
        createUserName: data.uploadUserName,
        createTime: data.uploadTime
      }
      this.purchaseAttachmentConfig[0].grid.dataSource.push(_tempData)
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'download') {
        this.handleDownloadFile(data)
      }
    },
    // 下载文件
    handleDownloadFile(data) {
      this.$API.fileService
        .downloadPublicFile({
          id: data.attachmentId
        })
        .then((res) => {
          download({
            fileName: data.attachmentName,
            blob: res.data
          })
        })
    },
    handleClickCellTitle(e) {
      if (e.field == 'attachmentName') {
        this.preview(e.data)
      }
    },
    preview(item) {
      let params = {
        id: item.attachmentId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  background-color: #fff;
  padding: 10px 15px;
}
.btnGroups {
  display: flex;
  justify-content: flex-end;
}
.form-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
/deep/ .mt-form-item {
  margin-bottom: 8px;
}
/deep/ .mt-rich-text-editor {
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-rte-content {
    height: 170px !important;
    padding: 0px 7px;
    em {
      font: revert;
    }
    ol,
    ul {
      list-style: revert;
    }
  }
}
.rinput {
  margin-left: 10px;
  // font-size: 0.18rem;
  height: 1.95rem;
  // width: 5.25rem;
  // position: absolute;
  // left: 0px;
}
.list-item2 {
  padding-left: 10px;
  line-height: 33px;
}
.list-item2:hover {
  background-color: #eeeeee;
}
.my-dropdown2 {
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
::v-deep .vxe-pulldown {
  width: 100%;
  .vxe-input {
    width: 100%;
  }
}
::v-deep .vxe-input--inner {
  width: 100%;
  height: 100%;
  border-radius: 0px;
  outline: 0;
  margin: 0;
  font-family: inherit;
  line-height: inherit;
  padding: 0 0.6em;
  color: #606266;
  font-size: 12px;
  border: 1px solid rgba(0, 0, 0, 0.6);
  border-top: none;
  border-left: none;
  border-right: none;
  background-color: #fff;
  box-shadow: none;
  height: 18px;
}
::v-deep .mt-data-grid {
  .e-grid {
    .e-gridcontent {
      min-height: 100px;
    }
  }
}
</style>
