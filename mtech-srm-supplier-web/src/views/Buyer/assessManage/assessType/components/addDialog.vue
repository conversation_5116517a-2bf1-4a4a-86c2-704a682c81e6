<template>
  <mt-dialog ref="addDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('考核类型名称')"
          label-style="top"
          prop="typeCode"
        >
          <mt-select
            v-model="formInfo.typeCode"
            :data-source="typeCodeList"
            :placeholder="$t('请选择')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            @change="typeCodeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('所属公司')" label-style="top" prop="company">
          <mt-multi-select
            v-model="formInfo.company"
            :data-source="companyList"
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            @change="companyChange"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('是否允许申诉')"
          label-style="top"
          prop="allowAppeal"
        >
          <mt-radio
            v-model="formInfo.allowAppeal"
            :data-source="radioData"
            @change="allowAppealChange"
          ></mt-radio>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('是否允许冲销')"
          label-style="top"
          prop="allowReverse"
        >
          <mt-radio v-model="formInfo.allowReverse" :data-source="radioData"></mt-radio>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('是否允许预扣')"
          label-style="top"
          prop="allowWithhold"
        >
          <mt-radio v-model="formInfo.allowWithhold" :data-source="radioData"></mt-radio>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('是否超期自动确认')"
          label-style="top"
          prop="autoEnsure"
        >
          <mt-radio
            v-model="formInfo.autoEnsure"
            :data-source="radioData"
            @change="autoEnsureChange"
          ></mt-radio>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('申诉处理时长限制')"
          prop="appealDays"
          label-style="top"
          v-if="formInfo.allowAppeal == '1'"
        >
          <mt-input-number
            v-model="formInfo.appealDays"
            :min="0.1"
            :precision="1"
            :placeholder="$t('单位：天')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('默认反馈时长')"
          prop="feedbackDays"
          label-style="top"
          v-if="formInfo.autoEnsure == '1'"
        >
          <mt-input-number
            v-model="formInfo.feedbackDays"
            :min="0.1"
            :precision="1"
            :placeholder="$t('单位：天')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item class="form-item full-width" :label="$t('备注：')" label-style="top">
          <mt-input
            v-model="formInfo.remark"
            :multiline="true"
            :rows="2"
            maxlength="200"
            float-label-type="Never"
            :placeholder="$t('字数不超过200字')"
            width="820"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import MtRadio from '@mtech-ui/radio'

export default {
  components: { MtRadio },
  data() {
    return {
      typeCodeList: [],
      radioData: [
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '0'
        }
      ],
      formInfo: {
        allowAppeal: '0',
        allowReverse: '0',
        autoEnsure: '0',
        allowWithhold: '0',
        bizId: 0,
        bizType: 2,
        defaultValue: '',
        fieldCode: null,
        fieldId: null,
        fieldName: null,
        company: '',
        typeCode: '',
        source: '1',
        companyList: []
      },
      companyList: [],
      rules: {
        typeCode: [
          { required: true, message: this.$t('请输入'), trigger: 'blur' },
          { whitespace: true, message: this.$t('请输入'), trigger: 'blur' }
        ],
        company: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        feedbackDays: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        appealDays: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    async initData() {
      console.log('initData=', this.info)
      await this.getOrgs()
      await this.getClaimCodeLiist()
      if (this.isEdit) {
        this.$API.assessManage
          .detailClaimType({
            id: this.info.id
          })
          .then((res) => {
            if (res.code == 200) {
              this.$nextTick(() => {
                this.formInfo = {
                  ...res.data,
                  allowAppeal: res.data.allowAppeal ? '1' : '0',
                  allowReverse: res.data.allowReverse ? '1' : '0',
                  allowWithhold: res.data.allowWithhold ? '1' : '0',
                  autoEnsure: res.data.autoEnsure ? '1' : '0',
                  company: res.data.companyList.map((e) => e.companyCode)
                }
              })
            }
          })
      }
      this.show()
    },
    //获取公司下级工厂
    async getOrgs() {
      await this.$API.masterData
        .findSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: []
        })
        .then((res) => {
          this.companyList = res.data
        })
    },
    getClaimCodeLiist() {
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'claimType'
        })
        .then((res) => {
          this.typeCodeList = res.data
        })
    },
    show() {
      this.$refs['addDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['addDialog'].ejsRef.hide()
    },
    companyChange(e) {
      this.formInfo.companyList.length = 0
      if (e.value.length > 0) {
        this.companyList.forEach((e) => {
          if (this.formInfo.company.includes(e.orgCode)) {
            this.formInfo.companyList.push({
              companyCode: e.orgCode,
              companyName: e.orgName,
              companyId: e.id
            })
          }
        })
        console.log('companyChange=', e, this.formInfo.companyList)
      }
    },
    typeCodeChange(e) {
      if (e?.itemData) {
        this.formInfo.typeName = e.itemData.itemName
      }
    },
    allowAppealChange(e) {
      if (e == '0') {
        this.formInfo.appealDays = 0
      }
    },
    autoEnsureChange(e) {
      if (e == '0') {
        this.formInfo.feedbackDays = 0
      }
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$API.assessManage
            .saveClaimType({
              ...this.formInfo,
              allowAppeal: this.formInfo.allowAppeal == '1' ? true : false,
              allowReverse: this.formInfo.allowReverse == '1' ? true : false,
              allowWithhold: this.formInfo.allowWithhold == '1' ? true : false,
              autoEnsure: this.formInfo.autoEnsure == '1' ? true : false
            })
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0 20px;
  font-size: 16px;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
    }
    .full-width {
      width: 100%;
    }
  }
}
</style>
