<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="history">
      <ul>
        <li v-for="item in dataSource" :key="item.id">
          <span class="name">{{ $t('姓名：') }}{{ item.updateUserName }}</span>
          <div>
            <p>{{ $t('从') }}{{ item.lastMaterialName }}&emsp;{{ item.lastCategoryName }}</p>
            <p>{{ $t('到') }}{{ item.materialName }}&emsp;{{ item.categoryName }}</p>
          </div>
          <span class="data">{{ $t('日期：') }}{{ item.modifyDate }}</span>
          <!-- <span class="version" @click="clickHistory(item)">历史版本</span> -->
        </li>
      </ul>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      dataSource: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    listId() {
      return this.modalData.title
    }
  },
  mounted() {
    this.show()
    this.f()
  },
  methods: {
    f() {
      this.$loading()
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      this.$emit('confirm-function')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.history {
  width: 100%;
  box-sizing: border-box;
  padding: 20px;
  ul {
    width: 100%;
    min-width: 860px;
    li {
      display: flex;
      width: 100%;
      height: 50px;
      justify-content: space-between;
      position: relative;
      border-left: 1px solid;
      .name {
        margin-left: 20px;
        display: block;
        width: 15%;
      }
      div {
        width: 55%;
        p {
          width: 100%;
          word-wrap: break-word;
          word-break: break-all;
        }
      }
      .data {
        display: block;
        width: 30%;
      }
      // .version {
      //   display: block;
      //   width: 60px;
      //   color: #00469c;
      //   text-decoration: underline;
      // }
      // .version:hover {
      //   font-size: 15px;
      // }
    }
    li:before {
      display: block;
      content: '';
      width: 15px;
      height: 15px;
      border: 3px solid #139dde;
      background: #fff;
      border-radius: 50%;
      margin-left: -8px;
    }
    li:nth-last-child(1) {
      border: none;
    }
    li:hover {
      cursor: pointer;
    }
  }
}
</style>
