<template>
  <div class="lifeCycle-container">
    <!-- 顶部信息 start -->
    <div class="lifeCycle-top">
      <div class="lifeCycle-left">
        <div>
          <template v-if="logoUrl">
            <img :src="logoUrl" alt="" />
          </template>
          <template v-else>
            <i class="mt-icons mt-icon-icon_card_company"></i>
          </template>
        </div>
        <div style="margin-left: 16px">
          <p class="leftCode">
            <span>{{ $t('客户编码：') }}{{ headerInfo.customerEnterpriseCode }}</span>
            <span style="margin-left: 152px"
              >{{ $t('客户名称：') }}{{ headerInfo.customerName }}</span
            >
          </p>
          <p class="leftTitle">
            <span>{{ $t('供应商编号-SRM：') }}{{ headerInfo.supplierInternalCode }}</span>
            <span style="margin-left: 80px"
              >{{ $t('供应商编号-SAP：') }}{{ headerInfo.supplierCode }}</span
            >
          </p>
          <p class="leftTitle">
            <span
              >{{ $t('创建时间：') }}
              <!-- {{checkDate(headerInfo.createTime)}} -->
              {{ headerInfo.createTime }}
            </span>
          </p>
        </div>
      </div>
      <div style="display: flex">
        <div class="btnClass" @click="onBack">{{ $t('返回') }}</div>
        <div class="btnClass" style="margin-left: 20px" @click="infoChange">
          {{ $t('发起信息变更') }}
        </div>
      </div>
    </div>

    <mt-tabs
      :e-tab="false"
      overflow-mode="Popup"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <template v-if="selectIndex == 0">
      <base-info
        :partner-archive-id="partnerArchiveId"
        :org-id="orgId"
        :supplier-enterprise-id="supplierEnterpriseId"
        :partner-relation-code="partnerRelationCode"
      ></base-info>
    </template>
    <template v-else-if="selectIndex == 1">
      <business-info
        :partner-archive-id="partnerArchiveId"
        :org-id="orgId"
        :supplier-enterprise-id="supplierEnterpriseId"
        :partner-relation-code="partnerRelationCode"
      ></business-info>
    </template>
    <template v-else-if="selectIndex == 2">
      <relevant-certificate
        :partner-archive-id="partnerArchiveId"
        :org-id="orgId"
        :supplier-enterprise-id="supplierEnterpriseId"
        :partner-relation-code="partnerRelationCode"
      ></relevant-certificate>
    </template>
    <template v-else-if="selectIndex == 3">
      <personnel-equipment
        :partner-archive-id="partnerArchiveId"
        :org-id="orgId"
        :supplier-enterprise-id="supplierEnterpriseId"
        :partner-relation-code="partnerRelationCode"
      ></personnel-equipment>
    </template>
    <template v-else-if="selectIndex == 4">
      <delivery-cycle
        :partner-archive-id="partnerArchiveId"
        :org-id="orgId"
        :supplier-enterprise-id="supplierEnterpriseId"
        :partner-relation-code="partnerRelationCode"
      ></delivery-cycle>
    </template>
  </div>
</template>

<script>
import baseInfo from '../components/baseInfo.vue'
import businessInfo from '../components/businessInfo.vue'
import relevantCertificate from '../components/relevantCertificate.vue'
import personnelEquipment from '../components/personnelEquipment.vue'
import deliveryCycle from '../components/deliveryCycle.vue'
export default {
  components: {
    baseInfo,
    businessInfo,
    relevantCertificate,
    personnelEquipment,
    deliveryCycle
  },
  data() {
    return {
      templateConfig: {
        grid: {
          columnData: []
        }
      },
      dataSource: [
        { title: this.$t('基本信息') },
        { title: this.$t('财务信息') },
        { title: this.$t('相关证书') },
        { title: this.$t('人员及设备') },
        { title: this.$t('交货周期') }
        // { title: this.$t("调查表信息") },
        // { title: this.$t("历史版本") },
      ],
      partnerArchiveId: '',
      orgId: '',
      orgName: '',
      supplierEnterpriseId: '',
      partnerRelationCode: '',
      selectIndex: 0,
      headerInfo: {},
      organizationId: 0,

      logoUrl: '',
      status: ''
    }
  },
  created() {
    // 获取参数
    let partnerArchiveId = this.$route.query.partnerArchiveId
    let orgId = this.$route.query.orgId
    let orgName = unescape(this.$route.query.orgName)
    let orgCode = this.$route.query.orgCode
    let supplierEnterpriseId = this.$route.query.supplierEnterpriseId
    let organizationId = this.$route.query.organizationId || ''
    let partnerRelationCode = this.$route.query.partnerRelationCode || ''
    let status = this.$route.query.status

    this.partnerArchiveId = partnerArchiveId
    this.partnerRelationCode = partnerRelationCode
    this.orgId = orgId
    this.orgName = orgName
    this.orgCode = orgCode
    this.supplierEnterpriseId = supplierEnterpriseId
    this.organizationId = organizationId // 供应商资源库跳转带公司id
    this.status = status
    // 详情
    this.getAccessDetail(partnerArchiveId)
  },
  methods: {
    checkDate(e) {
      if (!!e) {
        if (e == 0) {
          return (e = '')
        } else if (typeof e == 'object') {
          return this.$utils.formateTime(e, 'yyyy-MM-dd hh:mm:ss')
        } else if (typeof e == 'string') {
          let val = parseInt(e)
          return this.$utils.formateTime(val, 'yyyy-MM-dd hh:mm:ss')
        } else {
          return e
        }
      } else {
        return e
      }
    },
    // 发起信息变更：跳转到信息变更页面
    infoChange() {
      // 注册/潜在/合格状态的供应商可以变更
      if (this.status == 1 || this.status == 2 || this.status == 10) {
        this.$router.push({
          name: 'infoChangeSupDetail',
          query: {
            type: 'archive',
            partnerArchiveId: this.partnerArchiveId,
            orgId: this.orgId,
            orgName: escape(this.orgName),
            orgCode: this.orgCode,
            supplierEnterpriseId: this.supplierEnterpriseId,
            partnerRelationCode: this.partnerRelationCode,
            status: this.status,
            customerEnterpriseId: this.headerInfo.customerEnterpriseId,
            customerEnterpriseCode: this.headerInfo.customerEnterpriseCode
          }
        })
      } else {
        this.$toast({
          content: this.$t('此状态不能发起信息变更'),
          type: 'warning'
        })
      }
    },
    // 返回
    onBack() {
      this.$router.go(-1)
    },

    // tab页点击
    handleSelectTab(index) {
      this.selectIndex = index
    },

    // 获取任务详情
    getAccessDetail(id) {
      this.$loading()
      this.$API.supplierProfile
        .accessDetail({ id })
        .then((res) => {
          this.$hloading()
          this.headerInfo = res.data
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.fbox {
  display: flex;
  align-items: stretch;
}

.flex1 {
  flex: 1;
}

.epls {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.lifeCycle-container {
  min-width: 1200px;
  overflow-x: scroll;
  background: white;

  .lifeCycle-top {
    display: flex;
    justify-content: space-between;
    height: 160px;
    padding: 30px;
    .lifeCycle-left {
      display: flex;
      color: rgba(41, 41, 41, 1);
      .leftTitle {
        font-size: 18px;
        margin-top: 15px;
      }
      .leftCode {
        font-size: 18px;
        margin-top: 10px;
      }
      img {
        width: 80px;
        height: 80px;
      }
      i {
        font-size: 60px;
        color: #00469c;
      }
    }
    .btnClass {
      height: 30px;
      line-height: 30px;
      padding: 0 10px;
      font-size: 14px;
      font-weight: 600;
      color: rgba(0, 70, 156, 1);
      cursor: pointer;
    }
  }
  .header-status {
    padding: 30px 40px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .cp-name-line {
      white-space: nowrap;

      .cp-name {
        img {
          display: inline-block;
          width: 30px;
          height: 30px;
          margin-right: 10px;
          vertical-align: middle;
        }

        i {
          display: inline-block;
          width: 30px;
          height: 30px;
          font-size: 30px;
          margin-right: 10px;
          color: #00469c;
          vertical-align: middle;
        }

        span {
          display: inline-block;
          height: 30px;
          line-height: 30px;
          font-size: 24px;
          font-weight: 600;
          color: rgba(41, 41, 41, 1);
          vertical-align: middle;
        }
      }

      .cp-tips {
        margin-left: 40px;
        flex-wrap: wrap;

        .tip-item {
          height: 30px;
          line-height: 30px;
          padding: 0 10px;
          font-size: 16px;
          font-weight: 500;
          color: rgba(51, 166, 23, 1);
          background: rgba(51, 166, 23, 0.12);
          border-radius: 2px;
          margin-right: 10px;
          position: relative;
          user-select: none;
          max-width: 100px;
          margin-bottom: 10px;

          .txt-wrap {
            max-width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .close-btn {
            display: none;
          }

          &:hover .close-btn {
            display: block;
            width: 12px;
            height: 12px;
            font-size: 12px;
            position: absolute;
            right: -6px;
            top: -6px;
            color: #9baac1;
            line-height: 12px;
            cursor: pointer;
          }

          i.mt-icon-plus {
            line-height: 30px;
            font-size: 14px;
            cursor: pointer;
          }
        }

        .edit-tips {
          line-height: 30px;
          font-size: 12px;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
      }

      .cp-select {
        width: 410px;
        height: 50px;

        .right-btn {
          font-size: 14px;
          line-height: 40px;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
          font-weight: 600;
        }

        .right-btn:nth-child(2) {
          margin-left: 50px;
        }

        .right-btn:nth-child(3) {
          margin-left: 40px;
        }
      }
    }

    .rate-line {
      margin-top: -8px;

      .rate-item {
        margin-right: 30px;

        .rate-name {
          font-size: 12px;
          line-height: 12px;
          color: rgba(41, 41, 41, 1);
        }

        .rate-score {
          margin-left: 5px;
          width: 70px;
          height: 12px;
          position: relative;

          .bg-width {
            width: 100%;
            height: 12px;
            position: absolute;
            left: 0;
            top: 0;
            background: #00469c;
          }

          .yellow-bg {
            background: #eda133;
          }

          .star-bg {
            width: 14px;
            background: rgba(203, 203, 203, 0.4) url(../../../../assets/star.png) 0 0 no-repeat;
            background-size: 12px 12px;
            position: relative;
            z-index: 1;

            &::after {
              content: ' ';
              width: 2px;
              height: 12px;
              position: absolute;
              right: 0;
              top: 0;
              background: #fff;
            }
          }
        }

        .rate-num {
          margin-left: 5px;
          font-size: 12px;
          font-weight: bold;
          color: rgba(0, 70, 156, 1);
        }

        .yellow-num {
          color: #eda133;
        }
      }
    }

    .supplier-info {
      padding: 0 5%;
      height: 100px;
      margin-top: 20px;
      text-align: center;

      .info-item {
        position: relative;
        padding: 20px 0px;
        display: flex;
        justify-content: space-between;
        flex-direction: column;

        .mian-title {
          height: 24px;
          line-height: 24px;
          font-size: 24px;
          font-weight: 600;
          color: rgba(138, 204, 64, 1);
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          position: relative;

          span {
            font-size: 24px;
          }
        }

        .green {
          color: #8acc40;
        }

        .orange {
          color: #eda133;
        }

        .blue {
          color: #00469c;
        }

        .red {
          color: #ed5633;
        }

        .sub-title {
          height: 16px;
          font-size: 16px;
          font-weight: 600;
          color: rgba(41, 41, 41, 1);
        }

        .risk {
          justify-content: center;

          i {
            font-size: 18px;
            color: #4d5b6f;
            margin-left: 10px;
            cursor: pointer;
          }

          .pop-icon {
            position: relative;

            .pop-box {
              box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
              display: none;
              position: absolute;
              z-index: 9;
              width: 500px;
              height: 72px;
              background: rgba(255, 255, 255, 1);
              border-radius: 2px;
              padding: 14px 18px 10px;
              top: 16px;
              left: -250px;
              transform: translate3d(10px, 0, 0);

              .it-list {
                width: 30px;
                text-align: center;
                margin-right: 1px;

                .tp-txt {
                  height: 12px;
                  font-size: 12px;
                  color: rgba(41, 41, 41, 1);
                  margin-bottom: 4px;
                }

                .bt-color {
                  width: 30px;
                  height: 10px;
                  border-radius: 2px;
                }
              }

              .tip-lf {
                height: 12px;
                justify-content: space-between;
                font-size: 12px;
                color: rgba(41, 41, 41, 1);
                margin-top: 10px;
              }
            }
          }

          .pop-icon:hover {
            .pop-box {
              display: block;
            }
          }
        }

        .bt-tips {
          width: 100%;
          text-align: center;
          font-size: 12px;
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;

          span:nth-child(1) {
            color: #9a9a9a;
          }

          span:nth-child(2) {
            color: #00469c;
            display: inline-block;
            margin-left: 10px;
            cursor: pointer;
          }
        }

        &::after {
          content: ' ';
          display: inline-block;
          position: absolute;
          width: 1px;
          height: 60px;
          background: #dddddd;
          right: 0;
          top: 20px;
        }
      }

      .info-item:last-child {
        &::after {
          display: none;
        }
      }
    }
  }
}
</style>
