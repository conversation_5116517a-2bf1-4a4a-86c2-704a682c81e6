<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="dimensionName" :label="$t('维度名称：')">
          <mt-input
            v-model="formObject.dimensionName"
            float-label-type="Never"
            :placeholder="$t('请输入维度名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('状态：')">
          <mt-switch
            v-model="formObject.statusTag"
            :on-label="$t('启用')"
            :off-label="$t('停用')"
          ></mt-switch>
        </mt-form-item>
        <mt-form-item :label="$t('备注：')" class="process-desc">
          <mt-input
            :multiline="true"
            :rows="3"
            type="text"
            maxlength="200"
            float-label-type="Never"
            v-model="formObject.remark"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import MtSwitch from '@mtech-ui/switch'

export default {
  components: { MtSwitch },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        id: null, //配置Id
        enabled: 0, //是否启用 0: "启用", 1: "停用"
        statusTag: true,
        dimensionName: null, //名称
        remark: null //备注
      },
      formRules: {
        dimensionName: [{ required: true, message: this.$t('请输入维度名称'), trigger: 'blur' }]
      },
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      let _data = { ...this.modalData.data }
      this.formObject = {
        id: _data.id, //配置Id
        enabled: _data.enabled, //是否启用 0: "启用", 1: "停用"
        statusTag: _data.enabled === 0,
        dimensionName: _data.dimensionName, //名称
        remark: _data.remark //备注
      }
    }
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.formObject))
          params.enabled = params.statusTag ? 0 : 1
          delete params.statusTag
          if (this.editStatus) {
            this.$API.performanceScoreSetting.editDimension(params).then((res) => {
              if (res.code == 200) {
                this.$emit('confirm-function')
              }
            })
          } else {
            delete params.id
            this.$API.performanceScoreSetting.addDimension(params).then((res) => {
              if (res.code == 200) {
                this.$emit('confirm-function')
              }
            })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .process-desc {
    width: 820px !important;
  }
}
</style>
