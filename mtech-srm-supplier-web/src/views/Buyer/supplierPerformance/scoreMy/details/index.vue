<template>
  <!-- 评分计划详情页面 -->
  <div class="score-setting">
    <div class="top">
      <div class="left">
        <div class="title">{{ formObject.planName || '--' }}</div>
        <div class="form">
          <div class="form_div">{{ $t('考核计划编码：') }}{{ formObject.planCode }}</div>
          <div class="form_div">
            {{ $t('创建人：') }}{{ formObject.createUserName }} {{ formObject.createDate }}
          </div>
          <div class="form_div">{{ $t('绩效模板：') }}{{ formObject.templateName }}</div>
          <div class="form_div">{{ $t('绩效模板类型：') }}{{ formObject.templateTypeName }}</div>
        </div>
        <div class="data">
          <div class="form_div">{{ $t('考评周期：') }}{{ map[formObject.period] }}</div>
          <div class="form_div">{{ $t('期号：') }}{{ formObject.periodNo }}</div>
          <div class="form_div">
            {{ $t('发布组织：') }}{{ formObject.orgName }}<span>{{ formObject.orgId }}</span>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="right_but" @click="back(0)">{{ $t('返回') }}</div>
        <div class="right_but" @click="back(1)">{{ $t('保存') }}</div>
        <div class="right_but" @click="back(2)">{{ $t('保存并提交') }}</div>
      </div>
    </div>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @change="change"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
let defaultRules = []
const page = {
  current: 1,
  size: 200000
}
export default {
  data() {
    return {
      map: {
        0: this.$t('年度'),
        1: this.$t('半年度'),
        2: this.$t('季度'),
        3: this.$t('月度'),
        4: this.$t('单日')
      },
      pageConfig: [],
      formObject: {},
      arr: [],
      id: null,
      index: 0,
      indexResponseList: []
    }
  },
  created() {
    this.id = this.$route.query.id
    this.index = Number(this.$route.query.index) // 0可编辑 1不可编辑
    this.pageConfig = pageConfig(this.id, this.index)
    defaultRules = [
      {
        operator: 'equal',
        field: 'instanceId',
        type: 'Long',
        value: this.id,
        label: this.$t('实例id')
      }
    ]
    this.init()
  },
  methods: {
    handleClickToolBar(e) {
      console.log('toolbar:', e)
      let { rules, toolbar } = e
      // 刷新 或者重置
      if (
        !!toolbar &&
        !!toolbar.id &&
        (toolbar.id === 'refreshDataByLocal' || toolbar.id === 'resetDataByLocal')
      ) {
        this.getIndexResponseList({
          defaultRules,
          page,
          rules: []
        })
        return
      }
      if (!!rules && rules.rules) {
        this.getIndexResponseList({
          defaultRules,
          page,
          rules: rules.rules
        })
        return
      }
      let _selectRecords = e.gridRef.getMtechGridRecords()
      let _selectIds = []
      if (_selectRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else {
        _selectRecords.map((item) => {
          _selectIds.push(item.id)
        })
      }
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/supplierPerformance/scoreSetting/dimension/dimensionDialog" */ './components/ScoreDialog.vue'
          ),
        data: {
          title: this.$t('批量打分')
        },
        success: (res) => {
          this.indexResponseList.forEach((ele) => {
            if (
              res.value >= ele.minScore &&
              res.value <= ele.maxScore &&
              _selectIds.includes(ele.id)
            ) {
              ele['score'] = res.value
            }
          })
          this.pageConfig[0].grid.dataSource = this.indexResponseList
        }
      })
    },
    init() {
      this.$API.performanceScoreMy.myDetail({ instanceId: this.id }).then((res) => {
        this.$set(this, 'formObject', res.data)
      })

      this.getIndexResponseList({
        defaultRules,
        page,
        rules: []
      })
    },
    // 获取底部列表的分页接口
    getIndexResponseList(query) {
      this.$loading()
      this.$API.performanceScoreMy
        .indexResponseList(query)
        .then((result) => {
          this.$hloading()
          let {
            code,
            data: { records }
          } = result
          if (code === 200 && !!records && records.length > 0) {
            this.indexResponseList = records
          }
          this.$set((this.pageConfig[0].grid.dataSource = this.indexResponseList))
        })
        .catch(() => {
          this.$hloading()
        })
    },
    back(value) {
      if (value) {
        let _selectIds = []
        let isHasZero = false
        this.indexResponseList.map((item) => {
          if (!item.score || item.score === '0') {
            isHasZero = true
          }
          _selectIds.push({
            indexId: item.id,
            score: Number(item.score) // !item.score || item.score === "0" ? null : Number(item.score), // null 得话 上传报错
          })
        })
        let str = 'myDoscore'
        let toastTxt = this.$t('保存成功') + '，' + this.$t('即将跳转我的评分页面')
        if (value == 2) {
          str = 'myDoscoreAndSubmit'
          toastTxt = this.$t('提交成功') + '，' + this.$t('即将跳转我的评分页面')
        }

        // 如果有0值 弹框提示
        if (isHasZero) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t(`发现存在零值，确认继续保存？`)
            },
            success: () => {
              this.queryUpData(str, _selectIds, toastTxt)
            }
          })
        } else {
          this.queryUpData(str, _selectIds, toastTxt)
        }
      } else {
        this.$router.go(-1) //返回上一层
      }
    },

    // 上传信息
    queryUpData(str, _selectIds, toastTxt) {
      this.$API.performanceScoreMy[str](_selectIds).then(() => {
        this.$toast({
          content: toastTxt,
          type: 'success'
        })
        // 测试要求，保存或者保存并提交都跳转页面
        setTimeout(() => {
          this.$router.go(-1)
        }, 600)
        // if (value === 2) {
        // }
        // 重置
        this.getIndexResponseList({
          defaultRules,
          page,
          rules: []
        })
      })
    },

    change(e) {
      console.log(1, e)
      this.indexResponseList.forEach((ele) => {
        if (ele.id == e.id) {
          // 设置得分数 再范围之内渲染 不在范围之内 重置
          if (e.score >= ele.minScore && e.score <= ele.maxScore) {
            ele['score'] = e.score
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
  width: 100%;
  background: white;
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 30px 30px 20px 30px;
    margin-top: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid #e8e8e8ff;
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    .right_but {
      display: inline-block;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #00469cff;
      margin: 0 10px;
    }
    .left {
      color: #292929;
      .title {
        font-size: 20px;
        font-weight: 600;
      }
      .form {
        padding: 10px 0;
        font-size: 12px;
        font-weight: normal;
      }
      .form_div {
        display: inline-block;
        padding-right: 20px;
      }
      .data {
        padding: 10px 0;
        font-size: 14px;
        font-weight: 600;
        span {
          margin-left: 3px;
          color: #00469c;
        }
      }
    }
  }
  /deep/ .mt-data-grid {
    .status-label {
      font-size: 12px;
      padding: 4px;
      border-radius: 2px;
      &.status-disable {
        color: #9baac1;
        background: rgba(155, 170, 193, 0.1);
      }
      &.status-enable {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
  }
}
</style>
