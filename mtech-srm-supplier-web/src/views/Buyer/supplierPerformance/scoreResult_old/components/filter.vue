<template>
  <!-- 顶部-公用数据过滤 -->
  <div v-if="companyTreeFields.dataSource.length" class="filter-panel">
    <mt-DropDownTree
      class="filter-item"
      :id="'org_' + new Date().getTime()"
      ref="ownerOrgNameRef"
      :placeholder="$t('请选择所属组织')"
      v-model="ownerOrg.ownerOrgId"
      :fields="companyTreeFields"
      :show-clear-button="false"
      @select="selectOwnerOrg"
    ></mt-DropDownTree>

    <mt-select
      ref="planRef"
      class="filter-item"
      v-model="planData.id"
      float-label-type="Never"
      :data-source="performancePlanList"
      :fields="{ text: 'planName', value: 'id' }"
      :placeholder="$t('请选择考评计划')"
      @change="changePlanId"
    ></mt-select>

    <span class="filter-item">{{ $t('参评供应商数：') }}{{ filterParams.supplierCount || 0 }}</span>
    <span class="filter-item"
      >{{ $t('评分问卷提交数：') }}{{ filterParams.instanceCount || 0 }}</span
    >
  </div>
</template>

<script>
import MixIn from '../config/mixin'

export default {
  mixins: [MixIn],
  props: {
    filterParams: {
      type: Object,
      default: () => {
        return {
          orgId: null,
          planId: null,
          supplierCount: 0,
          instanceCount: 0
        }
      }
    }
  },
  data() {
    return {
      ownerOrg: { ownerOrgId: null, ownerOrgName: null },
      planData: { id: null },
      companyTreeFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      performancePlanList: [], //评分计划列表
      companyTreeList: [], //当前组织列表--Tree
      flatCompanyTreeList: [] //当前组织列表--Array
    }
  },
  mounted() {
    this.getCompanyTree()
    this.getPlanList()
  },
  methods: {
    getPlanList() {
      //评分计划-列表
      this.$API.performanceScoreSetting
        .getPlanList({
          condition: '',
          defaultRules: [
            {
              condition: 'and',
              field: 'enabled',
              operator: 'equal',
              type: 'string',
              value: 0 //设置数据
            }
          ],
          page: {
            current: 1,
            size: 1000
          },
          pageFlag: false
        })
        .then((res) => {
          this.performancePlanList = res.data.records
        })
    },
    getCompanyTree() {
      this.$API.performanceScoreSetting
        .getStatedLimitTree({
          orgLevelCode: 'ORG02',
          orgType: 'ORG001PRO'
        })
        .then((res) => {
          let _data = []
          if (res && res.data && Array.isArray(res.data)) {
            _data = res.data
          }
          this.companyTreeList = _data
          this.flatCompanyTreeList = this.flatTreeData(_data)
          this.companyTreeFields.dataSource = []
          this.$nextTick(() => {
            this.$set(this.companyTreeFields, 'dataSource', _data)
          })
        })
    },
    //选取组织数据
    selectOwnerOrg(e) {
      if (e.action !== 'select') return
      if (e && e.itemData) {
        this.ownerOrg = {
          ownerOrgId: [e.itemData.id],
          ownerOrgName: e.itemData.text
        }
      } else {
        this.ownerOrg = {
          ownerOrgId: null,
          ownerOrgName: null
        }
      }
      this.$emit('changeFilterData', {
        org: this.ownerOrg,
        plan: this.planData
      })
    },
    //切换评分计划
    changePlanId(e) {
      this.planData = e.itemData
      this.$emit('changeFilterData', {
        org: this.ownerOrg,
        plan: this.planData
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-panel {
  flex-shrink: 0;
  background: #fff;
  padding: 10px;
  display: flex;
  align-items: center;
  .filter-item {
    width: 250px;
    &:not(:first-child) {
      margin-left: 10px;
    }
  }
}
</style>
