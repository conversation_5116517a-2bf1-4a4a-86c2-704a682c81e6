<template>
  <!-- 评分计划详情页面 -->
  <div class="score-setting">
    <div class="top" v-if="formObject">
      <div class="left">
        <div class="title">{{ formObject.planName }}</div>
        <div class="form">
          <div class="form_div">{{ $t('考核计划编码：') }}{{ formObject.planCode }}</div>
          <div class="form_div">
            {{ $t('创建人：') }}{{ formObject.createUserName }}{{ formObject.createTime }}
          </div>
          <div class="form_div">
            {{ $t('绩效模板：') }}<span>{{ formObject.templateCode }}</span
            >{{ formObject.templateName }}
          </div>
          <div class="form_div">{{ $t('绩效模板类型：') }}{{ formObject.templateType }}</div>
        </div>
        <div class="data">
          <div class="form_div">{{ $t('考评周期：') }}{{ map[formObject.period] }}</div>
          <div class="form_div">
            {{ $t('发布组织：') }}{{ formObject.orgName }}
            <span>{{ formObject.orgId }}</span>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="right_but" @click="back">{{ $t('返回') }}</div>
        <!--        <div class="right_but">{{ $t('保存') }}</div>-->
      </div>
    </div>
    <mt-template-page
      v-if="formObject"
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      :current-tab="currentTab"
      :tab-config="tabConfig"
      @selected="selected"
    >
      <!-- 参考范围设置 -->
      <tab-setting slot="slot-0" :form="formObject" @init="init"></tab-setting>
      <!-- 评分人清单 -->
      <tab-detailed-list slot="slot-1" :form="formObject" @init="init"></tab-detailed-list>
      <!-- 指标特定评分人 -->
      <tab-user slot="slot-2" :form="formObject" @init="init"></tab-user>
    </mt-template-page>
  </div>
</template>

<script>
export default {
  components: {
    //参考范围设置
    tabSetting: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreSetting/dimension" */ './components/setting'
      ),
    //评分人清单
    tabDetailedList: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreSetting/target" */ './components/detailedList'
      ),
    // 指标特定评分人
    tabUser: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreSetting/target" */ './components/user'
      )
  },
  data() {
    return {
      map: {
        0: this.$t('年度'),
        1: this.$t('半年度'),
        2: this.$t('季度'),
        3: this.$t('月度'),
        4: this.$t('单日')
      },
      formObject: null,
      id: null,
      tabConfig: {
        eTab: true
      },
      pageConfig: [
        {
          gridId: 'a4735549-1979-4e79-b561-ef0dbf0737d7',
          tab: { title: this.$t('参考范围设置'), header: { text: this.$t('参考范围设置') } }
        },
        {
          gridId: '3eb21604-61f5-44e0-8458-448f45cbbab1',
          tab: { title: this.$t('评分人清单'), header: { text: this.$t('评分人清单') } }
        },
        {
          gridId: '6424a492-d494-4934-b11b-f8cba2e1a2f5',
          tab: {
            title: this.$t('指标特定评分人'),
            disabled: false,
            header: { text: this.$t('指标特定评分人') }
          }
        }
      ],
      isDisableTab: false,
      currentTab: 0
    }
  },
  created() {
    this.id = this.$route.query.id
    this.init()
  },
  methods: {
    // 设置 disabled
    selected(event) {
      let { selectedIndex } = event
      this.currentTab = selectedIndex
    },
    init(value) {
      console.log(value)
      let arr = ['appointRater', 'rangeConfig', 'raterList']
      if (value) {
        arr.forEach((ele) => {
          this.$set(this.formObject, ele, null)
        })
      }
      this.$API.performanceScorePlan.planDetailInfo({ id: this.id }).then((res) => {
        // 初始化tab disabled
        !!res.data && !!res.data.templateId && this.getPerformanceScorePlan(res.data.templateId)
        if (value) {
          arr.forEach((ele) => {
            this.$set(this.formObject, ele, res.data[ele])
          })
        } else {
          this.$set(this, 'formObject', res.data)
          if (!this.formObject.rangeConfig) this.formObject.rangeConfig = []
        }
      })
    },
    // 指标如果只有一个 特定评分人tab置灰不能点击
    getPerformanceScorePlan(templateId) {
      this.$API.performanceScorePlan.templateSelect({ templateId }).then((res) => {
        let disabled = (!!res.data && res.data.length === 1) || false
        this.$set(this.pageConfig[2].tab, 'disabled', disabled)
      })
    },
    back() {
      this.$router.go(-1) //返回上一层
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
  width: 100%;
  background: white;
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 30px 30px 20px 30px;
    margin-top: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid #e8e8e8ff;
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    .right_but {
      display: inline-block;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #00469cff;
      margin: 0 10px;
    }
    .left {
      color: #292929;
      .title {
        font-size: 20px;
        font-weight: 600;
      }
      .form {
        padding: 10px 0;
        font-size: 12px;
        font-weight: normal;
      }
      .form_div {
        display: inline-block;
        padding-right: 20px;
        span {
          margin: 0 3px;
          color: #00469c;
        }
      }
      .data {
        padding: 10px 0;
        font-size: 14px;
        font-weight: 600;
      }
    }
  }
  /deep/ .mt-data-grid {
    .status-label {
      font-size: 12px;
      padding: 4px;
      border-radius: 2px;
      &.status-disable {
        color: #9baac1;
        background: rgba(155, 170, 193, 0.1);
      }
      &.status-enable {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
  }
}
</style>
