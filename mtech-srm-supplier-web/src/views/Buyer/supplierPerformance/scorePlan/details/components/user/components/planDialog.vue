<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="indexId" :label="$t('指标：')">
          <mt-multi-select
            v-model="formObject.indexId"
            :data-source="dataArr"
            :fields="{
              text: 'indexName',
              value: 'indexId'
            }"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="type" :label="$t('特定类型：')">
          <mt-select
            ref="dimensionRef"
            v-model="formObject.type"
            float-label-type="Never"
            :data-source="supplierCompanyList"
            :fields="{
              text: 'name',
              value: 'value'
            }"
            :placeholder="$t('请选择特定类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="raterId" :label="$t('评分人：')">
          <mt-multi-select
            ref="employeeListRef"
            v-model="formObject.raterId"
            :data-source="employeeList"
            :fields="{ text: 'raterName', value: 'raterId' }"
            filter-bar-placeholder="$t('Search')"
            :allow-filtering="true"
            :placeholder="$t('请选择评分人')"
            @input="changeTemplateTypeSelect"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item :label="$t('备注：')" prop="remark">
          <mt-input
            :multiline="true"
            css-class="e-outline"
            :rows="3"
            type="text"
            float-label-type="Never"
            v-model="formObject.remark"
            maxlength="200"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig([]),
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('新增') }
        }
      ],
      dataArr: [],
      formObject: {
        type: 0
      },
      formRules: {
        indexId: [{ required: true, message: this.$t('请选择指标'), trigger: 'blur' }],
        raterId: [{ required: true, message: this.$t('请选择评分人'), trigger: 'blur' }],
        type: [{ required: true, message: this.$t('请选择特定类型'), trigger: 'blur' }]
      },
      supplierCompanyList: [
        {
          name: this.$t('仅限'),
          value: 0
        }
      ],
      employeeList: [] //人员列表
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData.id) {
      this.$API.performanceScorePlan
        .raterSelect({
          id: this.modalData.id
        })
        .then((res) => {
          this.employeeList = res.data
          console.log('res>>>>', res)
        })
    }
    this.init()
  },
  methods: {
    changeTemplateTypeSelect(e) {
      console.log(e)
      let arr = []
      this.employeeList.forEach((ele) => {
        if (e.includes(ele.raterId)) {
          arr.push(ele)
        }
      })
      console.log(arr)
      this.pageConfig = pageConfig(arr)
    },
    init() {
      console.log(this.modalData)
      this.$API.performanceScorePlan
        .templateSelect({ templateId: this.modalData.templateId })
        .then((res) => {
          this.dataArr = res.data
          console.log(res)
        })
      //
    },
    sent(value) {
      console.log(value)
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          console.log(this.formObject)
          let _params = []
          this.employeeList.forEach((ele) => {
            if (this.formObject.raterId.includes(ele.raterId)) {
              let obj = {
                raterId: ele.raterId,
                raterName: ele.raterName
              }
              _params.push(obj)
            }
          })
          let params = {
            indexIdList: this.formObject.indexId,
            planId: this.modalData.id,
            remark: this.formObject.remark || '',
            type: this.formObject.type,
            templateId: this.modalData.templateId,
            raterDTOList: _params
          }
          this.$API.performanceScorePlan.addRaterData(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    confirm() {
      this.sent(0)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .mt-form-item {
    width: 100% !important;
  }
  /deep/ span.e-input-group {
    padding: 0;
  }
  /deep/ .process-desc {
    width: 820px !important;
  }
}
</style>
