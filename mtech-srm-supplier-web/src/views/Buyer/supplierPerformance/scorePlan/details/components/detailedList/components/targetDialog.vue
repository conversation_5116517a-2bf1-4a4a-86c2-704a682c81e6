<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="supplierEnterpriseId" :label="$t('供应商：')">
          <mt-select
            ref="dimensionRef"
            v-model="formObject.supplierEnterpriseId"
            float-label-type="Never"
            :data-source="supplierCompanyList"
            :fields="{
              text: 'supplierEnterpriseName',
              value: 'supplierEnterpriseId'
            }"
            :placeholder="$t('请选择供应商')"
            @change="supp"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="orgId" :label="$t('公司：')">
          <mt-multi-select
            :id="'org_' + new Date().getTime()"
            ref="ownerOrgNameRef"
            :placeholder="$t('选择公司')"
            v-model="formObject.orgId"
            :data-source="companyTreeFields"
            :fields="{ text: 'orgName', value: 'orgId' }"
            @input="selectOwnerOrg"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="raterId" :label="$t('评分人：')">
          <mt-multi-select
            ref="employeeListRef"
            v-model="formObject.raterId"
            :data-source="employeeList"
            :fields="{ text: 'employeeName', value: 'id' }"
            filter-bar-placeholder="$t('Search')"
            :allow-filtering="true"
            :placeholder="$t('请选择评分人')"
          ></mt-multi-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('新增') }
        }
      ],
      formObject: {},
      formArr: {},
      formRules: {
        supplierEnterpriseId: [
          { required: true, message: this.$t('请选择供应商'), trigger: 'blur' }
        ],
        raterId: [{ required: true, message: this.$t('请选择评分人'), trigger: 'blur' }],
        orgId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }]
      },
      supplierCompanyList: [],
      employeeList: [], //人员列表
      companyTreeFields: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData.id) {
      this.$API.performanceScorePlan
        .planSelect({
          id: this.modalData.id
        })
        .then((res) => {
          this.supplierCompanyList = res.data
          console.log('res>>>>', res)
        })
    }
    this.init()
  },
  methods: {
    init() {},
    flatTreeData(tree, children_key = 'children') {
      if (!tree || Object.prototype.toString.call(tree) !== '[object Array]' || tree.length <= 0)
        return []
      return tree.reduce(
        (pre, cur) => pre.concat(cur, this.flatTreeData(cur[children_key], children_key)),
        []
      )
    },
    supp(e) {
      console.log(e)
      if (e.itemData) {
        this.formArr = e.itemData
        this.$API.performanceScorePlan
          .relationListByOrgId({
            orgId: this.modalData.orgId,
            supplierEnterpriseId: [e.itemData.supplierEnterpriseId]
          })
          .then((res) => {
            this.companyTreeFields = res.data
            console.log(res)
          })
      }
    },
    selectOwnerOrg(e) {
      console.log(e)
      console.log('form>>>>', this.formObject.orgId)
      if (e.length < 1) {
        this.$set(this, 'employeeList', [])
        console.log(this.employeeList)
        return
      }
      let arrs = []
      let arr = []
      this.companyTreeFields.forEach((ele) => {
        if (e.includes(ele.orgId)) {
          arrs.push({
            orgCode: ele.orgCode,
            orgId: ele.orgId,
            orgName: ele.orgName
          })
          arr.push(
            this.$API.performanceScorePlan.getOrganizationEmployees({
              orgId: ele.orgId
            })
          )
        }
      })

      Promise.all(arr).then((res) => {
        // this.$set(this, "employeeList", res.data);
        console.log(res)
        let resData = []
        res.forEach((ele, index) => {
          ele.data.forEach((item) => {
            resData.push({ ...item, ...arrs[index] })
          })
        })
        this.$set(this, 'employeeList', resData)
        console.log(this.employeeList)
      })
    },
    selectDownOrg(e) {
      if (e && e.itemData) {
        this.formObject.downOrgName = e.itemData.text
      }
    },
    changeFullScore(e) {
      this.formObject.fullScore = e
    },
    sent(value) {
      console.log(value)
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          // this.formObject.raterId
          let params = []
          this.employeeList.forEach((ele) => {
            if (this.formObject.raterId.includes(ele.id)) {
              params.push({
                deptId: ele.departmentOrganizationId,
                deptName: ele.departmentName,
                orgCode: ele.orgCode,
                source: 1,
                orgId: ele.orgId,
                orgName: ele.orgName,
                partnerArchiveId: this.formArr.partnerArchiveId,
                partnerRelationId: this.formArr.partnerRelationId,
                planId: this.modalData.id,
                raterId: ele.userId,
                raterName: ele.employeeName,
                supplierEnterpriseCode: this.formArr.supplierEnterpriseCode,
                supplierEnterpriseId: this.formArr.supplierEnterpriseId,
                supplierEnterpriseName: this.formArr.supplierEnterpriseName
              })
            }
          })
          this.$API.performanceScorePlan.addRaterx(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    confirm() {
      this.sent(0)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .mt-form-item {
    width: 100% !important;
  }
  /deep/ span.e-input-group {
    padding: 0;
  }
  /deep/ .process-desc {
    width: 820px !important;
  }
}
</style>
