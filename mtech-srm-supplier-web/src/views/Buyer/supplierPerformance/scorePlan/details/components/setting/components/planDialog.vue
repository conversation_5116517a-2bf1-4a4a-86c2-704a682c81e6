<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content fbox">
      <div class="select-box">
        <mt-form ref="dialogRef">
          <mt-form-item prop="selectCompanyId" :label="$t('所属组织：')">
            <mt-select
              width="300"
              :fields="{
                text: 'orgName',
                value: 'id'
              }"
              :disabled="true"
              v-model="selectCompanyId"
              :data-source="orgList"
              :show-clear-button="false"
              :placeholder="$t('筛选当前用户所在组织')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="selectDefineId" :label="$t('分级：')">
            <mt-select
              width="300"
              :fields="{
                text: 'labelName',
                value: 'id'
              }"
              v-model="selectDefineId"
              :data-source="defineList"
              :show-clear-button="false"
              :placeholder="$t('筛选分级')"
              @change="changeDefineId"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="flex1">
        <mt-template-page
          ref="templateRef"
          :use-tool-template="false"
          :template-config="pageConfig"
        />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { pageConfig } from '../config/planDialog.js'
import utils from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: [],
      orgList: [], // 包含当前用户的所有的组织机构list
      selectCompanyId: '', // 已选公司id
      selectDefineId: null, // 已选分级id
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      editStatus: false,
      defineList: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  async created() {
    const userInfo = await this.queryUserInfo()
    if (userInfo.code === 200 && !utils.isEmpty(userInfo.data)) {
      let currentOrgObj = userInfo.data.companyOrg
      this.orgList = [currentOrgObj]
      this.selectCompanyId = currentOrgObj.id
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
    }
    this.getDefineList()
  },
  methods: {
    getDefineList() {
      this.$API.performanceScorePlan.getDefineList({ labelDefineType: 1 }).then((res) => {
        let defineList = [
          {
            id: '-1',
            tenantId: '0',
            labelCode: '0',
            labelName: this.$t('不限'),
            labelType: '0',
            labelDefineType: 0,
            status: 0,
            remark: '',
            createUserName: '0',
            createTime: '2021-12-17 19:35:18',
            coverType: 0,
            recommend: 0
          }
        ]
        let defineDataList = res.data || []
        let defineResultList = [...defineList, ...defineDataList]
        this.defineList = defineResultList
        if (Array.isArray(this.defineList) && this.defineList.length) {
          this.selectDefineId = this.defineList[0].id
        }
      })
    },
    changeDefineId(data) {
      const { itemData } = data
      if (!!itemData && !itemData.id) return
      let selectDefineId = itemData.id
      this.selectDefineId = selectDefineId
      this.getSupplierData()
    },
    getSupplierData() {
      if (this.selectCompanyId) {
        this.pageConfig = pageConfig(
          this.$API.performanceScorePlan.getSupplierData,
          this.selectCompanyId,
          this.selectDefineId
        )
      } else {
        setTimeout(() => {
          this.getSupplierData()
        }, 100)
      }
    },
    confirm() {
      const currentTab = this.$refs.templateRef.getCurrentTabRef()
      const rowSelected = currentTab.gridRef.getMtechGridRecords()
      // console.log(rowSelected);
      if (!rowSelected || rowSelected.length === 0) {
        this.$toast({ content: this.$t('请至少勾选一个供应商！'), type: 'warning' })
        return
      }
      let arr = []
      rowSelected.forEach((ele) => {
        arr.push({
          planId: this.modalData.id,
          orgId: ele.orgId,
          partnerArchiveId: ele.partnerArchiveId,
          partnerRelationId: ele.id,
          supplierEnterpriseCode: ele.supplierEnterpriseCode,
          supplierEnterpriseId: ele.supplierEnterpriseId,
          supplierEnterpriseName: ele.supplierEnterpriseName
        })
      })
      this.$API.performanceScorePlan.addIndex(arr).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$emit('confirm-function')
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 获取当前用户信息
    queryUserInfo() {
      return this.$API.supplierAcc.queryUserInfo()
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  height: 100%;
  flex-direction: column;
  .select-box {
    height: 70px;
    margin-top: 10px;
    .select-container {
      &:not(:first-child) {
        margin-left: 15px;
      }
    }
  }
  /deep/ span.e-input-group {
    padding: 0;
  }
  /deep/ .process-desc {
    width: 820px !important;
  }
}
</style>
