<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form v-if="dialogRules" ref="dialogRef" :model="formObject" :rules="dialogRules">
        <mt-form-item prop="planName" :label="$t('考评计划名称：')">
          <mt-input
            v-model="formObject.planName"
            float-label-type="Never"
            :placeholder="$t('请输入考评计划名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="orgId" :label="$t('发布组织：')" v-if="orgList.dataSource.length">
          <mt-DropDownTree
            :id="'ord_' + new Date().getTime()"
            ref="ownerOrgNameRef"
            v-model="formObject.orgId"
            :show-clear-button="false"
            v-if="orgList.dataSource.length"
            :fields="orgList"
            :placeholder="$t('请选择发布组织')"
            @select="companyList($event, 'orgName', true)"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="templateId" :label="$t('绩效模板：')">
          <mt-select
            v-model="formObject.templateId"
            :data-source="operation"
            :fields="{ text: 'templateName', value: 'templateId' }"
            :show-clear-button="true"
            :placeholder="$t('请选择绩效模板')"
            @change="companyList($event, 'templateName', false)"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="deadline" :label="$t('有效期：')">
          <mt-date-time-picker
            v-model="formObject.deadline"
            :open-on-focus="true"
            :placeholder="$t('选择有效期')"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item prop="period" :label="$t('周期：')">
          <mt-select
            v-model.number="formObject.period"
            :data-source="periodList"
            :fields="{ text: 'name', value: 'value' }"
            :show-clear-button="true"
            :placeholder="$t('请选择周期')"
          ></mt-select>
          <mt-date-picker
            v-if="formObject.period === 4"
            v-model="formObject.dayDate"
            format="yyyy-MM-dd"
            :open-on-focus="true"
            :placeholder="$t('选择日期')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="timeLimit" :label="$t('提交时限：(单位：天）')">
          <mt-input
            v-model.number="formObject.timeLimit"
            :min="0"
            :max="1000000000"
            :precision="0"
            :placeholder="$t('提交时限')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注：')" class="process-desc">
          <mt-input
            :multiline="true"
            css-class="e-outline"
            :rows="3"
            type="text"
            maxlength="200"
            float-label-type="Never"
            v-model="formObject.remark"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
export default {
  data() {
    var checkAge = (dialogRules, value, callback) => {
      var reg = /^[0-9]*$/
      if (!value) {
        callback(new Error(this.$t('时限不能为空')))
      } else if (value.length <= 9) {
        callback()
      }
      setTimeout(() => {
        if (reg.test(value)) {
          callback()
        } else {
          callback(new Error(this.$t('请输入正确的数字格式')))
        }
      }, 100)
    }

    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      operation: [],
      orgList: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      periodList: [
        {
          name: this.$t('年度'),
          value: 0
        },
        {
          name: this.$t('半年度'),
          value: 1
        },
        {
          name: this.$t('季度'),
          value: 2
        },
        {
          name: this.$t('月度'),
          value: 3
        },
        {
          name: this.$t('单日'),
          value: 4
        }
      ],

      editStatus: false,
      id: null,
      formObject: {
        timeLimit: ''
      },
      dialogRules: {
        timeLimit: [
          {
            validator: checkAge,
            trigger: 'blur',
            required: true,
            message: this.$t('提交时限：(单位：天)')
          }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    console.log(this.modalData)
    if (this.modalData && this.modalData.data) {
      this.id = this.modalData.data.id
      this.editStatus = true
    }
    this.init()
  },
  methods: {
    init() {
      let str = 'addPlanDatavalid'
      if (this.editStatus) {
        str = 'editPlanDataValid'
        this.$API.performanceScorePlan
          .planDetail({
            id: this.id
          })
          .then((res) => {
            let _data = { ...res.data }
            _data.orgId = [_data.orgId]
            this.$API.performanceScoreSetting
              .templateSelectList({ orgId: res.data.orgId })
              .then((_res) => {
                console.log(_res)
                this.operation = _res.data
              })
            this.$set(this, 'formObject', _data)
            this.formObject.deadline = new Date(Number(res.data.deadline))
            console.log(this.formObject.deadline)
          })
      }
      this.$API.performanceScorePlan[str]().then((res) => {
        this.dialogRules = utils.formatRules(res.data)
      })
      this.$API.performanceScoreSetting
        .getStatedLimitTree({
          orgLevelCode: 'ORG02',
          orgType: 'ORG001PRO'
        })
        .then((res) => {
          this.$nextTick(() => {
            this.$set(this.orgList, 'dataSource', res.data)
          })
          console.log('组织树', res)
        })
    },
    companyList(e, name, box) {
      console.log('e>>>>>>>', e)
      if (box) {
        this.formObject['orgId'] = e.itemData ? e.itemData.id : e.id
        this.formObject[name] = e.itemData ? e.itemData.text : e.text
        console.log(this.formObject['orgId'])
        this.$API.performanceScoreSetting
          .templateSelectList({ orgId: this.formObject.orgId })
          .then((res) => {
            console.log(res)
            this.operation = res.data
          })
      } else {
        this.formObject[name] = e.itemData[name]
      }
    },
    date(time) {
      return new Date(time).getTime()
    },
    confirm() {
      console.log(this.formObject)
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.formObject))
          if (Array.isArray(params.orgId)) {
            params.orgId = params.orgId[0]
          }
          if (params.timeLimit > 127) {
            params.timeLimit = 127
          } else if (params.timeLimit < 0) {
            params.timeLimit = 0
          }
          console.log(params.deadline)
          if (params.period === 4 && !params.dayDate) {
            this.$toast({ content: this.$t('请选择周期日期'), type: 'warning' })
            return
          }
          if (params.dayDate) params.dayDate = String(this.date(params.dayDate))
          params.deadline = String(this.date(params.deadline))
          let value = 'addPlanData'
          if (this.editStatus) {
            value = 'editPlanData'
          }
          this.$API.performanceScorePlan[value](params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .mt-form-item {
    width: 100% !important;
  }
  /deep/ span.e-input-group {
    padding: 0;
  }
  /deep/ .process-desc {
    width: 820px !important;
  }
}
</style>
