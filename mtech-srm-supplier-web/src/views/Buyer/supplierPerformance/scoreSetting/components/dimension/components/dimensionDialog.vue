<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" :height="330" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="dimensionName" :label="$t('维度名称：')">
          <mt-input
            v-model="formObject.dimensionName"
            float-label-type="Never"
            :placeholder="$t('请输入维度名称')"
            maxlength="30"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('备注：')" prop="remark">
          <mt-input
            :multiline="true"
            css-class="e-outline"
            :rows="3"
            type="text"
            maxlength="200"
            float-label-type="Never"
            v-model="formObject.remark"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('新增') }
        }
      ],
      formObject: {
        id: '', //配置Id
        dimensionName: '', //名称
        remark: '' //备注
      },
      formRules: {
        dimensionName: [
          { required: true, message: this.$t('请输入维度名称'), trigger: 'blur' },
          {
            min: 1,
            max: 30,
            message: this.$t('请输入维度名称'),
            trigger: 'blur'
          }
        ]
        // remark: [
        //   { required: true, message: this.$t("请输入"), trigger: "blur" },
        //   {
        //     min: 1,
        //     max: 20,
        //     message: '长度在 1 到 20 个字符',
        //     trigger: 'blur'
        //   }
        // ],
      },
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.$nextTick(() => {
        // this.$set(this.buttons[1].buttonModel, "content", "保存");
        this.buttons = [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('保存') }
          }
        ]
      })
      let _data = { ...this.modalData.data }
      this.formObject = {
        id: _data.id, //配置Id
        dimensionName: _data.dimensionName, //名称
        remark: _data.remark //备注
      }
    }
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.formObject))
          if (this.editStatus) {
            this.$API.performanceScoreSetting.editDimension(params).then((res) => {
              if (res.code == 200) {
                this.$emit('confirm-function')
              }
            })
          } else {
            delete params.id
            params.enable = 1
            this.$API.performanceScoreSetting.addDimension(params).then((res) => {
              if (res.code == 200) {
                this.$emit('confirm-function')
              }
            })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
