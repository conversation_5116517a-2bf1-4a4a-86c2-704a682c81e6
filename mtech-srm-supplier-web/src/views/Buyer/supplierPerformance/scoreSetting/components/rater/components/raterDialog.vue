<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <!-- v-if="companyTreeFields.dataSource.length" -->
        <mt-form-item prop="ownerOrgName" :label="$t('公司：')">
          <!-- <mt-DropDownTree
            ref="ownerOrgNameRef"
            v-if="companyTreeFields.dataSource.length"
            :placeholder="$t('选择公司')"
            v-model="formObject.ownerOrgId"
            :fields="companyTreeFields"
            :show-clear-button="false"
            @select="selectOwnerOrg"
          ></mt-DropDownTree> -->
          <mt-input disabled v-model="formObject.ownerOrgName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="typeIds" :label="$t('模板类型：')">
          <mt-multi-select
            ref="templateTypeRef"
            v-model="formObject.typeIds"
            float-label-type="Never"
            :data-source="templateTypeList"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('不选择即视为不限')"
            @change="changeTemplateTypeSelect"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="depIds" :label="$t('选择部门')">
          <mt-multi-select
            ref="depListRef"
            v-model="formObject.depIds"
            :data-source="departmentList"
            :fields="{ text: 'orgName', value: 'id' }"
            :placeholder="$t('请选择部门')"
            @change="changeDepSelect"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="raterIds" :label="$t('选择人员')">
          <mt-multi-select
            ref="employeeListRef"
            v-model="formObject.raterIds"
            :data-source="employeeList"
            :fields="{ text: 'employeeName', value: 'id' }"
            :placeholder="$t('请选择人员')"
            @change="changeRaterSelect"
          ></mt-multi-select>
        </mt-form-item>
      </mt-form>
      <div class="rater-list">
        <mt-data-grid
          ref="raterListRef"
          height="270"
          :data-source="selectRaterList"
          :column-data="raterColumnData"
          :allow-filtering="false"
          :allow-sorting="false"
          :allow-paging="false"
        ></mt-data-grid>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      formObject: {
        id: null, //配置Id
        ownerOrgCode: null, //所属组织编码
        ownerOrgId: null, //	所属组织id
        ownerOrgName: null, //所属组织名称
        orgLevelTypeCode: null, //所属组织名称
        templateTypeId: null, //模板类型 id
        templateTypeCode: null, //模板类型 code
        templateTypeName: null, //模板类型 name
        depIds: null, //部门IDS
        raterIds: null, //人员IDS
        typeIds: null // type Ids
      },
      formRules: {
        ownerOrgName: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        templateTypeId: [
          { required: true, message: this.$t('请选择适用模板类型'), trigger: 'blur' }
        ]
      },
      departmentList: [], //部门列表
      selectDepartmentList: [], //当前选中的部门列表
      employeeList: [], //人员列表
      selectRaterList: [], //当前选中的人员列表
      templateTypeList: [], //模板类型-列表
      selectTemplateTypeList: [], //当前选中的模板类型列表
      companyTreeList: [], //组织数据--树形
      flatCompanyTreeList: [], //组织数据--平铺
      companyTreeFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      raterColumnData: [
        {
          field: 'employeeName',
          headerText: this.$t('人员名称')
        },
        {
          field: 'employeeCode',
          headerText: this.$t('人员账号')
        },
        {
          field: 'ownerOrgName',
          headerText: this.$t('公司名称')
        },
        // {
        //   field: "createUserName1",
        //   headerText: this.$t("部门名称"),
        // },
        {
          field: 'createTime',
          headerText: this.$t('创建时间'),
          type: 'date',
          format: 'yyyy-MM-dd'
        },
        {
          field: 'createUserName',
          headerText: this.$t('创建人')
        }
      ],
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.templateTypeList = this.modalData.templateTypeList
    if (!this.editStatus) {
      //如果是新增操作，默认赋值第一条数据
      if (this.templateTypeList.length) {
        this.formObject.templateTypeId = this.templateTypeList[0]['id']
      }
    }
    // this.getCompanyTree();
    this.getUserInfoDetail()
    // this.getDepartmentList();
    // this.getEmployeeList();
  },
  methods: {
    // 获取当前用户的信息 取公司信息companyOrg
    getUserInfoDetail() {
      this.$API.performanceScoreSetting.getUserDetail().then((res) => {
        let { data } = res
        let { companyOrg } = data
        this.formObject = {
          ...this.formObject,
          ownerOrgCode: companyOrg.orgCode, // 所属组织编码
          ownerOrgId: companyOrg.id, //	所属组织id
          orgLevelTypeCode: companyOrg.orgLevelTypeCode, //所属组织名称
          ownerOrgName: companyOrg.orgName //所属组织名称
        }
        this.selectDefinedOwnerOrg()
      })
    },
    // 获取弹窗中的组织-下拉树
    getCompanyTree() {
      this.$API.performanceScoreSetting
        .getStatedLimitTree({
          orgLevelCode: 'ORG02',
          orgType: 'ORG001PRO'
        })
        .then((res) => {
          let _data = []
          if (res && res.data && Array.isArray(res.data)) {
            _data = res.data
          }
          this.companyTreeList = _data
          this.flatCompanyTreeList = this.modalData.flatTreeData(_data)
          this.companyTreeFields.dataSource = []
          this.$nextTick(() => {
            this.$set(this.companyTreeFields, 'dataSource', _data)
          })
        })
    },
    // // 获取弹窗中的部门列表
    // getDepartmentList() {
    //   this.$API.performanceScoreSetting
    //     .getDepartmentList({ departmentName: "" })
    //     .then((res) => {
    //       if (Array.isArray(res.data)) {
    //         this.departmentList = res.data;
    //       } else {
    //         this.departmentList = [];
    //       }
    //     });
    // },
    // // 获取弹窗中的人员列表
    // getEmployeeList() {
    //   this.$API.performanceScoreSetting
    //     .getEmployeeList({ employeeName: "" })
    //     .then((res) => {
    //       if (Array.isArray(res.data)) {
    //         this.employeeList = res.data;
    //       } else {
    //         this.employeeList = [];
    //       }
    //     });
    // },
    selectDefinedOwnerOrg() {
      this.$API.performanceScoreSetting
        .findSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG03'],
          organzationIds: [this.formObject.ownerOrgId]
        })
        .then((res) => {
          if (Array.isArray(res.data)) {
            this.departmentList = res.data
          } else {
            this.departmentList = []
            this.selectDepartmentList = []
            this.selectRaterList = []
          }
        })
    },
    //选取组织数据 #废弃
    selectOwnerOrg(e) {
      if (e.action !== 'select') return
      if (e && e.itemData) {
        this.formObject.ownerOrgId = [e.itemData.id]
        this.formObject.ownerOrgName = e.itemData.text
      } else {
        this.formObject.ownerOrgId = null
        this.formObject.ownerOrgName = null
      }
      this.$API.performanceScoreSetting
        .findSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG03'],
          organzationIds: this.formObject.ownerOrgId
        })
        .then((res) => {
          if (Array.isArray(res.data)) {
            this.departmentList = res.data
          } else {
            this.departmentList = []
            this.selectDepartmentList = []
            this.selectRaterList = []
          }
        })
    },
    // select数据赋值
    getSelectList(_list, object = 'departmentList') {
      let _res = []
      _list.forEach((e) => {
        _res.push(this[object].find((f) => f.id === e))
      })
      return _res
    },
    changeDepSelect(e) {
      if (e.value && Array.isArray(e.value) && e.value.length) {
        this.selectDepartmentList = this.getSelectList(e.value)
        let ids = []
        this.selectDepartmentList.forEach((e) => {
          ids.push(e.id)
        })
        this.$API.performanceScoreSetting
          .findEmployeesInDeparments({
            ids
          })
          .then((res) => {
            if (Array.isArray(res.data)) {
              this.employeeList = res.data
            } else {
              this.employeeList = []
              this.selectRaterList = []
            }
          })
      } else {
        this.selectDepartmentList = []
        this.employeeList = []
        this.selectRaterList = []
      }
    },
    changeRaterSelect(e) {
      console.log('change--', e)
      if (e.value && Array.isArray(e.value) && e.value.length) {
        this.selectRaterList = this.getSelectList(e.value, 'employeeList')
      } else {
        this.selectRaterList = []
      }
      this.selectRaterList.forEach((e) => {
        e.ownerOrgName = this.formObject.ownerOrgName
      })
    },
    changeTemplateTypeSelect(e) {
      console.log('change--', e)
      if (e.value && Array.isArray(e.value) && e.value.length) {
        this.selectTemplateTypeList = this.getSelectList(e.value, 'templateTypeList')
      } else {
        this.selectTemplateTypeList = []
      }
    },
    confirm() {
      if (this.formObject.orgLevelTypeCode === 'ORG01') {
        this.$toast({
          content: this.$t('抱歉，集团账户没有权利设置评分人！'),
          type: 'warning'
        })
        return
      }
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          if (this.selectRaterList.length < 1) {
            this.$toast({
              content: this.$t('未设置人员'),
              type: 'warning'
            })
            return
          }
          // 移除模板类型必填。模板类型可不选
          // if (this.selectTemplateTypeList.length < 1) {
          //   this.$toast({
          //     content: "未选择模板类型.",
          //     type: "warning",
          //   });
          //   return;
          // }
          let _formObject = { ...this.formObject }
          _formObject.depList = this.selectDepartmentList
          _formObject.raterList = this.selectRaterList
          // if (_formObject.ownerOrgId) {
          //   //根据当前选择的ownerOrgId数组，获取ownerOrgName数组
          //   if (
          //     Array.isArray(_formObject.ownerOrgId) &&
          //     _formObject.ownerOrgId.length
          //   ) {
          //     let _ids = [..._formObject.ownerOrgId],
          //       _names = [],
          //       _codes = [];
          //     _ids.forEach((e) => {
          //       let _find = this.flatCompanyTreeList.find((f) => e === f.id);
          //       _names.push(_find.name);
          //       _codes.push(_find.orgCode);
          //     });
          //     _formObject.ownerOrgId = _ids.join(","); //	所属组织id
          //     _formObject.ownerOrgName = _names.join(","); //所属组织名称
          //     _formObject.ownerOrgCode = _codes.join(","); //所属组织编码
          //   }
          // }
          let _raterlist = [...this.selectRaterList],
            _typelist = [...this.selectTemplateTypeList],
            addRaterDTOList = [],
            addTemplateTypeDTOList = []
          _raterlist.forEach((e) => {
            addRaterDTOList.push({
              raterId: e.userId, //	修改参数取值，取userId
              raterName: e.employeeName //评分人
            })
          })
          _typelist.forEach((e) => {
            addTemplateTypeDTOList.push({
              templateTypeCode: e.itemCode,
              templateTypeId: e.id,
              templateTypeName: e.itemName
            })
          })
          let params = {
            orgId: _formObject.ownerOrgId,
            orgCode: _formObject.ownerOrgCode,
            orgName: _formObject.ownerOrgName,
            addRaterDTOList,
            addTemplateTypeDTOList
          }
          this.$API.performanceScoreSetting.addRater(params).then(() => {
            this.$emit('confirm-function')
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .process-desc {
    width: 820px !important;
  }
}
</style>
