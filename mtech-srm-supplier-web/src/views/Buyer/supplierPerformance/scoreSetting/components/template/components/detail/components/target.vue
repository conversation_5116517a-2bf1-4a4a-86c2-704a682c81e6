<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="parentDimensionName" :label="$t('维度名称：')">
          <mt-input
            :disabled="true"
            :show-clear-button="false"
            v-model="formObject.parentDimensionName"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="indexName" :label="$t('指标名称：')" v-if="editStatus">
          <mt-input
            :disabled="true"
            :show-clear-button="false"
            v-model="formObject.indexName"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="indexId" :label="$t('指标名称：')" v-else>
          <mt-select
            ref="indexRef"
            v-model="formObject.indexId"
            float-label-type="Never"
            :data-source="indexList"
            :fields="{ text: 'indexName', value: 'indexId' }"
            :placeholder="$t('请选择指标')"
            @change="handleSelectChange($event, 'indexId')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="indexFullScore" :label="$t('指标满分：')">
          <mt-input
            :disabled="true"
            :show-clear-button="false"
            v-model="formObject.indexFullScore"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="weight" :label="$t('在维度内权重（%）：')">
          <mt-inputNumber
            ref="weightRef"
            :min="1"
            :max="100"
            v-model="formObject.weight"
            :placeholder="$t('输入权重') + '1~100'"
            @change="changeWeight"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item prop="scoreStandard" :label="$t('评分标准：')">
          <mt-input
            :disabled="true"
            :show-clear-button="false"
            v-model="formObject.scoreStandard"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        parentDimensionName: null,
        indexId: null,
        indexName: null,
        weight: null,
        indexFullScore: null, //指标满分
        scoreStandard: null //评分标准
      },
      formRules: {
        weight: [{ required: true, message: this.$t('请设置权重'), trigger: 'blur' }]
      },
      indexList: [],
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
    if (this.modalData && this.modalData.parent) {
      this.formObject.parentDimensionName = this.modalData.parent.dimensionName
    }
    if (this.modalData && this.modalData.indexList) {
      this.indexList = this.modalData.indexList
      if (!this.editStatus) {
        //如果是新增操作，默认赋值第一条数据
        if (this.indexList.length) {
          this.formObject.indexId = this.indexList[0]['indexId']
        }
      }
    }
  },
  methods: {
    changeWeight(e) {
      this.formObject.weight = e
    },
    handleSelectChange(e, formField) {
      this.formObject.indexId = e.itemData[formField]
      this.getFormDetail()
    },
    getFormDetail() {
      //指标详情
      this.$API.performanceScoreSetting.indexDetail({ id: this.formObject.indexId }).then((res) => {
        let { description, fullScore } = res.data
        this.formObject.indexFullScore = fullScore //指标满分
        this.formObject.scoreStandard = description //评分标准
      })
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.formObject))
          if (params.weight && params.weight > 0 && params.weight <= 100) {
            if (this.editStatus) {
              this.$emit('confirm-function', params)
            } else {
              //新增操作，可以通过下拉列表，选择维度
              this.$utils.assignDataFromRef(
                params,
                'indexId',
                this.$refs.indexRef.ejsRef,
                'indexName'
              )
              this.$emit('confirm-function', params)
            }
          } else {
            this.$toast({
              content: this.$t('权重设置有误'),
              type: 'warning'
            })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
