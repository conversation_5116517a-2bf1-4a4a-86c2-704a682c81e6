<template>
  <div class="tempalte-detail mt-flex-direction-column">
    <div class="page-top" @click="cancel"></div>
    <div class="page-bottom mt-flex">
      <div class="page-left" @click="cancel"></div>
      <div class="page-content fbox">
        <div class="detail-top">
          <div class="left">
            <div class="title">{{ formObject.templateName }}</div>
            <div class="form">
              <div class="form_div">{{ $t('模板编码：') }}{{ formObject.templateCode }}</div>
              <div class="form_div">
                {{ $t('创建人：') }}{{ formObject.createUserName }}
                {{ formObject.createDate }}
              </div>
              <div class="form_div">{{ $t('绩效模板：') }}{{ formObject.templateName }}</div>
              <div class="form_div">
                {{ $t('绩效模板类型：') }}{{ formObject.templateTypeName }}
              </div>
            </div>
            <div class="data">
              <div class="form_div">{{ $t('满分：') }}{{ formObject.score }}</div>
              <div class="form_div">
                {{ $t('所属组织：') }}{{ formObject.ownerOrgName
                }}<span>{{ formObject.ownerOrgCode }}</span>
              </div>
            </div>
          </div>
          <div class="right">
            <div class="right_but" @click="cancel">{{ $t('返回') }}</div>
            <div class="right_but" @click="confirm">{{ $t('保存') }}</div>
          </div>
        </div>
        <div class="detail-content flex1">
          <mt-template-page
            ref="templateRef"
            :template-config="pageConfig"
            @handleClickToolBar="handleClickToolBar"
            @handleClickCellTool="handleClickCellTool"
            @handleClickCellTitle="handleClickCellTitle"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      editStatus: false,
      pageConfig: [],
      headerFields: [
        { field: 'templateCode', headerText: this.$t('模板编码') },
        { field: 'templateName', headerText: this.$t('模板名称') },
        { field: 'templateTypeName', headerText: this.$t('模板类型') },
        { field: 'ownerOrgName', headerText: this.$t('所属组织') },
        { field: 'ownerOrgCode', headerText: this.$t('所属组织编码') }, //todo 缺少字段
        { field: 'score', headerText: this.$t('满分') },
        { field: 'createUserName', headerText: this.$t('创建人') },
        { field: 'createTime', headerText: this.$t('创建时间') }
      ],
      formObject: {
        id: null, //模板id
        templateCode: null, //模板编码
        templateName: null, //模板名称
        ownerOrgId: null, //所属组织id
        ownerOrgName: null, //所属组织名称
        templateTypeId: null,
        templateTypeCode: null,
        templateTypeName: null,
        score: null, //满分
        enabled: null, //	启用状态 0：启用，1：不启用
        remark: null, //	备注
        createTime: null, //	创建日期
        createUserName: null //	创建人
      },
      dimensionList: [], //维度列表
      detailInfo: {
        templateId: null, //模板id
        ownerOrgId: null, //	所属组织id
        dimensionList: [
          {
            indexList: [
              {
                indexId: null, //	指标id      ok
                indexName: null, //	指标名称      ok
                weight: null, //指标权重      ok
                allocateScore: null, //分配分值   ok  通过权重 计算出来
                indexFullScore: null, //指标满分      ok  维度没有
                indexFromSource: null, //	指标来源      ok  维度没有
                scoreStandard: null //评分标准      ok  维度没有
              }
            ],
            weight: null, //维度权重      ok
            allocateScore: null, //分配分值   ok  通过权重 计算出来
            dimensionName: null, //维度id      ok
            dimensionId: null //	维度名称      ok
          }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    }
  },
  mounted() {
    if (this.modalData && this.modalData.data && this.modalData.data.id) {
      this.getFormDetail(this.modalData.data.id)
    }
    this.pageConfig = pageConfig(this.modalData.data.enabled)
    this.getDimensionSelectList()
  },
  methods: {
    //获取维度列表-下拉框
    getDimensionSelectList() {
      this.$API.performanceScoreSetting.dimensionSelectList().then((res) => {
        if (res && res.data && Array.isArray(res.data)) {
          this.dimensionList = res.data
        } else {
          this.dimensionList = []
        }
      })
    },
    getFormDetail(id) {
      //模板详情
      this.$API.performanceScoreSetting.templateDetail({ id }).then((res) => {
        let _detail = { ...res.data }
        this.detailInfo = res.data
        this.formObject = {
          id: _detail.templateId, //模板id
          templateCode: _detail.templateCode, //模板编码
          templateName: _detail.templateName, //模板名称
          ownerOrgId: _detail.ownerOrgId, //所属组织id
          ownerOrgName: _detail.ownerOrgName, //所属组织名称
          templateTypeId: _detail.templateTypeId,
          templateTypeCode: _detail.templateTypeCode,
          templateTypeName: _detail.templateTypeName,
          score: _detail.score, //满分
          enabled: _detail.enabled, //	启用状态 0：启用，1：不启用
          remark: _detail.remark, //	备注
          createTime: _detail.createTime, //	创建日期
          createUserName: _detail.createUserName //	创建人
        }
        let _detailGridList = []
        if (
          res &&
          res.data &&
          Array.isArray(res.data.dimensionList) &&
          res.data.dimensionList.length
        ) {
          _detailGridList = res.data.dimensionList
        } else {
          _detailGridList = []
        }
        _detailGridList.forEach((e) => {
          //以下几个字段, 维度列表没有，需要补全
          e.indexFullScore = null //指标满分
          e.indexFromSource = null //指标来源
          e.scoreStandard = null //评分标准
          e.name = e.dimensionName //维度Name
          e.id = e.dimensionId //维度ID
          e.showDeleteIcon = true
          e.indexList.forEach((i) => {
            i.name = i.indexName //指标Name
            i.id = i.indexId //指标ID
            i.showDeleteIcon = Boolean(!i.indexFromSource) //存在指标来源，不可删除
            i.parentDimensionId = e.dimensionId
            i.parentDimensionName = e.dimensionName
          })
          let _hiddenDeleteIcon = e.indexList.filter((f) => !f.showDeleteIcon) //下级列表中，存在指标来源，不可删除
          if (_hiddenDeleteIcon.length) e.showDeleteIcon = false
        })
        this.pageConfig[0].treeGrid['dataSource'] = []
        this.$nextTick(() => {
          this.$set(this.pageConfig[0].treeGrid, 'dataSource', _detailGridList)
        })
      })
    },
    //表格顶部按钮点击
    handleClickToolBar(e) {
      let _selectRecords = e.treeGrid.getSelectedRecords()
      console.log('use-handleClickToolBar', e, _selectRecords)
      if (e.toolbar.id == 'Add') {
        //新增维度操作
        this.handleAddDimension()
        return
      }
    },
    //单元格icons，点击
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      const _data = e.data
      if (_data.dimensionId && _data.dimensionName) {
        if (e.tool.id == 'Add') {
          //新增指标操作
          this.handleAddIndex(e)
        } else if (e.tool.id == 'Edit') {
          //编辑维度操作
          this.handleEditDimension(e)
        } else if (e.tool.id == 'Delete') {
          //删除维度操作
          this.handleDeleteDimension(e.data)
        }
      } else if (_data.indexId && _data.indexName) {
        if (e.tool.id == 'Edit') {
          //编辑指标操作
          this.handleEditIndex(e)
        } else if (e.tool.id == 'Delete') {
          //删除指标操作
          this.handleDeleteIndex(e.data)
        }
      }
    },
    // 单元格title，点击
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
    },
    //设置维度分值
    setDimensionScore(w) {
      if (+w) {
        return (+this.formObject.score * +w) / 100
      } else {
        return 0
      }
    },
    //设置指标分值
    setIndexScore(dW, iW) {
      if (+dW && +iW) {
        return (+this.formObject.score * +dW * +iW) / 10000
      } else {
        return 0
      }
    },
    //新增维度操作
    handleAddDimension(e) {
      console.log('handleAddDimension--', e)
      let _dimensionList = [...this.dimensionList]
      let _usedDimensions = [], //当前列表页，已经使用过的维度ID列表
        _unUseDimensions = [] //未使用的维度列表
      let _detailList = [...this.pageConfig[0].treeGrid['dataSource']]
      _detailList.forEach((d) => {
        _usedDimensions.push(d.dimensionId)
      })
      _dimensionList.forEach((d) => {
        if (_usedDimensions.indexOf(d.dimensionId) < 0) {
          _unUseDimensions.push(d)
        }
      })
      if (_unUseDimensions.length) {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/supplierPerformance/scoreSetting/template/components/detail/components/dimension" */ './components/dimension.vue'
            ),
          data: {
            title: this.$t('添加维度信息'),
            dimensionList: _unUseDimensions
          },
          success: (data) => {
            this.editStatus = true //执行过数据编辑
            let _data = {
              showDeleteIcon: true,
              id: data.dimensionId,
              name: data.dimensionName,
              dimensionId: data.dimensionId,
              dimensionName: data.dimensionName,
              indexList: [],
              weight: data.weight,
              allocateScore: null
            }
            _data.allocateScore = this.setDimensionScore(data.weight)
            //以下几个字段, 维度列表没有，需要补全
            _data.indexFullScore = null //指标满分
            _data.indexFromSource = null //指标来源
            _data.scoreStandard = null //评分标准
            let _dataSource = [...this.pageConfig[0].treeGrid['dataSource']]
            _dataSource.push(_data)
            this.$set(this.pageConfig[0].treeGrid, 'dataSource', _dataSource)
          }
        })
      } else {
        this.$toast({
          content: this.$t('所有维度均已配置'),
          type: 'warning'
        })
      }
    },
    //编辑维度操作
    handleEditDimension(e) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/supplierPerformance/scoreSetting/template/components/detail/components/dimension" */ './components/dimension.vue'
          ),
        data: {
          title: this.$t('修改维度信息'),
          data: e.data
        },
        success: (data) => {
          this.editStatus = true //执行过数据编辑
          let _dataSource = [...this.pageConfig[0].treeGrid['dataSource']]
          _dataSource.forEach((e) => {
            if (e.dimensionId === data.dimensionId) {
              e.weight = data.weight
              e.allocateScore = this.setDimensionScore(e.weight)
              if (Array.isArray(e.indexList) && e.indexList.length) {
                //重置维度下的指标分值
                e.indexList.forEach((i) => {
                  i.allocateScore = this.setIndexScore(e.weight, i.weight)
                })
              }
            }
          })
          this.$set(this.pageConfig[0].treeGrid, 'dataSource', _dataSource)
        }
      })
    },
    //删除维度操作
    handleDeleteDimension(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除当前维度？')
        },
        success: () => {
          this.editStatus = true //执行过数据编辑
          let _dataSource = [...this.pageConfig[0].treeGrid['dataSource']]
          let _index = _dataSource.findIndex((e) => e.dimensionId === data.dimensionId)
          _dataSource.splice(+_index, 1)
          this.$set(this.pageConfig[0].treeGrid, 'dataSource', _dataSource)
        }
      })
    },
    //新增指标操作
    handleAddIndex(e) {
      let _indexListParams = {
        dimensionId: e.data.dimensionId,
        ownerOrgId: this.formObject.ownerOrgId
      }
      this.$API.performanceScoreSetting.indexSelectList(_indexListParams).then((res) => {
        let _indexList = []
        if (res && res.data && Array.isArray(res.data)) {
          _indexList = res.data
        } else {
          _indexList = []
        }

        if (_indexList.length < 1) {
          this.$toast({
            content: this.$t('当前维度下，未设置指标。或者指标未设置"启用"'),
            type: 'warning'
          })
          return
        }

        let _usedIndexList = [], //当前维度下，已经使用过的指标ID列表
          _unUseIndexList = [] //当前维度下，未使用的指标列表
        let _detailList = [...this.pageConfig[0].treeGrid['dataSource']]
        _detailList.forEach((f) => {
          if (f.dimensionId === e.data.dimensionId) {
            //遍历数据，寻找到当前维度数据
            f.indexList.forEach((i) => {
              _usedIndexList.push(i.indexId)
            })
          }
        })
        _indexList.forEach((d) => {
          if (_usedIndexList.indexOf(d.indexId) < 0) {
            _unUseIndexList.push(d)
          }
        })
        if (_unUseIndexList.length) {
          this.$dialog({
            modal: () =>
              import(
                /* webpackChunkName: "router/supplierPerformance/scoreSetting/template/components/detail/components/target" */ './components/target.vue'
              ),
            data: {
              title: this.$t('添加指标信息'),
              indexList: _unUseIndexList,
              parent: e.data
            },
            success: (data) => {
              this.editStatus = true //执行过数据编辑
              let _data = {
                showDeleteIcon: true,
                id: data.indexId,
                name: data.indexName,
                indexId: data.indexId,
                indexName: data.indexName,
                weight: data.weight,
                allocateScore: null,
                indexFullScore: data.indexFullScore,
                indexFromSource: null,
                scoreStandard: data.scoreStandard
              }
              let _dataSource = [...this.pageConfig[0].treeGrid['dataSource']]
              _dataSource.forEach((f) => {
                if (f.dimensionId === e.data.dimensionId) {
                  _data.parentDimensionId = e.data.dimensionId
                  _data.parentDimensionName = e.data.dimensionName
                  _data.allocateScore = this.setIndexScore(f.weight, data.weight)
                  f.indexList.push(_data)
                }
              })
              this.$set(this.pageConfig[0].treeGrid, 'dataSource', _dataSource)
            }
          })
        } else {
          this.$toast({
            content: this.$t('当前维度下，所有指标均已配置'),
            type: 'warning'
          })
        }
      })
    },
    //编辑指标操作
    handleEditIndex(e) {
      console.log('handleEditIndex---', e)
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/supplierPerformance/scoreSetting/template/components/detail/components/target" */ './components/target.vue'
          ),
        data: {
          title: this.$t('编辑指标信息'),
          data: e.data
        },
        success: (data) => {
          this.editStatus = true //执行过数据编辑
          let _dataSource = [...this.pageConfig[0].treeGrid['dataSource']]
          _dataSource.forEach((f) => {
            if (f.dimensionId === data.parentDimensionId) {
              f.indexList.forEach((i) => {
                if (i.indexId === data.indexId) {
                  i.weight = data.weight
                  i.allocateScore = this.setIndexScore(f.weight, i.weight)
                }
              })
            }
          })
          this.$set(this.pageConfig[0].treeGrid, 'dataSource', _dataSource)
        }
      })
    },
    //删除指标操作
    handleDeleteIndex(data) {
      console.log('handleDeleteIndex---', data)
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除当前指标？')
        },
        success: () => {
          this.editStatus = true //执行过数据编辑
          let _dataSource = [...this.pageConfig[0].treeGrid['dataSource']]
          _dataSource.forEach((f) => {
            if (f.dimensionId === data.parentDimensionId) {
              let _indexList = [...f.indexList]
              let _index = _indexList.findIndex((e) => e.indexId === data.indexId)
              _indexList.splice(+_index, 1)
              f.indexList = _indexList
            }
          })
          this.$set(this.pageConfig[0].treeGrid, 'dataSource', _dataSource)
        }
      })
    },
    //校验提交参数
    checkSaveParams(_list) {
      //外层：维度权重值之和=100
      //内层：每个维度下，各个指标权重值之和=100
      const totalList = (list, key = 'weight') =>
        list.reduce((pre, cur) => {
          return +pre + +cur[key]
        }, 0)
      if (Array.isArray(_list) && _list.length) {
        let _errorList = []
        let _totalWeight = totalList(_list)
        if (_totalWeight !== 100) {
          _errorList.push(this.$t('所有维度权重累加不等于100。'))
        }
        _list.forEach((e) => {
          {
            if (Array.isArray(e.indexList) && e.indexList.length) {
              let _totalWeight = totalList(e.indexList)
              if (_totalWeight !== 100) {
                _errorList.push(
                  `【${e.dimensionName}】` + this.$t('维度中，各指标权重累加不等于100。')
                )
              }
            }
          }
        })
        return _errorList
      } else {
        return [this.$t('未设置任何维度')]
      }
    },
    confirm() {
      //遍历数据，移除无用的临时字段
      let _dataSource = JSON.parse(JSON.stringify(this.pageConfig[0].treeGrid['dataSource']))
      _dataSource.forEach((e) => {
        delete e.id //统一字段名，用于列表显示
        delete e.name //统一字段名，用于列表显示
        delete e.showDeleteIcon //是否显示删除按钮
        delete e.indexFullScore //指标满分
        delete e.indexFromSource //指标来源
        delete e.scoreStandard //评分标准
        e.indexList.forEach((i) => {
          delete i.id //统一字段名，用于列表显示
          delete i.name //统一字段名，用于列表显示
          delete i.showDeleteIcon //是否显示删除按钮
          delete i.parentDimensionId //上级对应的维度ID
          delete i.parentDimensionName //上级对应的维度ID
        })
      })
      let _res = this.checkSaveParams(_dataSource)
      if (_res.length) {
        _res.forEach((e) => {
          setTimeout(() => {
            this.$toast({
              content: e,
              type: 'warning'
            })
          }, 200)
        })
      } else {
        let params = {
          dimensionList: _dataSource,
          ownerOrgId: this.formObject.ownerOrgId,
          templateId: this.formObject.id
        }
        this.$API.performanceScoreSetting.saveTemplateDetal(params).then(() => {
          this.$emit('confirm-function')
        })
      }
    },
    cancel() {
      if (this.editStatus) {
        //本页面，执行过数据修改操作。
        this.$dialog({
          data: {
            title: this.$t('数据未保存'),
            message: this.$t('数据未保存，是否执行保存？'),
            buttonText: [this.$t('返回(不保存)'), this.$t('保存')]
          },
          success: () => {
            //保存数据
            this.confirm()
          },
          failed: () => {
            //不保存
            this.$emit('cancel-function')
          }
        })
      } else {
        this.$emit('cancel-function')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.tempalte-detail {
  position: absolute;
  background: transparent;
  z-index: 1001;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  height: 100vh;
  width: 100vw;
  padding: 0;

  .page-top {
    height: 60px;
    flex-shrink: 0;
  }
  .page-bottom {
    flex: 1;
    .page-left {
      width: 250px;
      flex-shrink: 0;
    }
  }

  .page-content {
    background: #fff;
    flex: 1;
    padding: 20px;
    flex-direction: column;
  }
  .detail-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 30px 30px 20px 30px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid #e8e8e8ff;
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    .right_but {
      display: inline-block;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #00469cff;
      margin: 0 10px;
    }
    .left {
      color: #292929;
      .title {
        font-size: 20px;
        font-weight: 600;
      }
      .form {
        padding: 10px 0;
        font-size: 12px;
        font-weight: normal;
      }
      .form_div {
        display: inline-block;
        padding-right: 20px;
      }
      .data {
        padding: 10px 0;
        font-size: 14px;
        font-weight: 600;
        span {
          margin-left: 3px;
          color: #00469c;
        }
      }
    }
  }
  .detail-content {
    background: #e8e8e8;
    /deep/.e-grid {
      .e-rowcell {
        text-align: left !important;
        .grid-edit-column {
          display: inline-block !important;
        }
      }
    }
  }
}
</style>
