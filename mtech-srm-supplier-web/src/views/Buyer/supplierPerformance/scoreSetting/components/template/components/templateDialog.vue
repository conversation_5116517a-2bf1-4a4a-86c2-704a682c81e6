<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="templateName" :label="$t('模板名称：')">
          <mt-input
            v-model="formObject.templateName"
            float-label-type="Never"
            :placeholder="$t('请输入考评模板名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="ownerOrgName" :label="$t('所属组织：')">
          <mt-input
            :disabled="true"
            v-model="formObject.ownerOrgName"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="score" :label="$t('满分分值：')">
          <mt-inputNumber
            ref="fullScoreRef"
            :min="1"
            v-model="formObject.score"
            @change="changeFullScore"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item prop="templateTypeId" :label="$t('模板类型：')">
          <mt-select
            ref="templateTypeRef"
            v-model="formObject.templateTypeId"
            float-label-type="Never"
            :data-source="templateTypeList"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择模板类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('备注：')" prop="remark">
          <mt-input
            css-class="e-outline"
            :multiline="true"
            :rows="3"
            type="text"
            float-label-type="Never"
            v-model="formObject.remark"
            maxlength="200"
            :placeholder="$t('请输入备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmDetail,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      formObject: {
        id: null, //配置Id
        ownerOrgCode: null, //所属组织编码
        ownerOrgId: null, //	所属组织id
        ownerOrgName: null, //所属组织名称
        remark: null, //备注
        score: 100, //满分
        templateName: null, //模板名称
        templateTypeId: null, //模板类型 id
        templateTypeCode: null, //模板类型 code
        templateTypeName: null //模板类型 name
      },
      formRules: {
        templateName: [
          { required: true, message: this.$t('请输入考评模板名称'), trigger: 'blur' },
          {
            min: 0,
            max: 30,
            message: this.$t('长度在1到30个字符'),
            trigger: 'blur'
          }
        ],
        templateTypeId: [{ required: true, message: this.$t('请选择模板类型'), trigger: 'blur' }]
      },
      templateTypeList: [],
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      let _data = { ...this.modalData.data }
      let { id, remark, score, templateName, templateTypeId } = _data
      this.formObject.id = id
      this.formObject.remark = remark
      this.formObject.score = score
      this.formObject.templateName = templateName
      this.formObject.templateTypeId = templateTypeId
      this.buttons = [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
    this.templateTypeList = this.modalData.templateTypeList
    if (!this.editStatus) {
      //如果是新增操作，默认赋值第一条数据
      if (this.templateTypeList.length) {
        this.formObject.templateTypeId = this.templateTypeList[0]['id']
      }
    }
    this.getUserInfoDetail()
  },
  methods: {
    getUserInfoDetail() {
      this.$API.performanceScoreSetting.getUserDetail().then((res) => {
        let { data } = res
        let { companyOrg } = data
        this.formObject.ownerOrgId = companyOrg.id
        this.formObject.ownerOrgName = companyOrg.orgName
        this.formObject.ownerOrgCode = companyOrg.orgCode
      })
    },
    changeFullScore(e) {
      this.formObject.score = e
    },
    saveTemplate(type) {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          //处理下拉框数据赋值
          this.$utils.assignDataFromRefs(params, [
            {
              key: 'templateTypeId', //模板类型 templateTypeId下拉框数据
              ref: this.$refs.templateTypeRef.ejsRef,
              fields: {
                templateTypeCode: 'itemCode',
                templateTypeName: 'itemName'
              }
            }
          ])
          if (this.editStatus) {
            this.$API.performanceScoreSetting.editTemplate(params).then((res) => {
              if (res.code == 200) {
                this.$emit('confirm-function', {
                  type,
                  data: { id: res.data }
                })
              }
            })
          } else {
            delete params.id
            this.$API.performanceScoreSetting.addTemplate(params).then((res) => {
              if (res.code == 200) {
                this.$emit('confirm-function', {
                  type,
                  data: { id: res.data }
                })
              }
            })
          }
        }
      })
    },
    confirm() {
      this.saveTemplate('close')
    },
    confirmDetail() {
      this.saveTemplate('detail')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
