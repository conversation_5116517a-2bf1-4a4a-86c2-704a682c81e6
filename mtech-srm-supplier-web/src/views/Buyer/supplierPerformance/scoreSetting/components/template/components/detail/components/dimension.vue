<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="dimensionName" :label="$t('所属维度：')" v-if="editStatus">
          <mt-input
            :disabled="true"
            :show-clear-button="false"
            v-model="formObject.dimensionName"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="dimensionId" :label="$t('所属维度：')" v-else>
          <mt-select
            ref="dimensionRef"
            v-model="formObject.dimensionId"
            float-label-type="Never"
            :data-source="dimensionList"
            :fields="{ text: 'dimensionName', value: 'dimensionId' }"
            :placeholder="$t('所属维度')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="weight" :label="$t('权重（%）：')">
          <mt-inputNumber
            ref="weightRef"
            :min="1"
            :max="100"
            v-model="formObject.weight"
            :placeholder="$t('输入权重') + '1~100'"
            @change="changeWeight"
          ></mt-inputNumber>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: { weight: null, dimensionId: null, dimensionName: null },
      formRules: {
        weight: [{ required: true, message: this.$t('请设置权重'), trigger: 'blur' }]
      },
      dimensionList: [],
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
    if (this.modalData && this.modalData.dimensionList) {
      this.dimensionList = this.modalData.dimensionList
      if (!this.editStatus) {
        //如果是新增操作，默认赋值第一条数据
        if (this.dimensionList.length) {
          this.formObject.dimensionId = this.dimensionList[0]['dimensionId']
        }
      }
    }
  },
  methods: {
    changeWeight(e) {
      this.formObject.weight = e
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.formObject))
          if (params.weight && params.weight > 0 && params.weight <= 100) {
            if (this.editStatus) {
              this.$emit('confirm-function', params)
            } else {
              //新增操作，可以下拉选择维度
              this.$utils.assignDataFromRef(
                params,
                'dimensionId',
                this.$refs.dimensionRef.ejsRef,
                'dimensionName'
              )
              this.$emit('confirm-function', params)
            }
          } else {
            this.$toast({
              content: this.$t('权重设置有误'),
              type: 'warning'
            })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
