<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="templateName" :label="$t('模板名称：')">
          <mt-input
            v-model="formObject.templateName"
            float-label-type="Never"
            :placeholder="$t('请输入考评模板名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="ownerOrgId"
          :label="$t('所属组织：')"
          v-if="companyTreeFields.dataSource.length"
        >
          <mt-DropDownTree
            ref="ownerOrgNameRef"
            v-if="companyTreeFields.dataSource.length"
            :placeholder="$t('请选择所属组织')"
            v-model="formObject.ownerOrgId"
            :fields="companyTreeFields"
            :show-clear-button="false"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="score" :label="$t('满分分值：')">
          <mt-inputNumber
            ref="fullScoreRef"
            :min="1"
            v-model="formObject.score"
            @change="changeFullScore"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item prop="templateTypeId" :label="$t('模板类型：')">
          <mt-select
            ref="templateTypeRef"
            v-model="formObject.templateTypeId"
            float-label-type="Never"
            :data-source="templateTypeList"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择模板类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('备注：')" prop="remark">
          <mt-input
            css-class="e-outline"
            :multiline="true"
            :rows="3"
            type="text"
            float-label-type="Never"
            v-model="formObject.remark"
            maxlength="200"
            :placeholder="$t('请输入备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmDetail,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      formObject: {
        id: null, //配置Id
        ownerOrgCode: null, //所属组织编码
        ownerOrgId: null, //	所属组织id
        ownerOrgName: null, //所属组织名称
        remark: null, //备注
        score: 100, //满分
        templateName: null, //模板名称
        templateTypeId: null, //模板类型 id
        templateTypeCode: null, //模板类型 code
        templateTypeName: null //模板类型 name
      },
      formRules: {
        templateName: [{ required: true, message: this.$t('请输入指标名称'), trigger: 'blur' }],
        templateTypeId: [{ required: true, message: this.$t('请选择模板类型'), trigger: 'blur' }],
        // remark: [
        //   { min: 0,max: 20,message: '长度在 200 个字符以内123', trigger: 'blur' }
        // ],
        ownerOrgId: [{ required: true, message: this.$t('请选择所属组织'), trigger: 'blur' }]
      },
      templateTypeList: [],
      companyTreeList: [], //组织数据--树形
      flatCompanyTreeList: [], //组织数据--平铺
      companyTreeFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      let _data = { ...this.modalData.data }
      this.formObject = {
        id: _data.id, //配置Id
        ownerOrgCode: _data.ownerOrgCode, //所属组织编码
        ownerOrgId: [_data.ownerOrgId], //	所属组织id
        ownerOrgName: _data.ownerOrgName, //所属组织名称
        remark: _data.remark, //评分标准简介
        score: _data.score, //满分
        templateName: _data.templateName, //模板名称
        templateTypeId: _data.templateTypeId //模板类型
      }
      this.buttons = [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
    this.templateTypeList = this.modalData.templateTypeList
    if (!this.editStatus) {
      //如果是新增操作，默认赋值第一条数据
      if (this.templateTypeList.length) {
        this.formObject.templateTypeId = this.templateTypeList[0]['id']
      }
    }
    this.getCompanyTree()
  },
  methods: {
    changeFullScore(e) {
      this.formObject.score = e
    },
    getCompanyTree() {
      this.$API.performanceScoreSetting
        .getStatedLimitTree({
          orgLevelCode: 'ORG02',
          orgType: 'ORG001PRO'
        })
        .then((res) => {
          let _data = []
          if (res && res.data && Array.isArray(res.data)) {
            _data = res.data
          }
          this.companyTreeList = _data
          this.flatCompanyTreeList = this.modalData.flatTreeData(_data)
          this.companyTreeFields.dataSource = []
          this.$nextTick(() => {
            this.$set(this.companyTreeFields, 'dataSource', _data)
          })
        })
    },
    saveTemplate(type) {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (params.ownerOrgId) {
            //根据当前选择的ownerOrgId数组，获取ownerOrgName数组
            if (Array.isArray(params.ownerOrgId) && params.ownerOrgId.length) {
              let _ids = [...params.ownerOrgId],
                _names = [],
                _codes = []
              _ids.forEach((e) => {
                let _find = this.flatCompanyTreeList.find((f) => e === f.id)
                _names.push(_find.name)
                _codes.push(_find.orgCode)
              })
              params.ownerOrgId = _ids.join(',') //	所属组织id
              params.ownerOrgName = _names.join(',') //所属组织名称
              params.ownerOrgCode = _codes.join(',') //所属组织编码
            }
          }
          //处理下拉框数据赋值
          this.$utils.assignDataFromRefs(params, [
            {
              key: 'templateTypeId', //模板类型 templateTypeId下拉框数据
              ref: this.$refs.templateTypeRef.ejsRef,
              fields: {
                templateTypeCode: 'itemCode',
                templateTypeName: 'itemName'
              }
            }
          ])
          if (this.editStatus) {
            this.$API.performanceScoreSetting.editTemplate(params).then((res) => {
              if (res.code == 200) {
                this.$emit('confirm-function', {
                  type,
                  data: { id: res.data }
                })
              }
            })
          } else {
            delete params.id
            this.$API.performanceScoreSetting.addTemplate(params).then((res) => {
              if (res.code == 200) {
                this.$emit('confirm-function', {
                  type,
                  data: { id: res.data }
                })
              }
            })
          }
        }
      })
    },
    confirm() {
      this.saveTemplate('close')
    },
    confirmDetail() {
      this.saveTemplate('detail')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
