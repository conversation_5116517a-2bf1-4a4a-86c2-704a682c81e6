<template>
  <mt-dialog
    ref="dialog"
    :header="dialogTitle"
    css-class="create-proj-dialog"
    :buttons="buttons"
    @close="cancel"
    :open="onOpen"
  >
    <div class="form-dialog">
      <AddHeadFirst
        v-if="!rowId || (!!rowId && effectiveData)"
        ref="addHeadFirst"
        :data="effectiveData"
        :is-edit="!!rowId"
        :project-code="projectCode"
        :first-data="firstData"
        :task-code="taskCode"
        v-show="step === 1"
      />
      <AddHeadSecond
        ref="addHeadSecond"
        :first-data="firstAuthData"
        :first-datas="firstData"
        v-show="step === 2"
      />
    </div>
  </mt-dialog>
</template>
<script>
import AddHeadFirst from './addHeadFirst.vue'
import AddHeadSecond from './addHeadSecond.vue'

export default {
  components: {
    AddHeadFirst,
    AddHeadSecond
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: this.$t('新增生效'),
      step: 1, // 当前步骤
      firstStepData: {},
      firstAuthData: {},
      secondStepData: {},
      effectiveData: undefined,
      firstData: {}
    }
  },
  computed: {
    buttons() {
      if (this.step === 1) {
        return [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirmFirstStep,
            buttonModel: { isPrimary: 'true', content: this.$t('下一步') }
          }
        ]
      } else {
        return [
          {
            click: this.cancelSecondStep,
            buttonModel: { content: this.$t('上一步') }
          },
          {
            click: this.confirmSecondStep,
            buttonModel: { isPrimary: 'true', content: this.$t('下一步') }
          }
        ]
      }
    },
    rowId() {
      return this.modalData.rowId || ''
    },
    projectCode() {
      return this.modalData.projectCode
    },
    taskCode() {
      return this.modalData.taskCode
    }
  },
  async created() {
    console.log('projectCode', this.projectCode, this.rowId)
    if (this.rowId) {
      this.getDetailData()
    }
  },
  mounted() {
    document.body.style.overflow = 'hidden'
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    cancel() {
      this.$refs['dialog'].ejsRef.hide()
      document.body.style.overflow = 'scroll'
    },
    cancelSecondStep() {
      this.step = 1
    },
    confirmFirstStep() {
      this.$refs.addHeadFirst.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.projectCode) {
            let _temp = this.$refs.addHeadFirst.getData()
            this.firstStepData = _temp.data
            this.firstAuthData = _temp.response
            this.confirmAdd()
          } else {
            this.$refs.addHeadFirst.next().then((result) => {
              if (result.response) {
                this.firstStepData = result.data
                this.firstAuthData = result.response
              }
              this.step = 2
            })
          }
        }
      })
    },
    confirmSecondStep() {
      this.$refs.addHeadSecond.next().then((result) => {
        this.secondStepData = result
        this.confirmAdd()
      })
    },

    confirmAdd() {
      const param = this.formatConfirmParam()
      console.log(param)
      this.$API.supplierEffective.addEffective(param).then((res) => {
        console.log(res)
        if (res.code === 200 && res.data) {
          this.goDetail(res.data.infoDTO?.id)
        }
      })
    },

    formatConfirmParam() {
      return {
        effectiveDTO: {
          ...this.firstStepData,
          authProjectCode: this.firstAuthData.authProjectCode,
          authProjectId: this.firstAuthData.authProjectId,
          authProjectName: this.firstAuthData.authProjectName,
          categoryRelationCode: this.firstAuthData.categoryRelationCode,
          categoryRelationId: this.firstAuthData.categoryRelationId,
          partnerRelationId: this.firstAuthData?.partnerRelationId
            ? this.firstAuthData.partnerRelationId
            : this.firstStepData.partnerRelationId,
          partnerRelationCode: this.firstAuthData?.partnerRelationCode
            ? this.firstAuthData.partnerRelationCode
            : this.firstStepData.partnerRelationCode
        },
        infoDTO: {
          applyName: this.$t('生效申请单')
        }
      }
    },

    goDetail(id) {
      this.$emit('confirm-function', id)
    },

    getDetailData() {
      this.$API.supplierEffective.getDetail(this.rowId).then((res) => {
        if (res.code === 200) {
          this.formObject = res.data
          this.effectiveData = res.data.effectiveDTO || {}
        } else {
          this.$toast({ content: res.data.msg, type: 'error' })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.form-dialog {
  padding: 32px 6px 0 6px !important;

  /deep/ .active {
    background-color: rgb(17, 147, 255) !important;
  }
}
</style>
