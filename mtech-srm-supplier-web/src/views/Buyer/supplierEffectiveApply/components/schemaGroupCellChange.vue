<template>
  <div class="detail-grid-cell-template" ref="templateRef">
    <mt-select
      v-if="field === 'schemeGroup'"
      id="schemeGroup"
      v-model="data.schemeGroup"
      float-label-type="Never"
      :allow-filtering="true"
      :filtering="(e) => filterSchema(e, schemeGroupList, 'schemaGroupName')"
      :data-source="schemaList"
      :fields="{
        value: 'schemaGroupCode',
        text: 'schemaGroupName'
      }"
      @change="schemeGroupChange"
    ></mt-select>
  </div>
</template>

<script>
import bus from '../config/bus'
import utils from '@/utils/utils'

export default {
  components: {},
  data() {
    return {}
  },
  computed: {
    field() {
      return this.data.column.field
    }
  },
  watch: {
    schemaList(newVal) {
      this.$refs.templateRef.querySelector('#schemeGroup').updateData(newVal)
    }
  },
  mounted() {
    this.querySchemaGroup(this.data.purchaseOrgCode)
    bus.$on('changeCell', this.changeExtData)
  },
  methods: {
    changeExtData(field, value) {
      console.log('123345345345', field, value, this.data.schemaList)
      this.data[field] = value
      this.schemaList = value
      this.$forceUpdate() //强制更新
    },
    // 方案组接口
    querySchemaGroup(value) {
      this.$API.supplierEffective
        .querySchemaGroup({
          businessOrganizationCode: value
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            res.data.map((item) => {
              item.schemaGroupName = item.schemaGroupCode + '-' + item.schemaGroupName
            })
            this.schemeGroupList = res.data || []
            this.$set(this.data, 'schemaList', res.data)
            bus.$emit('changeCell', 'schemaList', res.data)
            console.log('onChangePurchaseOrgCodeonChangePurchaseOrgCode', this.data.schemaList)
          }
        })
    },
    schemeGroupChange(event) {
      const { itemData } = event
      if (JSON.stringify(itemData) == JSON.stringify({})) {
        return
      }
      if (itemData.schemaGroupName == undefined) {
        bus.$emit('changeExt', 'schemeGroupName', '')
        return
      }
      bus.$emit('changeExt', 'schemeGroupName', itemData.schemaGroupName)
    }
  }
}
</script>

<style lang="scss" scoped></style>
