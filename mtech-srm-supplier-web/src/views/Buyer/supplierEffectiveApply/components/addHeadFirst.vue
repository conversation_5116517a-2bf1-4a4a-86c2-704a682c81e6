<template>
  <mt-form ref="ruleForm" :model="headFormData" :rules="rules">
    <mt-form-item prop="orgId" :label="$t('公司')">
      <!-- <mt-select
        v-model="headFormData.orgId"
        float-label-type="Never"
        :data-source="orgList"
        :fields="{ text: 'orgName', value: 'id' }"
        :placeholder="$t('请选择公司')"
        @change="orgChange"
        v-if="!projectCode"
      ></mt-select> -->
      <RemoteAutocomplete
        v-if="!projectCode"
        :params="{
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true
        }"
        v-model="headFormData.orgCode"
        :fields="{ text: 'orgName', value: 'orgCode' }"
        @change="orgChange"
        url="/masterDataManagement/tenant/organization/specified-level-paged-query"
        :title-switch="false"
        :placeholder="$t('请选择')"
        select-type="administrativeCompany"
      ></RemoteAutocomplete>
      <mt-input v-model="headFormData.orgName" v-if="projectCode" :disabled="true"> </mt-input>
    </mt-form-item>

    <mt-form-item prop="effectiveType" :label="$t('生效类型')">
      <mt-select
        v-model="headFormData.effectiveType"
        float-label-type="Never"
        :data-source="effectiveTypeList"
        :placeholder="$t('请选择生效类型')"
        @change="setEffectiveMode"
        v-if="!taskCode"
      ></mt-select>
      <mt-input v-model="effectiveTypeName" v-if="taskCode" :disabled="true"> </mt-input>
    </mt-form-item>
    <mt-form-item prop="partnerRelationCode" :label="$t('供应商')">
      <mt-select
        v-model="headFormData.partnerRelationCode"
        float-label-type="Never"
        :data-source="supplierList"
        :fields="{
          text: 'supplierEnterpriseName',
          value: 'partnerRelationCode'
        }"
        :allow-filtering="true"
        :filtering="filteringSupplier"
        :placeholder="$t('请选择供应商')"
        @change="supplierEnterpriseChange"
      ></mt-select>
    </mt-form-item>
    <mt-form-item prop="categoryId" :label="$t('品类')">
      <mt-select
        v-model="headFormData.categoryId"
        float-label-type="Never"
        :data-source="categoryList"
        :fields="{ text: 'categoryName', value: 'categoryId' }"
        :placeholder="$t('请选择品类')"
        @change="categoryChange"
        v-if="!projectCode"
      ></mt-select>
      <mt-input v-model="headFormData.categoryName" v-if="projectCode" :disabled="true"> </mt-input>
    </mt-form-item>
    <mt-form-item prop="sceneDefineId" :label="$t('引入场景')">
      <mt-select
        v-model="headFormData.sceneDefineId"
        float-label-type="Never"
        :data-source="scenesList"
        :fields="{ text: 'sceneName', value: 'id' }"
        :placeholder="$t('请选择引入场景')"
        @change="sceneDefineChange"
        v-if="!projectCode"
      ></mt-select>
      <mt-input v-model="headFormData.sceneDefineName" v-if="projectCode" :disabled="true">
      </mt-input>
    </mt-form-item>
    <mt-form-item
      v-if="(taskCode && taskCode !== 'preEffectiveTempType') || isShowEffectiveMode"
      prop="effectiveMode"
      :label="$t('生效方式')"
    >
      <mt-select
        v-model="headFormData.effectiveMethod"
        float-label-type="Never"
        :data-source="effectiveMethodList"
        :placeholder="$t('请选择生效方式')"
      />
    </mt-form-item>
  </mt-form>
</template>
<script>
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    RemoteAutocomplete
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {}
    },
    projectCode: {
      type: String,
      default: ''
    },
    taskCode: {
      type: String,
      default: ''
    },
    firstData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isShowEffectiveMode: false, // 选择生效类型之后 判断是否显示生效方式下拉
      effectiveTypeName: null,
      headFormData: {
        effectiveMethod: 10
      },
      rules: {
        orgId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        effectiveType: [
          {
            required: true,
            message: this.$t('请选择生效类型'),
            trigger: 'blur'
          }
        ],
        partnerRelationCode: [
          { required: true, message: this.$t('请选择供应商'), trigger: 'blur' }
        ],
        categoryId: [{ required: true, message: this.$t('请选择品类'), trigger: 'blur' }],
        sceneDefineId: [
          {
            required: true,
            message: this.$t('请选择引入场景'),
            trigger: 'blur'
          }
        ],
        effectiveMethod: [{ required: true, message: this.$t('请选择生效方式'), trigger: 'blur' }]
      },
      // orgList: [], // 公司列表
      supplierList: [], // 供应商列表
      categoryList: [], // 品类列表
      scenesList: [], // 场景列表
      effectiveTypeList: [
        { text: this.$t('预生效'), value: '1' },
        { text: this.$t('正式生效'), value: '2' }
      ],
      effectiveMethodList: [
        { text: this.$t('线下采委会审批'), value: 10 },
        { text: this.$t('线上审批'), value: 20 }
      ]
    }
  },
  async created() {
    // this.getSceneList();
    // const userDetail = await this.getUserDetail();
    // if (userDetail && userDetail.id) {
    //   this.getChildrenCompanyOrganization(userDetail.id);
    // }
    console.log('loglogloglog', this.data, this.projectCode, this.taskCode, this.firstData)
    if (this.data && this.data.id) {
      this.headFormData = Object.assign({}, this.data)
    }
    if (this.projectCode) {
      this.getCateSupplierList()
    }
    if (this.taskCode) {
      if (this.taskCode === 'effectiveTempType') {
        this.headFormData.effectiveType = '2'
        this.effectiveTypeName = this.$t('正式生效')
      } else {
        this.headFormData.effectiveType = '1'
        this.effectiveTypeName = this.$t('预生效')
      }
    }
  },
  methods: {
    // 设置生效方式下拉显示隐藏
    setEffectiveMode(e) {
      console.log('logloglogloglog123', e)
      if (e.itemData.value == '2') {
        this.isShowEffectiveMode = true
      } else {
        this.isShowEffectiveMode = false
      }
    },
    filteringSupplier(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          this.supplierList.filter((item) => {
            if (item?.supplierEnterpriseName.indexOf(e.text) > -1) {
              return true
            } else {
              return false
            }
          })
        )
      } else {
        e.updateData(this.supplierList)
      }
    },
    // 处理公司接口响应数据（处理返回的接口数据，会把接口请求的数据返回，然后数据处理完以后请用return返回）
    handleCompanyData(resData = []) {
      resData = resData.filter((item) => {
        return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
      })
      return resData
    },
    // 品类认证页面获取供应商列表
    getCateSupplierList() {
      this.$API.CategoryCertification.getSupListByAuthCode({
        authProjectCode: this.projectCode
      }).then((res) => {
        console.log('getSupListByAuthCode', res)
        if (res.code == 200 && !utils.isEmpty(res.data)) {
          this.supplierList = res.data
        } else {
          this.supplierList = []
        }
      })
    },
    // getUserDetail() {
    //   return this.$API.supplierPurchaseData.queryUserDetail().then((res) => {
    //     let { data } = res;
    //     let { companyOrg } = data;
    //     return companyOrg;
    //   });
    // },
    // getChildrenCompanyOrganization(userId) {
    //   // this.$API.supplierInvitation["getChildrenCompanyOrganization"]({
    //   //   organizationId: userId,
    //   // }).then((result) => {
    //   this.$API.supplierInvitation["getChildrenCompanyOrganization2"]({
    //     organizationLevelCodes: ["ORG02", "ORG01"],
    //     orgType: "ORG001PRO",
    //     includeItself: true,
    //   }).then((result) => {
    //     if (result.code === 200 && !utils.isEmpty(result.data)) {
    //       this.orgList = result.data.filter((item) => {
    //         return (
    //           item.orgLevelTypeCode === "ORG02" ||
    //           item.orgLevelTypeCode === "ORG01"
    //         );
    //       });
    //     } else {
    //       this.orgList = [];
    //     }
    //   });
    // },
    // STATUS_DRAFT(1, "注册"),
    // STATUS_POTENTIAL(2, "潜在"),
    // STATUS_NORMAL(10, "合格"),
    // STATUS_FROZEN(20, "冻结"),
    // STATUS_BLACK(30, "黑名单"),
    // Integer 类型 这些状态可以惩罚
    getOrgPartnerRelations(orgId, orgCode) {
      this.$API.supplierEffective
        .getOrgPartnerRelationsByStatus({
          orgId,
          orgCode,
          selectCombination: '1',
          status: [2, 10]
        })
        .then((res) => {
          if (res.code == 200 && !utils.isEmpty(res.data)) {
            let _data = cloneDeep(res.data)
            _data.forEach((item) => {
              item.supplierEnterpriseName = `${item.supplierEnterpriseName}(${item.orgCode})`
            })
            this.supplierList = _data
          } else {
            this.supplierList = []
          }
        })
    },
    getCateGoryList(id) {
      if (!id) {
        this.categoryList = []
        return
      }
      this.$API.supplierEffective
        .getCategoryPartnerRelationsByStatus({
          partnerRelationId: id,
          status: [2, 3, 10]
        })
        .then((res) => {
          if (res.code == 200 && !utils.isEmpty(res.data)) {
            this.categoryList = res.data
          } else {
            this.categoryList = []
          }
        })
    },

    getSceneList(orgId, categoryId) {
      if (!categoryId || !orgId) {
        return
      }
      this.$API.supplierEffective
        // .getSceneList({
        //   page: {
        //     pages: 1,
        //     total: 10000,
        //   },
        //   defaultRules: [
        //     {
        //       field: "status",
        //       label: this.$t("状态"),
        //       operator: "equals",
        //       type: "integer",
        //       value: 1,
        //     },
        //   ],
        // })
        .getSceneList2({
          categoryId: categoryId,
          orgId: orgId
        })
        .then((res) => {
          if (res.code == 200 && !utils.isEmpty(res.data)) {
            this.scenesList = res.data
          } else {
            this.scenesList = []
          }
        })
    },
    filterSupplier(e) {
      if (e.text) {
        let result = utils.debounce(this.getOrgPartnerRelations, 1000)
        result(e.text)
      }
    },
    orgChange(event) {
      // 有级联
      const { itemData = {} } = event
      this.headFormData.orgId = itemData?.id
      this.headFormData.orgCode = itemData?.orgCode
      this.headFormData.orgName = itemData?.orgName
      if (itemData?.id) {
        this.getOrgPartnerRelations(itemData?.id, itemData?.orgCode)
        this.getSceneList(itemData?.id, this.headFormData.categoryId)
      } else {
        this.supplierList = []
        this.scenesList = []
        this.headFormData.partnerRelationCode = ''
        this.headFormData.sceneDefineId = ''
      }
    },
    supplierEnterpriseChange(event) {
      const { itemData = {} } = event
      if (this.projectCode) {
        this.firstData.partnerArchiveId = itemData?.partnerArchiveId
        this.firstData.partnerRelationId = itemData?.partnerRelationId
        this.firstData.supplierCode = itemData?.supplierCode
        this.firstData.supplierName = itemData?.supplierName

        this.headFormData.supplierEnterpriseCode = itemData?.supplierEnterpriseCode
        this.headFormData.supplierEnterpriseName = itemData?.supplierEnterpriseName
        this.headFormData.partnerRelationId = itemData?.partnerRelationId
        // this.headFormData.partnerRelationCode = itemData?.partnerRelationCode
        this.headFormData.supplierEnterpriseId = itemData?.supplierEnterpriseId
        this.headFormData.orgId = itemData?.orgId
        this.headFormData.orgCode = itemData?.orgCode
        this.headFormData.orgName = itemData?.orgName
        this.headFormData.categoryId = itemData?.categoryId
        this.headFormData.categoryCode = itemData?.categoryCode
        this.headFormData.categoryName = itemData?.categoryName
        this.headFormData.sceneDefineId = itemData?.sceneId
        this.headFormData.sceneDefineCode = itemData?.sceneCode
        this.headFormData.sceneDefineName = itemData?.sceneName
        this.headFormData.categoryRelationCode = itemData?.categoryRelationCode
        this.headFormData.categoryRelationId = itemData?.categoryRelationId
        this.headFormData.authProjectCode = itemData?.authProjectCode
        this.headFormData.authProjectId = itemData?.authProjectId
        this.headFormData.authProjectName = itemData?.authProjectName
        console.log(itemData.categoryRelationCode, this.headFormData)

        this.$store.commit('cateCertiEffectApply', itemData)
      } else {
        this.firstData.partnerArchiveId = itemData?.partnerArchiveId
        this.firstData.partnerRelationId = itemData?.id
        this.firstData.supplierCode = itemData?.supplierCode
        this.firstData.supplierName = itemData?.supplierName

        this.headFormData.supplierEnterpriseCode = itemData?.supplierEnterpriseCode
        this.headFormData.supplierEnterpriseName = itemData?.supplierEnterpriseName
        this.headFormData.supplierEnterpriseId = itemData?.supplierEnterpriseId
        this.headFormData.partnerRelationCode = itemData?.partnerRelationCode
        this.headFormData.partnerRelationId = itemData?.id
        this.headFormData.categoryId = ''
        this.getCateGoryList(itemData?.id)
      }
    },
    categoryChange(event) {
      const { itemData } = event
      this.headFormData.categoryName = itemData?.categoryName || ''
      this.headFormData.categoryCode = itemData?.categoryCode || ''
      this.headFormData.categoryRelationCode = itemData?.categoryRelationCode || ''
      this.headFormData.categoryRelationId = itemData?.id

      this.getSceneList(this.headFormData.orgId, itemData?.categoryId)
    },
    sceneDefineChange(event) {
      const { itemData = {} } = event
      this.headFormData.sceneDefineName = itemData?.sceneName
      this.headFormData.sceneDefineCode = itemData?.sceneCode
    },

    getData() {
      return {
        data: this.headFormData,
        response: {
          authProjectCode: this.headFormData.authProjectCode,
          authProjectId: this.headFormData.authProjectId,
          authProjectName: this.headFormData.authProjectName,
          categoryRelationCode: this.headFormData.categoryRelationCode
        }
      }
    },
    next() {
      return new Promise((resolve, reject) => {
        // todo: 临时校验，待删除
        // resolve({
        //   data: this.headFormData,
        //   response: {
        //     authProjectSupplierId: "1497129249806966785",
        //   },
        // });

        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            const param = this.formatNextParam()
            console.log(param)
            return this.$API.supplierEffective
              .findAuthProjectCode(param)
              .then((res) => {
                resolve({
                  data: this.headFormData,
                  response: res.data
                })
              })
              .catch(() => {
                reject()
              })
          } else {
            reject()
          }
        })
      })
    },

    formatNextParam() {
      return {
        categoryRelationId: this.headFormData.categoryRelationId,
        partnerRelationId: this.headFormData.partnerRelationId,
        categoryRelationCode: this.headFormData.categoryRelationCode,
        sceneDefineId: this.headFormData.sceneDefineId,
        effectiveType: this.headFormData.effectiveType,
        orgId: this.headFormData.orgId,
        orgCode: this.headFormData.orgCode,
        orgName: this.headFormData.orgName
      }
    }
  }
}
</script>
<style lang="scss" scoped></style>
