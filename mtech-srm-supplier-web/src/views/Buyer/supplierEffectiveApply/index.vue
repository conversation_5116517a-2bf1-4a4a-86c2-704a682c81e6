<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { pageConfig, defaultToolbar } from './config/index'
import { cloneDeep } from 'lodash'
import utils from '@/utils/utils'

export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
    taskCode: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      pageConfig: []
    }
  },
  computed: {
    routeName() {
      return this.$route.name
    }
  },
  mounted() {
    pageConfig[0].grid.asyncConfig.defaultRules = []
    console.log('this.info.status', this.info.status)
    if (this.routeName === 'pur-category-certification-detail') {
      pageConfig[0].grid.asyncConfig.defaultRules.push({
        label: this.$t('品类认证项目编号'),
        field: 'authProjectCode',
        type: 'string',
        operator: 'equal',
        value: this.info.projectCode
      })
      if (this.taskCode == 'preEffectiveTempType') {
        pageConfig[0].grid.asyncConfig.defaultRules.push({
          label: this.$t('生效类型'),
          field: 'effectiveType',
          type: 'string',
          operator: 'equal',
          value: '1'
        })
      } else {
        pageConfig[0].grid.asyncConfig.defaultRules.push({
          label: this.$t('生效类型'),
          field: 'effectiveType',
          operator: 'equal',
          type: 'string',
          value: '2'
        })
      }
      if (this.info.status != 10 && this.info.status != 30) {
        if (this.taskCode == 'preEffectiveTempType') {
          pageConfig[0].toolbar = []
        } else {
          pageConfig[0].toolbar = [
            {
              id: 'audit',
              title: this.$t('查看OA审批'),
              icon: 'icon_solid_editsvg'
            }
          ]
        }
        pageConfig[0].useToolTemplate = false
      } else {
        if (this.taskCode == 'preEffectiveTempType') {
          let _defaultToolbar = cloneDeep(defaultToolbar)
          _defaultToolbar.tools[0].splice(5, 1)
          pageConfig[0].toolbar = _defaultToolbar
        } else {
          pageConfig[0].toolbar = defaultToolbar
        }
        pageConfig[0].useToolTemplate = false
      }
      // pageConfig[0].toolbar.tools[0] = pageConfig[0].toolbar.tools[0].filter(
      //   (e) => e.id != "Add"
      // );
    }
    this.pageConfig = pageConfig
  },
  methods: {
    handleClickToolBar(e) {
      console.log('handleClickToolBar', e)
      //勾选的每一列的数据
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0 && !['Add', 'export'].includes(e.toolbar.id)) {
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id === 'Submit') {
        const list = e.gridRef.getMtechGridRecords()
        this.handleSubmit(list)
      } else if (e.toolbar.id === 'Delete') {
        const list = e.gridRef.getMtechGridRecords()
        this.handleDelete(list)
      } else if (e.toolbar.id === 'Issued') {
        const list = e.gridRef.getMtechGridRecords()
        this.handleIssued(list)
      } else if (e.toolbar.id === 'Edit') {
        const list = e.gridRef.getMtechGridRecords()
        this.handleEdit(list)
      } else if (e.toolbar.id === 'audit') {
        const list = e.gridRef.getMtechGridRecords()
        this.audit(list)
      } else if (e.toolbar.id === 'export') {
        // 导出
        this.handleExport()
      }
    },
    audit(sltList) {
      if (sltList.length < 1) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (sltList.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      if (sltList[0].effectiveType == 1) {
        this.$toast({ content: this.$t('预生效状态不能查看OA审批'), type: 'warning' })
        return
      }
      let params = {
        applyId: sltList[0].id,
        businessType: 'effective'
      }
      this.$API.assessManage.infoGetOALink(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    handleClickCellTitle(e) {
      console.log('handleClickCellTitle', e)
      if (e.field === 'applyCode') {
        this.goDetail(e.data.id)
      }
    },
    handleAdd() {
      this.$dialog({
        modal: () => import('./components/addDialog.vue'),
        data: {
          projectCode: this.info.projectCode,
          taskCode: this.taskCode
        },
        success: (id) => {
          this.$router.push({
            path: '/supplier/pur/effective-apply-detail',
            query: {
              id: id
            }
          })
        }
      })
    },
    goDetail(rowId) {
      console.log(this.$t('点击编辑'))
      this.$router.push({
        path: '/supplier/pur/effective-apply-detail',
        query: {
          id: rowId
        }
      })
    },
    handleEdit(list) {
      if (list.length === 0) return
      const idList = list.map((v) => v.id)

      if (list.length !== 1) {
        this.$toast({
          content: this.$t('编辑时只能选中一条数据'),
          type: 'warning'
        })
        return
      }
      if (list[0].applyStatus === 10 || list[0].applyStatus === 30 || list[0].applyStatus === 90) {
        this.$router.push({
          path: '/supplier/pur/effective-apply-detail',
          query: {
            id: idList[0]
          }
        })
      } else {
        this.$toast({
          content: this.$t('选中数据不支持编辑'),
          type: 'warning'
        })
      }
    },
    handleSubmit(list) {
      if (list.length === 0) {
        return
      }
      const idList = list
        .filter((v) => v.applyStatus === 10 || v.applyStatus === 30)
        .map((v) => v.id)

      if (idList.length < list.length) {
        this.$toast({
          content: this.$t('选中数据中含有不支持提交的单据'),
          type: 'warning'
        })
        return
      }
      this.$loading()
      this.$API.SupplierPunishment.applySubmit({
        applyIdList: idList
      })
        .then((res) => {
          this.$hloading()
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.$toast({
              content: res.msg,
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$toast({
              content: res.msg,
              type: 'warning'
            })
          }
        })
        .catch(() => {
          this.$hloading()
        })
    },
    handleDelete(list) {
      if (list.length === 0) {
        return
      }
      const idList = list
        .filter((v) => v.applyStatus === 10 || v.applyStatus === 30)
        .map((v) => v.id)

      if (idList.length < list.length) {
        this.$toast({
          content: this.$t('选中数据中含有不支持删除的单据'),
          type: 'warning'
        })
        return
      }
      this.$loading()
      this.$API.supplierEffective
        .delEffective({
          applyIdList: idList
        })
        .then((res) => {
          this.$hloading()
          if (res.code === 200 && res.data) {
            this.$toast({
              content: res.msg,
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$toast({
              content: res.msg,
              type: 'warning'
            })
          }
        })
        .catch(() => {
          this.$hloading()
        })
    },
    handleIssued(list) {
      if (list.length === 0) {
        return
      }
      const idList = list.filter((v) => v.applyStatus === 90).map((v) => v.id)

      if (idList.length < list.length) {
        this.$toast({
          content: this.$t('选中数据中含有不支持下发的单据'),
          type: 'warning'
        })
        return
      }
      this.$loading()
      this.$API.supplierEffective
        .issuedEffective({
          applyIdList: idList
        })
        .then((res) => {
          this.$hloading()
          if (res.code === 200 && res.data) {
            this.$toast({
              content: res.msg,
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$toast({
              content: res.msg,
              type: 'warning'
            })
          }
        })
        .catch(() => {
          this.$hloading()
        })
    },
    // 导出
    handleExport() {
      const rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        condition: rule.condition || '',
        rules: rule.rules || []
      }
      const requestUrl = {
        preName: 'assessManage',
        urlName: 'exportSupEffectiveList'
      }
      utils.exportData(requestUrl, params)
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
