<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('调查表模板名称')"
          label-style="top"
          prop="taskTemplateName"
        >
          <mt-input
            v-model="formInfo.taskTemplateName"
            :placeholder="$t('请输入调查表模板名称')"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('调查表类型')"
          label-style="top"
          prop="taskTemplateType"
        >
          <mt-select
            v-model="formInfo.taskTemplateType"
            :data-source="typeList"
            :placeholder="$t('请选择调查表类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('调查表模板分类')"
          label-style="top"
          prop="taskTemplateClassify"
        >
          <mt-select
            v-model="formInfo.taskTemplateClassify"
            :data-source="taskClassifyList"
            :fields="fields"
            :placeholder="$t('请选择调查表模板分类')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('备注：')" label-style="top">
          <mt-input
            v-model="formInfo.remark"
            :multiline="true"
            :rows="2"
            maxlength="200"
            float-label-type="Never"
            :placeholder="$t('字数不超过200字')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { taskTemplateTypeSetting } from '@/utils/setting'
const typeList = []
for (let i in taskTemplateTypeSetting) {
  typeList.push({
    text: taskTemplateTypeSetting[i],
    value: parseInt(i)
  })
}

export default {
  data() {
    return {
      formInfo: {
        taskTemplateName: '', // 调查表模板名称
        taskTemplateType: null, // 调查表类型
        taskTemplateClassify: '', // 调查表模板分类
        remark: '' //备注
      },
      typeList,
      fields: {
        text: 'itemName',
        value: 'itemCode'
      },
      rules: {
        taskTemplateName: [
          { required: true, message: this.$t('请输入阶段名称'), trigger: 'blur' },
          { whitespace: true, message: this.$t('请输入阶段名称'), trigger: 'blur' }
        ],
        taskTemplateType: [
          { required: true, message: this.$t('请选择调查表类型'), trigger: 'blur' }
        ],
        taskTemplateClassify: [
          { required: true, message: this.$t('请选择调查表模板分类'), trigger: 'blur' }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmAndEnter,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      approveVal: 0
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    },
    taskClassifyList() {
      this.formInfo.taskTemplateClassify =
        !!this.modalData.taskClassifyList &&
        !!this.modalData.taskClassifyList[0] &&
        this.modalData.taskClassifyList[0].itemCode
      return this.modalData.taskClassifyList
    }
  },
  mounted() {
    this.show()
    this.isEdit && this.initData()
  },
  methods: {
    initData() {
      if (this.info && Object.keys(this.info).length) {
        const { id, taskTemplateName, taskTemplateType, taskTemplateClassify, remark } = this.info
        this.formInfo = Object.assign({}, this.formInfo, {
          id,
          taskTemplateName,
          taskTemplateType,
          taskTemplateClassify,
          remark
        })
      }
    },

    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm(type = 'confirm') {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          const methodName = this.isEdit ? 'editFormTemplate' : 'addFormTemplate'
          this.$API.QuestionnaireConfig[methodName](this.formInfo)
            .then((res) => {
              const { code, data } = res
              if (code == 200 && data) {
                if (type === 'enter') {
                  if (this.formInfo.id || data) {
                    this.$emit('confirm-function', this.formInfo.id || data)
                  }
                } else {
                  this.$toast({ content: this.$t('操作成功'), type: 'success' })
                  this.$emit('confirm-function')
                }
              }
            })
            .catch((err) => {
              this.$toast({ content: err.msg || this.$t('系统异常'), type: 'error' })
            })
        }
      })
    },
    confirmAndEnter() {
      this.confirm('enter')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
