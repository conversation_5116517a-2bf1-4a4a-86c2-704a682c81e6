// 阶段定义
<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item class="form-item" :label="$t('阶段名称')" label-style="top" prop="stageName">
          <mt-input
            v-model="formInfo.stageName"
            :placeholder="$t('请输入阶段名称')"
            float-label-type="Never"
            width="390"
            :maxlength="30"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('阶段类型')" label-style="top" prop="accessType">
          <mt-select
            v-if="typeList.length > 0"
            v-model="formInfo.accessType"
            :data-source="typeList"
            :placeholder="$t('请选择阶段类型')"
            width="390"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('排序值')" prop="sortValue" label-style="top">
          <mt-inputNumber
            :show-clear-button="true"
            :show-spin-button="false"
            v-model="formInfo.sortValue"
            :placeholder="$t('排序值不能重复')"
            width="390"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('备注：')" label-style="top">
          <mt-input
            v-model="formInfo.remark"
            :multiline="true"
            :rows="2"
            maxlength="200"
            float-label-type="Never"
            :placeholder="$t('字数不超过200字')"
            width="820"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { taskTemplateTypeSetting } from '@/utils/setting'

const typeList = []
for (let i in taskTemplateTypeSetting) {
  typeList.push({
    text: taskTemplateTypeSetting[i],
    value: parseInt(i)
  })
}

export default {
  data() {
    return {
      formInfo: {
        stageName: '', // 阶段模板名称
        accessType: 0, // 阶段类型（默认为供应商准入阶段）
        sortValue: null, // 排序值
        status: 2, // 是否启用
        remark: '' //备注
      },
      typeList,
      rules: {
        stageName: [
          {
            required: true,
            message: this.$t('请输入阶段名称'),
            trigger: 'blur'
          },
          {
            whitespace: true,
            message: this.$t('请输入阶段名称'),
            trigger: 'blur'
          }
        ],
        accessType: [
          {
            required: true,
            message: this.$t('请选择阶段类型'),
            trigger: 'blur'
          }
        ],
        sortValue: [{ required: true, message: this.$t('请输入排序值'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  mounted() {
    this.show()
    this.isEdit && this.initData()
  },
  methods: {
    initData() {
      if (this.info && Object.keys(this.info).length) {
        const { id, stageName, accessType, sortValue, status, remark } = this.info
        this.formInfo = Object.assign({}, this.formInfo, {
          id,
          stageName,
          accessType,
          sortValue,
          status,
          remark
        })
      }
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          const methodName = this.isEdit ? 'editStage' : 'addStage'
          this.$API.AccessStage[methodName](this.formInfo)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0 20px;
  font-size: 16px;
}
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
/deep/.mt-input-number input {
  width: 100%;
}
</style>
