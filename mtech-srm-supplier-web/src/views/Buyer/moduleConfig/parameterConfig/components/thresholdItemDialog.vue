// 门槛项定义
<template>
  <mt-dialog ref="thresholdItemDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('门槛名称')"
          label-style="top"
          prop="thresholdName"
        >
          <mt-input
            v-model="formInfo.thresholdName"
            :placeholder="$t('请输入门槛名称')"
            float-label-type="Never"
            :maxlength="30"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('门槛类型')" label-style="top" prop="formType">
          <mt-select
            v-model="formInfo.formType"
            :data-source="formTypeList"
            :placeholder="$t('请选择门槛类型')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            @change="formTypeChange"
          ></mt-select>
        </mt-form-item>
        <!-- <mt-form-item
          class="form-item"
          :label="$t('数据源')"
          label-style="top"
          prop="bizType"
        >
          <mt-select
            v-model="formInfo.bizType"
            :data-source="bizTypeList"
            :placeholder="$t('请选择数据源')"
            @change="bizTypeChange"
          ></mt-select>
        </mt-form-item> -->
        <mt-form-item class="form-item" :label="$t('监控字段')" label-style="top" prop="fieldCode">
          <mt-select
            v-model="formInfo.fieldCode"
            :data-source="thresholdFieldList"
            :placeholder="$t('请选择')"
            :fields="{ text: 'fieldName', value: 'fieldCode' }"
            :allow-filtering="true"
            :filtering="onFiltering"
            @change="fieldCodeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item full-width"
          :label="$t('门槛原值')"
          prop="originalValue"
          label-style="top"
          v-if="formInfo.whetherList == '1'"
        >
          <mt-input
            v-model="formInfo.originalValue"
            maxlength="50"
            :placeholder="$t('请输入')"
            :disabled="formInfo.symbol > 5"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('门槛项目')"
          label-style="top"
          prop="thresholdProject"
          v-if="formInfo.whetherList == '1'"
        >
          <mt-select
            v-model="formInfo.thresholdProject"
            :data-source="thresholdProjectList"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            float-label-type="Never"
            :placeholder="$t('请选择')"
            width="390"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('校验操作符')" label-style="top" prop="symbol">
          <mt-select
            v-model="formInfo.symbol"
            :data-source="symbolList"
            :placeholder="$t('请选择')"
            @change="symbolChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('是否红线项')"
          label-style="top"
          prop="isRedLine"
        >
          <mt-select
            v-model="formInfo.isRedLine"
            :data-source="redLineItemList"
            :placeholder="$t('请选择是否红线项')"
          />
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('归属领域')"
          label-style="top"
          prop="belongDomain"
        >
          <mt-select
            v-model="formInfo.belongDomain"
            :data-source="belongToList"
            :placeholder="$t('请选择归属领域')"
          />
        </mt-form-item>
        <mt-form-item
          class="form-item full-width"
          :label="$t('默认目标值')"
          prop="defaultValue"
          label-style="top"
        >
          <mt-input
            v-model="formInfo.defaultValue"
            :placeholder="$t('默认目标值')"
            maxlength="50"
            :disabled="formInfo.symbol > 5"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item full-width" :label="$t('备注：')" label-style="top">
          <mt-input
            v-model="formInfo.remark"
            :multiline="true"
            :rows="2"
            maxlength="200"
            float-label-type="Never"
            :placeholder="$t('字数不超过200字')"
            width="820"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  data() {
    return {
      editTimes: 0,
      sourceLabel: this.$t('选择资质'),
      thresholdProjectList: [],
      bizTypeList: [
        {
          text: this.$t('资质项'),
          value: 1
        },
        {
          text: this.$t('监控字段'),
          value: 2
        }
      ],
      symbolList: [
        {
          text: '>',
          value: 1
        },
        {
          text: '<',
          value: 2
        },
        {
          text: '≥',
          value: 3
        },
        {
          text: '≤',
          value: 4
        },
        {
          text: '=',
          value: 5
        },
        {
          text: this.$t('非空'),
          value: 6
        },
        {
          text: this.$t('为空'),
          value: 7
        }
      ],
      redLineItemList: [
        { text: this.$t('否'), value: 2 },
        { text: this.$t('是'), value: 1 }
      ],
      belongToList: [
        { text: this.$t('研发'), value: 1 },
        { text: this.$t('质量'), value: 2 },
        { text: this.$t('成本'), value: 3 },
        { text: this.$t('采购'), value: 4 }
      ],
      formInfo: {
        bizId: 0,
        bizType: 2,
        defaultValue: '',
        fieldCode: null,
        fieldId: null,
        fieldName: null,
        formType: 0,
        symbol: 0,
        thresholdName: '',
        source: '1',
        whetherList: null,
        isRedLine: 2
      },
      thresholdFieldList: [],
      formTypeList: [],
      rules: {
        thresholdName: [
          { required: true, message: this.$t('请输入'), trigger: 'blur' },
          { whitespace: true, message: this.$t('请输入'), trigger: 'blur' }
        ],
        formType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        bizType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        fieldCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        symbol: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        originalValue: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        thresholdProject: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        isRedLine: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        belongDomain: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  mounted() {
    this.initData()
    this.show()
  },
  methods: {
    onFiltering(e) {
      e.updateData(this.thresholdFieldList.filter((x) => x.fieldName.includes(e.text)))
    },
    symbolChange() {
      // if (e.value > 5) {
      //   this.formInfo.defaultValue = null;
      //   this.rules.defaultValue[0].required = false;
      // } else {
      //   this.rules.defaultValue[0].required = true;
      // }
    },
    async initData() {
      await this.getDict()
      await this.getThresholdProjectList()
      // await this.getThresholdFieldList(this.info.bizType);
      // await this.getThresholdFieldList();
      if (this.isEdit && this.info && Object.keys(this.info).length) {
        this.formInfo = {
          ...this.info,
          bizType: 2,
          formType: this.info.formType.toString()
        }
      }
    },
    async getDict() {
      await this.$API.ModuleConfig.queryDict({
        dictCode: 'thresholdType'
      }).then((res) => {
        this.formTypeList = res.data
      })
    },
    async getThresholdProjectList() {
      await this.$API.ModuleConfig.queryDict({
        dictCode: 'thresholdProjectList'
      }).then((res) => {
        this.thresholdProjectList = res.data
      })
    },
    show() {
      this.$refs['thresholdItemDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['thresholdItemDialog'].ejsRef.hide()
    },
    // bizTypeChange(e) {
    //   if (e.value == 1) {
    //     this.sourceLabel = this.$t("选择资质");
    //   } else {
    //     this.sourceLabel = this.$t("选择信息");
    //   }
    //   this.getThresholdFieldList(e.value);
    // },
    // async getThresholdFieldList() {
    //   await this.$API.ModuleConfig.queryThresholdFieldList()
    //     .then((res) => {
    //       if (res.code === 200) {
    //         this.thresholdFieldList = res.data;
    //       }
    //     })
    //     .catch((e) => {
    //       this.thresholdFieldList = [];
    //       this.$toast({ content: e.msg, type: "warning" });
    //       return;
    //     });
    // },

    queryByBusinessType(param) {
      this.$API.ModuleConfig.queryByBusinessType(param)
        .then((res) => {
          if (res.code === 200) {
            this.thresholdFieldList.length = 0
            setTimeout(() => {
              this.thresholdFieldList = res.data
            }, 10)
          }
        })
        .catch((e) => {
          this.thresholdFieldList.length = 0
          this.$toast({ content: e.msg, type: 'warning' })
          return
        })
    },
    formTypeChange(e) {
      if (this.editTimes == 0) {
        ++this.editTimes
      } else {
        this.formInfo.fieldCode = null
        this.formInfo.originalValue = null
        this.formInfo.thresholdProject = null
      }

      this.queryByBusinessType({ businessType: e.itemData.itemCode })
    },

    fieldCodeChange(e) {
      if (e != null && e.value != null) {
        this.formInfo.fieldCode = e.itemData.fieldCode
        this.formInfo.fieldId = e.itemData.id
        this.formInfo.fieldName = e.itemData.fieldName
        this.$set(this.formInfo, 'whetherList', e.itemData.whetherList)
        if (e.itemData.whetherList == 0) {
          this.formInfo.originalValue = null
          this.formInfo.thresholdProject = null
        }
      } else {
        this.formInfo.fieldCode = null
        this.formInfo.fieldId = null
        this.formInfo.fieldName = null
      }
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          // if (this.formInfo.defaultValue < 0) {
          //   this.$toast({
          //     content: this.$t("请输入非负整数"),
          //     type: "warning",
          //   });
          //   return;
          // }
          const methodName = this.isEdit ? 'updateThresholdDef' : 'addThresholdDef'

          this.$API.ModuleConfig[methodName](this.formInfo)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0 20px;
  font-size: 16px;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
    }
    .full-width {
      width: 100%;
    }
  }
}
</style>
