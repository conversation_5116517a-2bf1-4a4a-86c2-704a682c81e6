<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="
            labelDefineType === 3
              ? $t('标签名称')
              : labelDefineType === 1
              ? $t('级别定义名称')
              : $t('关系类别名称')
          "
          label-style="top"
          prop="labelName"
        >
          <mt-input
            v-model="formInfo.labelName"
            :placeholder="$t('请输入名称')"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="
            labelDefineType === 3
              ? $t('标签标识')
              : labelDefineType === 1
              ? $t('级别定义标识')
              : $t('关系类别标识')
          "
          label-style="top"
          prop="labelType"
        >
          <template v-if="labelDefineType === 1">
            <mt-input
              :disabled="true"
              v-model="formInfo.labelType"
              :placeholder="$t('请输入标识')"
            ></mt-input>
          </template>
          <template v-if="labelDefineType !== 1">
            <mt-input v-model="formInfo.labelType" :placeholder="$t('请输入标识')"></mt-input>
          </template>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('是否启用')" label-style="left">
          <mt-switch v-model="formInfo.status" :active-value="1" :inactive-value="2"></mt-switch>
        </mt-form-item>

        <template v-if="labelDefineType === 1">
          <mt-row :gutter="20">
            <mt-col :span="12">
              <mt-form-item class="form-item" :label="$t('向下覆盖')" label-style="left">
                <mt-switch
                  v-model="formInfo.coverType"
                  :active-value="1"
                  :inactive-value="0"
                ></mt-switch>
              </mt-form-item>
            </mt-col>
            <mt-col :span="12">
              <mt-form-item class="form-item" :label="$t('向上提报')" label-style="left">
                <mt-switch
                  v-model="formInfo.recommend"
                  :active-value="1"
                  :inactive-value="0"
                ></mt-switch>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </template>

        <mt-form-item class="form-item" :label="$t('备注：')" label-style="top">
          <mt-input
            v-model="formInfo.remark"
            :multiline="true"
            maxlength="200"
            :rows="3"
            float-label-type="Never"
            :placeholder="$t('字数不超过200字')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import MtSwitch from '@mtech-ui/switch'

export default {
  components: { MtSwitch },
  data() {
    return {
      formInfo: {
        labelDefineType: 2, //1分级
        labelName: '', // 级别模板名称
        labelType: '', // 级别标识
        status: 1, // 是否启用， 1启用2禁用
        remark: '', //备注
        recommend: 0,
        coverType: 0
      },
      rules: {
        labelName: [
          { required: true, message: this.$t('请输入名称'), trigger: 'blur' },
          { whitespace: true, message: this.$t('请输入名称'), trigger: 'blur' }
        ],
        labelType: [
          { required: true, message: this.$t('请输入标识'), trigger: 'blur' },
          { whitespace: true, message: this.$t('请输入标识'), trigger: 'blur' }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      levelTypeList: [
        'S',
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'k',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    labelDefineType() {
      return this.modalData.labelDefineType
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    },
    currentViewData() {
      return this.modalData.currentViewData
    }
  },
  mounted() {
    this.show()
    this.isEdit && this.initData()

    // 新增级别定义 默认级别标识 级别标识不能随便填写 有顺序
    if (this.labelDefineType === 1 && !this.isEdit) {
      this.formInfo.labelType = this.getLabelType()
    }
  },
  methods: {
    initData() {
      // 编辑
      if (this.info && Object.keys(this.info).length) {
        const { id, labelName, labelType, status, remark, coverType, recommend } = this.info
        this.formInfo = Object.assign({}, this.formInfo, {
          id,
          labelName,
          labelType,
          status: status,
          remark
        })
        if (this.labelDefineType === 1) {
          this.formInfo.coverType = coverType // 向下覆盖
          this.formInfo.recommend = recommend // 向上覆盖（升降级别时候用）
        }
      }
    },
    // 获取级别定义下的默认级别
    getLabelType() {
      let keyValueList = {}
      for (let [key, value] of Object.entries(this.levelTypeList)) {
        keyValueList[key] = value
      }
      let listLength = this.currentViewData.length
      return keyValueList[listLength]
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      this.formInfo.labelDefineType = this.labelDefineType
      if (this.labelDefineType !== 1) {
        delete this.formInfo.recommend
        delete this.formInfo.coverType
      }
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          if (!this.isEdit) {
            let similarTypeName =
              this.currentViewData.find((item) => {
                return item.labelName === this.formInfo.labelName
              }) || ''
            if (!!similarTypeName && !!similarTypeName.labelName) {
              this.$toast({ content: this.$t('不能存在相同的名称'), type: 'warning' })
              return
            }
          }

          const methodName = this.isEdit ? 'updateGrade' : 'addGrade'
          this.$API.GradeConfig[methodName](this.formInfo)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
            .catch((err) => {
              this.$toast({ content: err.msg || this.$t('系统异常'), type: 'error' })
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
