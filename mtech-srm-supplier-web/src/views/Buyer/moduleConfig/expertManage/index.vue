<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      :current-tab="currentTab"
    ></mt-template-page>
    <mt-dialog
      ref="dialog"
      v-if="dialogVisible"
      css-class="index-dialog"
      :header="dialogHeader"
      :buttons="buttons"
      :open="onOpen"
      :close="onClose"
      height="40%"
      width="40%"
    >
      <mt-form ref="calcRuleForm" :model="data" style="margin-top: 15px">
        <mt-form-item
          prop="sceneApproveCnt"
          class="form-item dimension-name"
          :label="$t('现场审查次数')"
        >
          <mt-input
            maxlength="20"
            v-model="data.sceneApproveCnt"
            :disabled="false"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入现场审查次数')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item
          prop="reportApproveCnt"
          class="form-item dimension-type"
          :label="$t('报告审核次数')"
        >
          <mt-input
            maxlength="20"
            v-model="data.reportApproveCnt"
            :disabled="false"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入报告审核次数')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="refuseExecutionCnt" class="form-item" :label="$t('拒绝执行次数')">
          <mt-input
            maxlength="100"
            v-model="data.refuseExecutionCnt"
            :disabled="false"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入拒绝执行次数')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>
<script>
import { templateTypesList, categoryList, orgList, expertDatabase } from './config'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  data() {
    const currentTab = JSON.parse(localStorage.getItem('tabIndex'))
      ? JSON.parse(localStorage.getItem('tabIndex'))
      : 0
    localStorage.removeItem('tabIndex')
    return {
      data: {
        sceneApproveCnt: '',
        reportApproveCnt: '',
        refuseExecutionCnt: '',
        id: undefined
      },
      currentTab,
      pageConfig: [
        {
          useToolTemplate: false,
          gridId: '82c78d4d-63b0-4617-88de-6e78e6f9a6da',
          toolbar: {
            tools: [
              [
                'Add',
                'Delete',
                {
                  id: 'enable',
                  title: this.$t('启用'),
                  icon: 'icon_table_enable'
                },
                {
                  id: 'disable',
                  title: this.$t('停用'),
                  icon: 'icon_table_disable'
                },
                { id: 'ImportCate', icon: 'icon_solid_upload', title: this.$t('导入') },
                { id: 'ExportCate', icon: 'icon_solid_Download', title: this.$t('导出') },
                {
                  id: 'Edit',
                  icon: 'icon_solid_edit',
                  title: this.$t('编辑')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: expertDatabase,
            asyncConfig: {
              url: '/sourcing/tenant/expert/apply/query'
            }
          }
        }
      ],
      tabSltIndex: currentTab,
      isEdit: false,

      /*--- 评审模板 ---*/
      templateTypeList: [], //评审模板-模板类型列表（下拉）

      // 弹窗数据
      dialogVisible: false,
      dialogHeader: this.$t('编辑'),
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  mounted() {
    this.getInitList()
  },
  methods: {
    // 创建随机数
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnoprstuvwxyz123456789',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    //=====【表格顶部操作toolbar】=====
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      const _statusList = sltList.map((el) => Number(el.applyStatus))
      if (
        (!sltList || sltList.length <= 0) &&
        ['Delete', 'delete', 'enable', 'disable', 'Edit'].includes(toolbar.id)
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      //-------导入按钮--------
      if (toolbar.id === 'ImportCate') {
        this.ImportOnReview()
        return
      }

      //-------新增按钮--------
      if (toolbar.id === 'Add' || toolbar.id === 'add') {
        // 专家 == 新增
        this.$router.push({
          path: `/supplier/expert-detail`,
          query: {
            key: this.randomString(),
            applyStatus: 0
          }
        })
      }

      //-------删除按钮--------
      else if (toolbar.id === 'Delete' || toolbar.id === 'delete') {
        //相关页面==删除
        if (_statusList.some((item) => item != 0 && item != -1)) {
          this.$toast({
            content: this.$t(`所选数据中含有不能删除的状态的数据，不能进行该操作`),
            type: 'warning'
          })
          return
        }
        let ids = sltList.map((e) => e.id)
        this.handleDelete(ids)
      }

      //-------启动/停用按钮-------- //
      else if (toolbar.id === 'enable' || toolbar.id === 'disable') {
        let toStatus = toolbar.id === 'enable' ? 4 : -1
        // const _bizTypeList = sltList.map((el) => Number(el.bizType))
        // if (_bizTypeList.some((item) => item != 1)) {
        //   this.$toast({
        //     content: this.$t(`所选数据中含有招投标业务类型的数据，不能进行该操作`),
        //     type: 'warning'
        //   })
        //   return
        // }
        // if (_statusList.some((item) => item != (toolbar.id === 'enable' ? -1 : 4))) {
        //   const contens =
        //     this.$t('所选单据中含有已') +
        //     (toolbar.id === 'enable' ? this.$t('生效') : this.$t('停用')) +
        //     this.$t('状态的单据，无法') +
        //     (toolbar.id === 'enable' ? this.$t('启用') : this.$t('停用'))
        //   this.$toast({
        //     content: contens,
        //     type: 'warning'
        //   })
        //   return
        // }

        let ids = sltList.map((e) => e.id)
        this.operatorState(toolbar.id, ids, toStatus)
      }

      // 编辑按钮
      else if (e.toolbar.id == 'Edit') {
        if (sltList.length > 1) {
          this.$toast({ content: this.$t('一次只能编辑一条数据'), type: 'warning' })
          return
        }
        this.dialogHeader = this.$t('编辑审查次数')
        this.data = sltList[0]
        this.show()
      }
      // 导出
      else if (e.toolbar.id == 'ExportCate') {
        this.ExportExpert()
      }
    },
    // 专家导出
    ExportExpert() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 20 },
        rules: rule.rules || []
      }
      this.$API.ModuleConfig.exportExpertFile(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 专家导入
    ImportOnReview() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'importFile',
          importApi: this.$API.ModuleConfig.importExpertFile,
          downloadTemplateApi: this.$API.ModuleConfig.downloadExpertTemplate
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //=====【点击表格触发】=======(相关参数设置了cellTools后触发，具体在config)
    handleClickCellTool(e) {
      const { tool, data } = e
      const { id } = tool || {}
      const { applyStatus } = data
      if (id === 'edit' || id === 'delete') {
        if (applyStatus > 1) {
          this.$toast({
            content: this.$t('当前状态不可编辑'),
            type: 'warning'
          })
          return
        }
      } else if (['enable', 'disable'].includes(tool.id)) {
        // let toStatus = id === 'enable' ? 4 : -1
        // let _status = Number(applyStatus)
        // if (_status === -1 || _status === 0 || _status === 4) {
        //   //相关页面==启动停用
        //   this.operatorState(id, [data.id], toStatus)
        // }
      }
    },
    //=====【点击表格列内容触发】=====(表格列设置了CellTitle后触发)
    //单元格标题
    handleClickCellTitle(e) {
      if (e.field == 'applyCode') {
        this.$router.push({
          path: `/supplier/expert-detail`,
          query: {
            configId: e.data.id,
            applyStatus: e.data.applyStatus
          }
        })
      } else if (e.field == 'categoryInfo') {
        this.$dialog({
          modal: () => import('./detail/components/categoryDialog.vue'),
          data: {
            title: this.$t('查看品类'),
            data: e.data.categoryInfo
          },
          success: () => {}
        })
      }
    },
    // 获取页面初始数据
    async getInitList() {
      await this.getCategoryList()
      await this.getStatedLimitTree()
      await this.getTypeList()
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 获取品类列表
    async getCategoryList() {
      categoryList.length = 0
      // await this.$API.ModuleConfig.getCategoryList({
      //   level: 1,
      // }).then((res) => {
      //   res.data.forEach((e) => categoryList.push(e));
      // });
      await this.$API.supplierInvitation
        .getCategoryListAll({
          condition: 'and',
          page: {
            current: 1,
            size: 10000
          },
          pageFlag: false,
          rules: [
            {
              field: 'statusId',
              type: 'int',
              operator: 'equal',
              value: 1
            },
            {
              field: 'tree_level',
              operator: 'equal',
              type: 'int',
              value: 1
            },
            {
              condition: 'and',
              rules: [
                {
                  field: 'categoryCode',
                  type: 'string',
                  operator: 'contains',
                  value: ''
                },
                {
                  condition: 'or',
                  field: 'categoryName',
                  type: 'string',
                  operator: 'contains',
                  value: ''
                }
              ]
            }
          ]
        })
        .then((res) => {
          res.data.records.forEach((e) => categoryList.push(e))
        })
    },
    // 获取组织树
    async getStatedLimitTree() {
      orgList.length = 0
      let query = 'ORG02'

      await this.$API.ModuleConfig.getStatedLimitTree({
        orgLevelCode: query,
        // orgType: "ORG001ADM",
        orgType: 'ORG001PRO'
      }).then((res) => {
        let flatData = this.flatTreeData(res.data).filter((e) => e.orgLeveLTypeCode === 'ORG02')
        flatData.forEach((e) => orgList?.push(e))
      })
    },
    // 扁平化树形结构数据
    flatTreeData(tree, children_key = 'children') {
      if (!tree || Object.prototype.toString.call(tree) !== '[object Array]' || tree.length <= 0)
        return []
      return tree.reduce(
        (pre, cur) => pre.concat(cur, this.flatTreeData(cur[children_key], children_key)),
        []
      )
    },
    // 获取评审模板类型列表
    async getTypeList() {
      templateTypesList.length = 0
      await this.$API.ModuleConfig.queryDict({
        dictCode: 'reviewType'
      }).then((res) => {
        if (res.code == 200) {
          res.data.forEach((e) => templateTypesList.push(e))
        }
      })
    },
    async operatorState(opt, ids, state) {
      const params = {
        applyInfoIdList: ids,
        status: state
      }
      const res = await this.$API.expert.operatorState(params)
      if (res.code === 200) {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      }
    },
    handleDelete(idList) {
      let _params = {
        idList
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: async () => {
          const res = await this.$API.expert.expertDelete(_params)
          if (res.code === 200) {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        }
      })
    },
    // 弹窗事件
    onOpen(args) {
      this.show()
      args.preventFocus = true
    },
    onClose() {
      this.hide()
    },
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.dialog.ejsRef.show()
      })
    },
    hide() {
      this.dialogVisible = false
      this.$nextTick(() => {
        this.$refs.dialog.ejsRef.hide()
      })
    },
    async confirm() {
      let _params = {
        expertId: this.data.expertId,
        refuseExecutionCnt: this.data.refuseExecutionCnt,
        reportApproveCnt: this.data.reportApproveCnt,
        sceneApproveCnt: this.data.sceneApproveCnt
      }
      const res = await this.$API.expert.updateExpertNumber(_params)
      if (res.code === 200) {
        this.$toast({
          content: this.$t('修改成功'),
          type: 'success'
        })
        this.hide()
        this.$refs.templateRef.refreshCurrentGridData()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/mixin.scss';

.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
  /deep/.e-rowcell.sticky-col-0,
  /deep/.e-headercell.sticky-col-0 {
    @include sticky-col;
    left: 0px;
  }
  /deep/.e-rowcell.sticky-col-1,
  /deep/.e-headercell.sticky-col-1 {
    @include sticky-col;
    left: 50px; // (注意：) 50px就是前面所有固定列的列宽相加，比如这里就是 0 + 复选框列（50px） = 50px
  }
}
</style>
