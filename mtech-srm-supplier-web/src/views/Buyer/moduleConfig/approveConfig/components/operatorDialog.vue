<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('节点')"
          label-style="top"
          prop="approveDefineType"
        >
          <mt-select
            v-model="formInfo.approveDefineType"
            :data-source="approveNodeList"
            :placeholder="$t('请选择审批节点')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item
          prop="workflowKey"
          :label="$t('审批流选择')"
          v-if="flowGroupFields.dataSource.length"
        >
          <!-- <mt-DropDownTree
            :fields="companyFields"
            :showCheckBox="false"
            class="mt-down-tree"
            id="checkboxTreeSelect"
            placeholder='请选择审批流'
            :allowMultiSelection="false"
            :autoCheck="false"
            v-model="formInfo.workflowName"
            @select="companyChange"
          ></mt-DropDownTree> -->
          <mt-input
            :readonly="true"
            :show-clear-button="false"
            v-model="formInfo.workflowName"
            float-label-type="Never"
            ref="inputBox"
            @focus="focusGroupInput"
          ></mt-input>
          <div class="close-btn" @click="closeBtn">{{ $t('关闭') }}</div>
          <mt-tree-view
            v-show="showTreeView"
            class="tree-view-container"
            ref="innerTreeView"
            :fields="flowGroupFields"
            @nodeSelected="nodeSelected"
            @nodeClicked="nodeClicked"
          ></mt-tree-view>
        </mt-form-item>

        <mt-form-item class="form-item" :label="$t('是否启用')" label-style="left">
          <mt-switch v-model="formInfo.status" :active-value="1" :inactive-value="2"></mt-switch>
        </mt-form-item>

        <mt-form-item class="form-item" :label="$t('备注：')" label-style="top">
          <mt-input
            v-model="formInfo.remark"
            :multiline="true"
            :rows="2"
            maxlength="200"
            float-label-type="Never"
            :placeholder="$t('字数不超过200字')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import MtSwitch from '@mtech-ui/switch'

export default {
  components: { MtSwitch },
  data() {
    return {
      miodea: [],
      showTreeView: false,
      formInfo: {
        approveDefineName: '',
        approveDefineType: '', // 审批节点
        workflowGroup: '',
        workflowName: '',
        workflowId: '',
        workflowKey: '', // 审批流
        status: 1, // 是否启用， 1启用2备用
        remark: '' //备注
      },
      selectCompanyObject: {},
      approveNodeList: [],
      rules: {
        approveDefineType: [
          { required: true, message: this.$t('请选择审批节点'), trigger: 'blur' }
        ],
        workflowKey: [{ required: true, message: this.$t('请选择审批流'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],

      companyFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'subChild'
      },
      flowGroupFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'subChild'
      },
      selectedNodeSet: new Set(),
      currentTemplateNode: null,
      businessTypeList: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  created() {
    this.getCompanyTree()
    this.getDict()
  },
  mounted() {
    this.show()
    this.isEdit && this.initData()
  },
  methods: {
    closeBtn() {
      this.showTreeView = false
    },
    focusGroupInput() {
      this.showTreeView = true
    },
    getDict() {
      this.$API.supplierResources['getDictItem']({
        dictCode: 'supplierApproveNode'
      }).then((res) => {
        let { data } = res
        if (res.code === 200 && !!data && data.length > 0) {
          let filterData = res.data.map((v) => {
            return {
              text: v.name,
              value: v.itemCode
            }
          })
          this.approveNodeList = filterData
        }
      })
    },
    getCompanyTree() {
      // 根据用户id来获取公司树 不能按照需求来完成 后端暂时没有接口 包括品类也是根据账户信息来获取
      this.$API.GradeConfige.findadd({
        group: 'processModel'
      }).then((res) => {
        let { data } = res
        if (res.code === 200) {
          this.companyFields = Object.assign({}, this.companyFields, {
            dataSource: data
          })
          this.$set(this.flowGroupFields, 'dataSource', res.data)
        }
      })
    },
    companyChange(node) {
      console.log(node)
      if (node.id) {
        this.initCompanyValue = ''
        this.selectCompanyId = node.id
        this.queryStageTempalte(node.id)
      }
    },
    // 先触发 nodeSelected , 然后触发nodeClicked 事件， 这里只处理叶子节点
    nodeClicked() {
      if (this.currentTemplateNode) {
        const { categoryName, id, key, name } = this.currentTemplateNode

        ;(this.formInfo.workflowGroup = categoryName), (this.formInfo.workflowName = name)
        this.formInfo.workflowKey = key
        this.formInfo.workflowId = id
        this.showTreeView = false
      }
    },
    // 判断是否模板节点, true: 是
    checkTemplateNode(treeRef, id) {
      const treeNodes = treeRef.ejsInstances.getTreeData(id)
      return treeNodes.length && treeNodes[0].categoryName
    },
    // 模板树的选中事件，在其中调用接口，获取数据， 忽略叶子节点
    nodeSelected(e) {
      const { nodeData } = e
      this.currentTemplateNode = undefined // 每次事件触发先，先将最近选中的模板置空

      if (this.selectedNodeSet.has(nodeData.id)) {
        return
      }

      ;(this.formInfo.workflowGroup = null), (this.formInfo.workflowName = null)
      this.formInfo.workflowKey = null
      this.formInfo.workflowId = null

      const treeRef = this.$refs.innerTreeView
      if (this.checkTemplateNode(treeRef, nodeData.id)) {
        this.currentTemplateNode = treeRef.ejsInstances.getTreeData(nodeData.id)[0]
      } else {
        this.getTemplateByCategory(nodeData, treeRef)
      }
    },

    // 获取分类下的所有模板
    getTemplateByCategory(nodeData, treeRef) {
      this.$loading()
      let _params = {
        category: nodeData.id,
        current: 1,
        size: 100,
        type: 'ProcessModel',
        key: ''
      }
      this.$API.GradeConfige.getTemplatePage(_params).then((res) => {
        this.$hloading()
        if (!res.data || !res.data.records || res.data.records.length === 0) {
          this.$toast({ content: this.$t('获取模板失败，请重试！'), type: 'warning' })
          return
        }
        let _records = [...res.data.records]
        _records.forEach((e) => {
          e.categoryName = nodeData.text
        })
        treeRef.ejsInstances.addNodes([..._records], nodeData.id)
        this.selectedNodeSet.add(nodeData.id)
      })
    },

    initData() {
      if (this.info && Object.keys(this.info).length) {
        const { id, approveDefineType, workflowKey, status, remark, workflowGroup, workflowName } =
          this.info
        this.formInfo = Object.assign({}, this.formInfo, {
          id,
          approveDefineType,
          workflowKey,
          status,
          remark,
          workflowGroup,
          workflowName
        })
        console.log(123, this.$refs.inputBox)
        setTimeout(() => {
          this.$nextTick(() => {
            this.showTreeView = false
          })
        }, 600)
      }
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
      this.showTreeView = false
    },
    hide() {
      this.showTreeView = false
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          const methodName = this.isEdit ? 'updateGrade' : 'addGrade'
          console.log(this.formInfo.approveDefineType)
          this.approveNodeList.forEach((item) => {
            if (this.formInfo.approveDefineType == item.value) {
              this.formInfo.approveDefineName = item.text
            }
          })
          this.$API.GradeConfige[methodName](this.formInfo)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
                this.showTreeView = false
              }
            })
            .catch((err) => {
              this.$toast({ content: err.msg || this.$t('系统异常'), type: 'error' })
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;

  .close-btn {
    position: absolute;
    right: 0;
    height: 20px;
    line-height: 20px;
    top: 26px;
    font-size: 12px;
    background: #0378d5;
    color: #fff;
    padding: 0 6px;
    border-radius: 4px;
    cursor: pointer;
  }

  /deep/ .tree-view-container {
    box-shadow: inset 0 0 0 1px rgba(232, 232, 232, 1);
    width: 100%;
    position: absolute;
    left: 0;
    top: 50px;
    z-index: 2;
    background: #fff;
    height: 200px;
    overflow: auto;
  }

  /deep/ .e-dlg-content {
    overflow-y: scroll;
  }
}
</style>
