<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('准入流程名称')"
          label-style="top"
          prop="accessTemplateName"
        >
          <mt-input
            v-model="formInfo.accessTemplateName"
            :placeholder="$t('请输入准入流程名称')"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('流程类型')"
          label-style="top"
          prop="accessTemplateType"
        >
          <mt-select
            v-model="formInfo.accessTemplateType"
            :data-source="typeList"
            :disabled="isEdit"
            :placeholder="$t('请选择流程类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('备注：')" label-style="top">
          <mt-input
            v-model="formInfo.remark"
            :multiline="true"
            :rows="2"
            maxlength="200"
            float-label-type="Never"
            :placeholder="$t('字数不超过200字')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { taskTemplateTypeSetting } from '@/utils/setting'
const typeList = []
for (let i in taskTemplateTypeSetting) {
  typeList.push({
    text: taskTemplateTypeSetting[i],
    value: parseInt(i)
  })
}

export default {
  data() {
    return {
      formInfo: {
        accessTemplateName: '', // 准入模板名称
        accessTemplateType: null, // 	准入模板类型
        remark: '' //备注
      },
      typeList,

      rules: {
        accessTemplateName: [
          { required: true, message: this.$t('请输入准入流程名称'), trigger: 'blur' },
          { whitespace: true, message: this.$t('请输入准入流程名称'), trigger: 'blur' }
        ],
        taskTemplateType: [{ required: true, message: this.$t('请选择流程类型'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  mounted() {
    this.show()
    this.isEdit && this.initData()
  },
  methods: {
    // 编辑时初始化数据
    initData() {
      const { id: accessTemplateId, accessTemplateName, accessTemplateType } = this.info
      this.formInfo = Object.assign({}, this.formInfo, {
        accessTemplateId,
        accessTemplateName,
        accessTemplateType
      })
    },

    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      const methodName = this.isEdit ? 'updateAccess' : 'addAccess'
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$API.AccessProcess[methodName](this.formInfo).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
