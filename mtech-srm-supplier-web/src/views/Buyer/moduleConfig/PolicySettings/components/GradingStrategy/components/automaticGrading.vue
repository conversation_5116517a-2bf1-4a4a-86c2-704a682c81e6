<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('分级策略名称')"
          label-style="top"
          prop="labelName"
        >
          <mt-input
            v-model="formInfo.labelName"
            :placeholder="$t('请输入分级策略名称')"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>

        <!-- <mt-form-item
          class="form-item"
          :label="$t('适用组织')"
          label-style="top"
          prop="taskTemplateType"
        >
          <mt-select
            v-model="formInfo.taskTemplateType"
            :data-source="grandlist"
            :placeholder="$t('请选择适用组织')"
          ></mt-select>
        </mt-form-item> -->

        <mt-form-item prop="orgName" class="form-item" :label="$t('适用组织')">
          <template v-if="showCompayInput">
            <mt-DropDownTree
              :fields="companyFields"
              :show-check-box="false"
              id="checkboxTreeSelect"
              :placeholder="this.formInfo.orgName || $t('组织树状图，可选至任意层级、单选')"
              :allow-multi-selection="false"
              :auto-check="false"
              @select="companyChange"
            ></mt-DropDownTree>
          </template>
        </mt-form-item>

        <mt-form-item
          class="form-item"
          :label="$t('考评模板')"
          label-style="top"
          prop="taskTemplateType"
        >
          <mt-select
            v-model="formInfo.taskTemplateType"
            :data-source="taskTemplates"
            :placeholder="$t('请选择考评模板')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item
          class="form-item"
          :label="$t('适用级别')"
          label-style="top"
          prop="taskGrading"
        >
          <mt-multi-select
            v-model="formInfo.taskGrading"
            :data-source="gradingList"
            :show-clear-button="true"
            :allow-filtering="true"
            @select="selectValue"
            @removed="removed"
            ref="multiSelect"
            :placeholder="$t('请选择适用级别')"
          ></mt-multi-select>
        </mt-form-item>

        <template v-for="(item, index) in rangeArray">
          <mt-form-item
            class="form-item"
            :label="item.labelName"
            label-style="top"
            :key="item.id"
            :prop="item.prop"
            v-if="maxMinValue[item.labelType]"
          >
            <!-- value延迟 -->
            <div class="slier-warp">
              <mt-slider
                id="ticks"
                :index="index"
                :tooltip="tooltip"
                :value="maxMinValue[item.labelType]"
                :ticks="ticks"
                step="1"
                :min="0"
                :max="100"
                :limits="rangelimits[item.labelType]"
                @changed="changeRage($event, item.labelType)"
                type="Range"
              ></mt-slider>
            </div>
            <!-- <mt-input
              width="200"
              type="Number"
              v-model="formInfo.extDTOList[index].minNumber"
              :placeholder="$t('请输入最小分级')"
              float-label-type="Never"
            ></mt-input>
            <span>~</span>
            <mt-input
              width="200"
              type="Number"
              v-model="formInfo.extDTOList[index].maxNumber"
              :placeholder="$t('请输入最大分级')"
              float-label-type="Never"
            ></mt-input> -->
          </mt-form-item>
        </template>

        <mt-form-item class="form-item" :label="$t('是否启用')" label-style="left">
          <mt-switch v-model="formInfo.status" :active-value="1" :inactive-value="2"></mt-switch>
        </mt-form-item>

        <mt-form-item class="form-item" :label="$t('备注：')" label-style="top">
          <mt-input
            v-model="formInfo.remark"
            :multiline="true"
            :rows="2"
            maxlength="200"
            float-label-type="Never"
            :placeholder="$t('字数不超过200字')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils.js'
import MtSwitch from '@mtech-ui/switch'
import MtSlider from '@mtech-ui/slider'

export default {
  components: { MtSwitch, MtSlider },
  data() {
    return {
      showCompayInput: false, // 是否展示公司
      companyFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      // 已选的公司对象 单选
      currentCompany: {},
      gradingList: [],
      maxMinValue: [],
      rangelimits: [],

      ticksValue: [30, 70],
      ticks: {
        placement: 'After',
        largeStep: 10,
        smallStep: 10,
        showSmallTicks: true
      },
      tooltip: {
        isVisible: true
      },

      typeList: [],
      extDTOList: {
        gradeCode: '',
        gradeId: '',
        gradeName: ''
      },
      formInfo: {
        orgCode: '',
        orgId: '',
        orgName: '',
        extDTOList: [],

        taskGrading: [], //适用级别
        labelDefineType: 2, //1分级
        labelName: '', // 级别模板名称
        labelType: '', // 级别标识
        status: 1, // 是否启用， 1启用2禁用
        remark: '' //备注
      },
      rangeArray: [], // 已选的级别
      page: {
        current: 1,
        size: 10
      },
      originRules: {
        taskGrading: [{ required: true, message: this.$t('请选择适用级别'), trigger: 'blur' }],

        orgName: [{ required: true, message: this.$t('请选择适用组织'), trigger: 'blur' }],
        labelName: [
          { required: true, message: this.$t('请输入级别名称'), trigger: 'blur' },
          { whitespace: true, message: this.$t('请输入级别名称'), trigger: 'blur' }
        ],
        labelType: [
          { required: true, message: this.$t('请输入级别标识'), trigger: 'blur' },
          { whitespace: true, message: this.$t('请输入级别标识'), trigger: 'blur' }
        ]
      },
      rules: {
        taskGrading: [{ required: true, message: this.$t('请选择适用级别'), trigger: 'blur' }],

        orgName: [{ required: true, message: this.$t('请选择适用组织'), trigger: 'blur' }],
        labelName: [
          { required: true, message: this.$t('请输入级别名称'), trigger: 'blur' },
          { whitespace: true, message: this.$t('请输入级别名称'), trigger: 'blur' }
        ],
        // maxNumber: [
        //   {
        //     required: true,
        //     message: this.$t("请输入分级，最小为")0this.$t("，最大为")100",
        //     trigger: "blur",
        //   },
        // ],
        labelType: [
          { required: true, message: this.$t('请输入级别标识'), trigger: 'blur' },
          { whitespace: true, message: this.$t('请输入级别标识'), trigger: 'blur' }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      levelTypeList: [
        'S',
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'k',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ],
      // 考评模板
      taskTemplates: []
    }
  },
  watch: {
    ticksValue(newValue) {
      console.log(newValue)
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  created() {
    this.initSortArray()
    this.getCompanyTree()
  },
  async mounted() {
    this.show()
    await this.lodeData()
    this.isEdit && this.initData()
  },
  methods: {
    // 将等级改成 {index： value} Obj
    initSortArray() {
      let typeListObj = {}
      for (let [key, value] of Object.entries(this.levelTypeList)) {
        typeListObj[value] = key
      }
      this.typeListObj = typeListObj
    },
    // 根据levellist 升序排序
    sortLevelType() {
      let typeListObj = this.typeListObj
      let result = this.rangeArray.sort((o, n) => {
        return typeListObj[n.labelType] - typeListObj[o.labelType]
      })
      return result
    },
    // 增加
    selectValue(e) {
      let { itemData } = e
      let selectItem = this.gradingList.filter((v) => v.labelType === itemData.labelType)[0]
      selectItem.prop = `maxNumber_${selectItem.labelType}`
      this.rangeArray.push(selectItem)
      this.rangeArray = this.sortLevelType()
      this.renderRules2('plus')
    },
    // 删除
    removed(e) {
      let { itemData } = e
      let index = this.rangeArray.findIndex((v) => v.labelType === itemData.value)
      let vIndex = Object.keys(this.maxMinValue).findIndex((v) => v === itemData.value)
      let maxMinValue = []
      this.rangeArray.splice(index, 1)
      for (let key in this.maxMinValue) {
        if (itemData.value !== key) {
          maxMinValue[`${key}`] = JSON.parse(JSON.stringify(this.maxMinValue[key]))
        }
      }
      this.maxMinValue = maxMinValue
      console.log(this.maxMinValue)
      this.renderRules('minus', itemData.value, vIndex)
    },

    /**
     * 渲染新的slider
     * max min - value
     * max min - limit
     * method = 新增
     * 以value为界限 增加边界
     *
     * method = 减少
     * 以value为界限 扩大边界
     *
     * this.rangeArray 以ABCD正序排序 导致的数据问题
     */
    renderRules2() {
      let rangelimits = [] // 范围
      let maxMinValueList = [] // 值
      this.rangeArray.forEach((item, index) => {
        let range = this.findEmptyRage2(index) // 查找空的区间

        maxMinValueList[`${item.labelType}`] = [range[0], range[0] + 1, item.labelType]

        rangelimits[`${item.labelType}`] = {
          enabled: true,
          minStart: range[0],
          minEnd: range[1],
          maxStart: range[0],
          maxEnd: range[1]
        }
      })
      this.rangelimits = rangelimits
      this.maxMinValue = maxMinValueList

      this.$nextTick(() => {
        this.renderLimit()
      })
    },

    findEmptyRage2(index) {
      let rangeArrayLength = this.rangeArray.length
      let equalLength = Math.floor(100 / rangeArrayLength)
      let range =
        index === 0
          ? [0, equalLength]
          : index === rangeArrayLength - 1
          ? [equalLength * index + 1, 100]
          : [equalLength * index + 1, equalLength * (index + 1)]
      return range
    },

    renderRules(method, value) {
      let existValue = this.maxMinValue // 现有的 ##缓存## 对比
      let labelTypeArr = Object.keys(existValue) // 供应商级别数组 old

      let maxMinValueList = [] // 值
      let rangelimits = [] // 范围
      this.rangeArray.forEach((item, index) => {
        // 已有的 复用原数据
        if (labelTypeArr.includes(item.labelType) && method === 'plus') {
          rangelimits[`${item.labelType}`] = {
            enabled: true,
            minStart: existValue[`${item.labelType}`][0],
            minEnd: existValue[`${item.labelType}`][1],
            maxStart: existValue[`${item.labelType}`][0],
            maxEnd: existValue[`${item.labelType}`][1]
          }
          maxMinValueList[`${item.labelType}`] = JSON.parse(
            JSON.stringify(existValue[`${item.labelType}`])
          )
        } else if (labelTypeArr.includes(item.labelType) && method === 'minus') {
          // 新加的 要对比生成新的范围
          let range = this.findEmptyRage(
            JSON.parse(JSON.stringify(existValue[`${item.labelType}`])),
            index,
            value
          )
          rangelimits[`${item.labelType}`] = {
            enabled: true,
            minStart: range[0],
            minEnd: range[1],
            maxStart: range[0],
            maxEnd: range[1]
          }
          maxMinValueList[`${item.labelType}`] = JSON.parse(
            JSON.stringify(existValue[`${item.labelType}`])
          )
        } else if (item.labelType) {
          // 新加的 要对比生成新的范围
          let range = this.findEmptyRage([], index) // 查找空的区间

          rangelimits[`${item.labelType}`] = {
            enabled: true,
            minStart: range[0],
            minEnd: range[1],
            maxStart: range[0],
            maxEnd: range[1]
          }
          maxMinValueList[`${item.labelType}`] = [range[0], range[0] + 1, item.labelType]
        }
      })

      this.rangelimits = rangelimits
      this.maxMinValue = maxMinValueList
    },

    // 更新limit边界
    renderLimit() {
      let existValue = Object.values(this.maxMinValue)
      let rangelimits = []
      if (existValue.length < 2) {
        return
      }
      // 1 排序
      let sortArray = existValue.sort(function (n, o) {
        return n[0] - o[0]
      })
      console.log(sortArray)
      for (let i = 0; i < sortArray.length; i++) {
        let range = [
          sortArray[i - 1] ? sortArray[i - 1][1] + 1 : 0,
          sortArray[i + 1] ? sortArray[i + 1][0] - 1 : 100
        ]

        rangelimits[`${sortArray[i][2]}`] = {
          enabled: true,
          minStart: range[0],
          minEnd: range[1],
          maxStart: range[0],
          maxEnd: range[1]
        }
      }

      this.rangelimits = rangelimits
    },

    // 查找 0 - 100 之间的没被占用的区间
    // 1 先排序 [[0, 10], [90, 99], [20, 25], [11, 15]] =>
    // [0, 10]
    // [11, 15]
    // [20, 25]
    // [90, 99]
    // 2 对比 掐头去尾 循环寻找空隙
    // return [min - max]
    findEmptyRage(valueArray = []) {
      let range = [0, 100]
      let existValue = Object.values(this.maxMinValue)
      if (existValue.length === 0) {
        return range
      }
      // 1 排序
      let sortArray = existValue.sort(function (n, o) {
        return n[0] - o[0]
      })

      // 删除场景下
      if (valueArray.length > 0) {
        if (existValue.length === 1) {
          return [0, 100]
        }
        let length = sortArray.length
        for (let i = 0; i < sortArray.length; i++) {
          if (valueArray[0] === sortArray[i][0] && valueArray[1] === sortArray[i][1]) {
            if (i === 0 && !!sortArray[1][0]) {
              range = [0, sortArray[1][0]]
              break
            }
            if (i + 1 === length) {
              range = [sortArray[i][0], 100]
              break
            }

            range = [sortArray[i][0], sortArray[i + 1][0] - 1]
            break
          }
        }
        return range
      }
      // 第一个最小值 ！= 0 取得 0-第一个最小值 区间 （掐头去尾）
      if (sortArray[0][0] !== 0) {
        range = [0, sortArray[0][0] - 1]
        return range
      }
      let length = sortArray.length
      if (sortArray[length - 1][1] !== 100) {
        range = [sortArray[length - 1][1] + 1, 100]
        return range
      }

      for (let i = 0; i < sortArray.length; i++) {
        if (sortArray[i][1] !== sortArray[i + 1][0]) {
          range = [sortArray[i][0], sortArray[i + 1][0] - 1]
          break
        }
      }
      return range
    },
    // 滑动结束
    changeRage(e, labelType) {
      console.log('changed', e, labelType)
      let { value } = e
      this.maxMinValue[`${labelType}`] = [value[0], value[1], labelType]
      // 更新界限
      let result = utils.debounce(this.renderLimit, 400)
      result()
    },

    getCompanyTree() {
      // 根据用户id来获取公司树 不能按照需求来完成 后端暂时没有接口 包括品类也是根据账户信息来获取
      // this.$API.supplierInvitation
      //   .findOrganizationCompanyTreeByAccount({
      //     accountId: 0,
      //   })
      this.$API.supplierInvitation
        .findOrganizationCompanyTreeByAccount2({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true
        })
        .then((res) => {
          let { data } = res
          if (res.code === 200 && !utils.isEmpty(data)) {
            this.showCompayInput = true
            this.companyFields = Object.assign({}, this.companyFields, {
              dataSource: data
            })

            this.isEdit &&
              this.companyChange({
                id: this.info.orgId,
                name: this.info.orgName
              })
          } else {
            // 空数据 重置
            this.companyFields = {
              dataSource: [],
              value: 'id',
              test: 'orgCode',
              text: 'name',
              child: 'children'
            }
          }
        })
    },
    companyChange(node) {
      let value = node.id
      if (!value || this.currentCompany.id === value) {
        return
      }

      // 获取 currentCompany
      this.filterNode(this.companyFields.dataSource, node.id)

      // console.log(this.currentCompany);
      this.formInfo.taskTemplateType = this.currentCompany
      this.formInfo.orgCode = this.currentCompany.orgCode
      this.formInfo.orgId = this.currentCompany.id
      this.formInfo.orgName = this.currentCompany.name
    },
    /**
     * 根据公司 去获取对应的工厂  根据工厂 =》 对应的品类
     */
    filterNode(arr, id) {
      if (arr.filter((v) => v.id === id).length > 0) {
        let arrTmp = arr.filter((v) => v.id === id)
        this.currentCompany = arrTmp[0]
      } else {
        arr.forEach((vc) => {
          if (!!vc.children && vc.children.length > 0) {
            this.filterNode(vc.children, id)
          }
        })
      }
    },
    lodeData() {
      return this.$API.GradeConfig.grading({
        page: this.page,
        defaultRules: [{ field: 'labelDefineType', operator: 'equal', value: 1 }]
      }).then((res) => {
        if (res.code == 200) {
          this.typeList = res.data
          const gradingList = []
          for (let i in this.typeList.records) {
            let item = this.typeList.records[i]
            // 战略只能用在手动分级 不能放在自动分级中
            if (item.labelType === 'S') {
              continue
            }
            gradingList.push({
              ...this.typeList.records[i],
              text: this.typeList.records[i].labelName,
              value: this.typeList.records[i].labelType
            })
          }
          this.gradingList = gradingList
        }
      })
    },
    async initData() {
      if (this.info && Object.keys(this.info).length) {
        let detailInfo = await this.queryGradeDetail()
        // console.log(detailInfo);
        if (!!detailInfo.extDTOList && detailInfo.extDTOList.length > 0) {
          this.renderExtDTOList(detailInfo.extDTOList)
        }
        const { id, labelType, status, remark } = this.info

        this.formInfo = Object.assign({}, this.formInfo, {
          id,
          labelName: this.info.gradeStrategyName,
          labelType,
          status,
          remark
        })
      }
    },

    // 渲染已有的数据
    renderExtDTOList(extDTOList) {
      let maxMinValueList = [] // 值
      let rangelimits = [] // 范围
      let fullInfo = []
      let taskGrading = []
      extDTOList.forEach((v) => {
        let rankItem = this.gradingList.filter((item) => v.gradeName === item.labelName)
        if (!!rankItem && rankItem.length > 0) {
          fullInfo.push({
            ...v,
            ...rankItem[0]
          })
        }
      })

      let typeListObj = this.typeListObj
      fullInfo = fullInfo.sort((o, n) => {
        return typeListObj[n.labelType] - typeListObj[o.labelType]
      })

      fullInfo.forEach((item) => {
        taskGrading.push(item.labelType)
        let range = [item.minApplicableLevel, item.maxApplicableLevel]
        item.prop = `maxNumber_${item.labelType}`
        this.rangeArray.push(item)
        rangelimits[`${item.labelType}`] = {
          enabled: true,
          minStart: range[0],
          minEnd: range[1],
          maxStart: range[0],
          maxEnd: range[1]
        }
        maxMinValueList[`${item.labelType}`] = [
          item.minApplicableLevel,
          item.maxApplicableLevel,
          item.labelType
        ]
      })
      // console.log(taskGrading);
      this.formInfo.taskGrading = taskGrading
      this.rangelimits = rangelimits
      this.maxMinValue = maxMinValueList

      this.$nextTick(() => {
        this.renderLimit()
      })
    },
    queryGradeDetail() {
      this.$loading()
      return this.$API.GradeConfig['queryGradeDetail']({
        id: this.info.id
      })
        .then((res) => {
          this.$hloading()
          if (res.code == 200) {
            return res.data
          } else {
            return {}
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({ content: err.msg || this.$t('系统异常'), type: 'error' })
        })
    },
    show() {
      console.log(this.modalData)
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      console.log(this.typeList)
      let existValue = Object.values(this.maxMinValue)
      let extDTOList = []
      this.gradingList.forEach((item) => {
        let rangeItem = existValue.filter((citem) => citem[2] === item.labelType)[0]
        !!rangeItem &&
          extDTOList.push({
            gradeCode: item.labelCode,
            gradeId: item.id,
            gradeName: item.labelName,
            maxApplicableLevel: rangeItem[1],
            minApplicableLevel: rangeItem[0]
          })
      })

      this.typeList.records.forEach((item) => {
        if (this.gradingList.text == item.labelName) {
          this.extDTOList.gradeCode = item.labelCode
          this.extDTOList.gradeId = item.labelType
          this.extDTOList.gradeName = item.labelName
        }
      })

      let query = {
        orgCode: this.formInfo.orgCode,
        orgId: this.formInfo.orgId,
        orgName: this.formInfo.orgName,
        extDTOList,
        gradeStrategyCode: this.formInfo.id,
        gradeStrategyName: this.formInfo.labelName,
        remark: this.formInfo.remark,
        status: this.formInfo.status
      }
      if (this.isEdit) {
        query.id = this.formInfo.id
      }

      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          const methodName = this.isEdit ? 'queryupdateGrade' : 'queryAddGrade'
          this.$API.GradeConfig[methodName](query)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
            .catch((err) => {
              this.$toast({ content: err.msg || this.$t('系统异常'), type: 'error' })
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    changeSelect(e) {
      console.log('select', e)
      console.log(this.$refs.multiSelect.ejsRef.ej2Instances.value)
    },
    changeChange(e) {
      console.log('change', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;

  .slier-warp {
    padding: 0 20px;
  }
}
</style>
