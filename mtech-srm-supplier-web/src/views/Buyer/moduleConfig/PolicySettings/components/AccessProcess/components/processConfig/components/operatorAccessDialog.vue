<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :xs="12" :md="12" :lg="12">
            <mt-form-item
              class="form-item"
              :label="$t('名称')"
              label-style="top"
              prop="accessTemplateName"
            >
              <mt-input
                v-model="formInfo.accessTemplateName"
                max-length="50"
                :placeholder="
                  method === 'access' ? $t('请输入准入流程名称') : $t('请输入认证流程名称')
                "
                float-label-type="Never"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :xs="12" :md="12" :lg="12">
            <mt-form-item
              class="form-item"
              :label="$t('流程类型')"
              label-style="top"
              prop="accessTemplateType"
            >
              <mt-select
                v-model="formInfo.accessTemplateType"
                :data-source="typeList"
                :disabled="isEdit"
                @select="selectTemplateType($event)"
                :placeholder="$t('请选择流程类型')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <!-- <mt-form-item
          class="form-item"
          :label="$t('触发条件')"
          label-style="top"
          prop="triggerCondition"
        >
          <mt-multi-select
            v-model="formInfo.triggerCondition"
            :data-source="conditionsList"
            :show-clear-button="true"
            :disabled="isEdit"
            :placeholder="$t('请选择触发条件')"
          ></mt-multi-select>
        </mt-form-item> -->
        <!-- <mt-form-item class="form-item" label="备注：" label-style="top">
          <mt-input
            v-model="formInfo.remark"
            :multiline="true"
            :rows="2"
            maxlength="200"
            float-label-type="Never"
            placeholder="字数不超过200字"
          ></mt-input>
        </mt-form-item> -->
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
// import {
//   taskTemplateTypeSettingAccess,
//   taskTemplateTypeSettingAuthor,
// } from "@/utils/setting";
// const typeList = [];
// const authorList = [];
// for (let i in taskTemplateTypeSettingAccess) {
//   typeList.push({
//     text: taskTemplateTypeSettingAccess[i],
//     value: parseInt(i),
//   });
// }
// for (let i in taskTemplateTypeSettingAuthor) {
//   authorList.push({
//     text: taskTemplateTypeSettingAuthor[i],
//     value: parseInt(i),
//   });
// }
const typeList = JSON.parse(sessionStorage.getItem('accessProcessTypeList'))
const authorList = JSON.parse(sessionStorage.getItem('accessProcessAuthorList'))

export default {
  data() {
    return {
      formInfo: {
        accessTemplateName: '', // 准入模板名称
        accessTemplateType: '', // 	准入模板类型
        // triggerCondition: [], // 	触发条件
        remark: '' //备注
      },
      typeList,
      authorList,
      conditionsList: [
        {
          value: 0,
          text: this.$t('手动')
        },
        {
          value: 1,
          text: this.$t('首次供应商注册')
        },
        {
          value: 2,
          text: this.$t('品类注册')
        },
        {
          value: 3,
          text: this.$t('淘汰（推出）后启动')
        }
      ],

      rules: {
        accessTemplateName: [
          { required: true, message: this.$t('请输入准入流程名称'), trigger: 'blur' },
          { whitespace: true, message: this.$t('请输入准入流程名称'), trigger: 'blur' }
        ],
        accessTemplateType: [
          { required: true, message: this.$t('请选择流程类型'), trigger: 'blur' }
        ]
        // triggerCondition: [
        //   { required: true, message: this.$t("请选择触发条件"), trigger: "blur" },
        // ],
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    _this() {
      return this.modalData._this
    },
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    },
    method() {
      return this.modalData.method
    }
  },
  mounted() {
    this.show()
    this.typeList = this.method === 'access' ? this.typeList : this.authorList
    this.isEdit && this.initData()
  },
  methods: {
    selectTemplateType(e) {
      const { itemData } = e
      this.formInfo.accessTemplateType = itemData.value
    },
    // 编辑时初始化数据
    initData() {
      // 初始化 准入 认证流程
      const { id: accessTemplateId, accessTemplateName, accessTemplateType } = this.info
      this.formInfo = Object.assign({}, this.formInfo, {
        accessTemplateId,
        accessTemplateName,
        accessTemplateType
      })
    },

    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      const methodName = this.isEdit ? 'updateAccess' : 'addAccess'
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          let { accessTemplateId, accessTemplateType, accessTemplateName } = this.formInfo
          this.$API.AccessProcess[methodName]({
            accessTemplateId,
            accessTemplateType,
            accessTemplateName
          }).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
