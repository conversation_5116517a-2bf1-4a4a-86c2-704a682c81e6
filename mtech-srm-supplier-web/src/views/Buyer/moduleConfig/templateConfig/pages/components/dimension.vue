<template>
  <!-- 详情里面增维度设置 -->
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formInfo" :rules="rules">
        <mt-form-item prop="dimensionName" :label="$t('维度名称：')">
          <mt-input
            :max="30"
            :show-clear-button="false"
            v-model="formInfo.dimensionName"
            float-label-type="Never"
            :maxlength="200"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="weight" :label="$t('权重（%）：')">
          <mt-input
            :max-length="3"
            :max="100"
            type="number"
            oninput="if(value>100)value=100;if(value.length>4)value=value.slice(0,5);"
            v-model="formInfo.weight"
            :placeholder="$t('输入权重1~100')"
            @change="changeWeight"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('合格线')" label-style="top" prop="qualifiedScore">
          <div style="display: flex">
            <mt-select
              class="qualified"
              v-model="formInfo.qualifiedSymbol"
              :data-source="symbolList"
              :placeholder="$t('请选择')"
              width="195"
              @change="symbolChange"
            ></mt-select>
            <mt-input
              oninput="if(value>1000000000000000){value=100000000000000}else{value=value.replace(/[^\d]/g,'')}if(value.indexOf(0)==0){value=0}"
              :show-clear-button="true"
              :show-spin-button="false"
              v-model="formInfo.qualifiedScore"
              :placeholder="$t('非负整数')"
              width="190"
              @input="qualifiedScoreChange"
            ></mt-input>
          </div>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      symbolList: [
        {
          text: '>',
          value: '1'
        },
        {
          text: '<',
          value: '2'
        },
        {
          text: '≥',
          value: '3'
        },
        {
          text: '≤',
          value: '4'
        },
        {
          text: '=',
          value: '5'
        }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formInfo: {
        qualifiedSymbol: '1',
        dimensionName: '',
        qualifiedScore: '',
        weight: null,
        keyid: ''
      },
      rules: {
        dimensionName: [
          {
            required: true,
            message: this.$t('请输入维度名称'),
            trigger: 'blur'
          }
        ],
        weight: [{ required: true, message: this.$t('请设置权重'), trigger: 'blur' }],
        qualifiedScore: [
          {
            required: true,
            message: this.$t('请设置合格线分数'),
            trigger: 'blur'
          }
        ]
      },
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    sum() {
      return this.modalData.sum //传值分数
    },
    typename() {
      return this.modalData.typename //传值分数
    },
    soucor() {
      return this.modalData.soucor //传值分数
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formInfo = { ...this.modalData.data }
    }
    if (this.modalData && this.modalData.dimensionList) {
      this.dimensionList = this.modalData.dimensionList
      if (!this.editStatus) {
        //如果是新增操作，默认赋值第一条数据
        if (this.dimensionList.length) {
          this.formInfo.dimensionId = this.dimensionList[0]['dimensionId']
        }
      }
    }
  },
  created() {},
  methods: {
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    changeWeight(e) {
      this.formInfo.weight = e
    },
    symbolChange(e) {
      if (e.value != null && this.formInfo.qualifiedScore != null) {
        this.formInfo.symbolAndQualified = true
      } else {
        this.formInfo.symbolAndQualified = null
      }
    },
    qualifiedScoreChange(e) {
      if (e != null && this.formInfo.qualifiedSymbol != null) {
        this.formInfo.symbolAndQualified = true
      } else {
        this.formInfo.symbolAndQualified = null
      }
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.formInfo))
          params.keyid = params.keyid ? params.keyid : this.randomString()
          if (Number(params.qualifiedScore) <= this.modalData.soucor) {
            this.isabsole = true
            if (this.modalData.typename != undefined) {
              this.modalData.typename.forEach((item) => {
                if (params.dimensionName == item) {
                  this.isabsole = false
                }
              })
            }
            if (this.isabsole == false) {
              this.$toast({
                content: this.$t('维度名称已存在'),
                type: 'warning'
              })
            } else {
              let b
              if (this.modalData.sum != undefined) {
                b = Number(params.weight) + this.modalData.sum
                if (b > 100) {
                  this.$toast({
                    content: this.$t('维度权重之和不能大于100'),
                    type: 'warning'
                  })
                } else {
                  if (params.weight && params.weight > 0 && params.weight <= 100) {
                    if (this.editStatus) {
                      console.log('维度设置--编辑', params)
                      this.$emit('confirm-function', params)
                    } else {
                      //新增操作，可以下拉选择维度
                      console.log('维度设置--新增', params)
                      this.$emit('confirm-function', params)
                    }
                  } else {
                    this.$toast({
                      content: this.$t('权重，设置有误.'),
                      type: 'warning'
                    })
                  }
                }
              } else {
                if (params.weight && params.weight > 0 && params.weight <= 100) {
                  if (this.editStatus) {
                    console.log('维度设置--编辑', params)
                    this.$emit('confirm-function', params)
                  } else {
                    //新增操作，可以下拉选择维度
                    console.log('维度设置--新增', params)
                    this.$emit('confirm-function', params)
                  }
                } else {
                  this.$toast({
                    content: this.$t('权重，设置有误.'),
                    type: 'warning'
                  })
                }
              }
            }
          } else {
            this.$toast({
              content: this.$t('合格分只能小于等于满分'),
              type: 'warning'
            })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.qualified {
  float: left;
  margin-right: 10px;
}
</style>
