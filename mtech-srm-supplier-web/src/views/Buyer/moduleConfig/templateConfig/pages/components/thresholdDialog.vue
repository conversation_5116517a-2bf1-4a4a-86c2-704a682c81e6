// 门槛
<template>
  <mt-dialog ref="thresholdDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <mt-template-page ref="template1" :template-config="pageConfig"></mt-template-page>
  </mt-dialog>
</template>
<script>
import { thresholdDefcolumns } from '../config/index'

const editSettings = {
  allowEditing: true,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: true,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false
}

export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: 'eea2b428-a966-4547-9a9e-b7657bbe586a',
          isUseCustomEditor: true,
          toolbar: {
            tools: []
          },
          grid: {
            editSettings,
            allowPaging: false,
            columnData: [],
            dataSource: []
          }
        }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    data() {
      return this.modalData.data
    }
  },
  mounted() {
    console.log()
    thresholdDefcolumns[5].valueConverter.map = JSON.parse(
      sessionStorage.getItem('thresholdProjectList')
    )
    thresholdDefcolumns[3].valueConverter.map = JSON.parse(sessionStorage.getItem('formTypeList'))
    this.initData()
    this.show()
  },
  methods: {
    initData() {
      this.pageConfig[0].grid.columnData = thresholdDefcolumns
      this.pageConfig[0].grid.dataSource = this.data
    },

    show() {
      this.$refs['thresholdDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['thresholdDialog'].ejsRef.hide()
    },
    confirm() {
      this.$emit(
        'confirm-function',
        this.$refs.template1.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
/deep/ .relative-select {
  display: flex;
  justify-content: space-between;
  .mt-drop-down-tree {
    width: 300px;
  }
}
</style>
