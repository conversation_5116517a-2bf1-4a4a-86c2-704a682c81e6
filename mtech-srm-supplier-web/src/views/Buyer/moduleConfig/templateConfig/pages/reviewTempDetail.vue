<template>
  <!-- // 新增评审模板详情 -->
  <div class="tempalte-detail mt-flex-direction-column" style="width: 100%">
    <div class="page-bottom mt-flex">
      <div class="page-content fbox">
        <div class="detail-top">
          <div class="left" style="width: 93%">
            <div class="title">{{ formObject.templateName }}</div>
            <div class="form">
              <div class="form_div">
                {{ $t('模板编码：') }}
                {{ formObject.templateCode }}
              </div>
              <div class="form_div">
                {{ $t('模板名称：') }}{{ formObject.templateName }}
                {{ formObject.createDate }}
              </div>
              <div class="form_div">{{ $t(' 类型：') }}{{ formObject.templateTypeName }}</div>
              <div class="form_div">{{ $t('创建人：') }}{{ formObject.createUserName }}</div>
            </div>
            <div class="data">
              <div class="form_div">{{ $t('通过条件：') }}{{ formObject.ruleNames }}</div>
              <div class="form_div">
                {{ $t(' 公司：') }}
                <span>{{ formObject.orgName }}</span>
              </div>
              <div class="form_div">
                {{ $t(' 适用品类：') }}
                <span>{{ formObject.categoryName || $t('通用') }}</span>
              </div>
              <div class="form_div">
                {{ $t(' 满分：') }}
                <span>{{ formObject.templateFullScore }}</span>
              </div>
              <div class="form_div">
                {{ $t(' 创建时间：') }}
                <span>{{ formObject.createTime }}</span>
              </div>
            </div>
          </div>
          <div class="right">
            <div class="right_but" @click="cancel">{{ $t('返回') }}</div>
            <div v-if="insourcel" class="right_but" @click="confirm">{{ $t('保存') }}</div>
          </div>
        </div>
        <div class="detail-content">
          <vxe-toolbar>
            <template #buttons>
              <vxe-button @click="handleAddDimension" status="primary" size="small">{{
                $t('新增维度')
              }}</vxe-button>
            </template>
          </vxe-toolbar>
          <ScTable
            ref="xTable"
            max-height="100%"
            :row-config="{ height: 48 }"
            :columns="columns"
            :table-data="tableData"
            :tree-config="treeConfig"
            :tooltip-config="{ contentMethod: showTooltipMethod, enterable: true }"
          >
            >
          </ScTable>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { reviewTemplateData } from './config'
import { i18n } from '@/main'
import ScTable from '@/components/ScTable/src/index'
export default {
  components: {
    ScTable
  },
  data() {
    return {
      reviewTemplateId: '',
      soucor: null, //分数
      insourcel: true, //保存控制显示按钮
      twisabsole: false,
      abslue: false,
      slue: false,
      isabsole: false,
      editStatus: false,
      cry: true,
      formObject: {},
      tableData: [],
      detailInfo: {
        templateId: null, //模板id
        ownerOrgId: null, //	所属组织id
        dimensionDTOS: [
          {
            indexDTOS: [
              {
                indexId: null, //	指标id      ok
                indexName: null, //	指标名称      ok
                weight: null, //指标权重      ok
                allocateScore: null, //分配分值   ok  通过权重 计算出来
                indexFullScore: null, //指标满分      ok  维度没有
                indexFromSource: null, //	指标来源      ok  维度没有
                scoreStandard: null //评分标准      ok  维度没有
              }
            ],
            weight: null, //维度权重      ok
            allocateScore: null, //分配分值   ok  通过权重 计算出来
            dimensionName: null, //维度id      ok
            dimensionId: null //	维度名称      ok
          }
        ]
      },
      columns: [
        {
          field: 'dimensionName',
          title: i18n.t('名称'),
          width: '300',
          treeNode: true,
          slots: {
            default: ({ row, rowIndex }) => {
              return [
                <div style='width:100%;flex-direction: column; display: inline-flex;justify-content: space-between;'>
                  <span class='row-demension-text'>{row.dimensionName}</span>
                  <div style='width:100%; display: flex;'>
                    <vxe-button
                      status='primary'
                      v-show={!row.parentDimensionCode}
                      icon='mt-icons mt-icon-icon_card_plus'
                      type='text'
                      size='mini'
                      onClick={() => {
                        this.handleAddIndex(row, rowIndex)
                      }}>
                      添加指标
                    </vxe-button>
                    <vxe-button
                      status='primary'
                      v-show={!row.parentDimensionCode}
                      icon='mt-icons mt-icon-icon_list_edit'
                      type='text'
                      size='mini'
                      onClick={() => {
                        if (!row.reviewStandard) {
                          this.handleEditDimension(row)
                        } else {
                          this.handleEditIndex(row)
                        }
                      }}>
                      编辑
                    </vxe-button>
                    <vxe-button
                      status='primary'
                      icon='mt-icons mt-icon-icon_list_delete'
                      type='text'
                      size='mini'
                      onClick={() => {
                        if (!row.reviewStandard) {
                          this.handleDeleteDimension(row)
                        } else {
                          this.handleDeleteIndex(row)
                        }
                      }}>
                      删除
                    </vxe-button>
                  </div>
                </div>
              ]
            }
          }
        },
        {
          field: 'weight',
          title: i18n.t('分配权重（%)')
        },
        {
          field: 'indexFullScore',
          title: i18n.t('分值范围')
        },
        // {
        //   field: 'useSet',
        //   title: i18n.t('允许不适用'),
        //   width: 100
        // },
        {
          field: 'qualifiedScore',
          title: i18n.t('合格线'),
          width: 220,
          slots: {
            default: ({ row }) => {
              return [
                <div style='width:100%;flex-direction: row; display: inline-flex;justify-content: space-between;'>
                  <vxe-select
                    v-model={row.qualifiedSymbol}
                    placeholder='i18n.t("请选择")'
                    transfer
                    size='mini'
                    onChange={() => {
                      this.symbolChange(row)
                    }}>
                    <vxe-option value='1' label='>'></vxe-option>
                    <vxe-option value='2' label='<'></vxe-option>
                    <vxe-option value='3' label='≥'></vxe-option>
                    <vxe-option value='4' label='≤'></vxe-option>
                    <vxe-option value='5' label='='></vxe-option>
                  </vxe-select>
                  <vxe-input
                    v-model={row.qualifiedScore}
                    type='integer'
                    size='mini'
                    min={row.lowestScore}
                    max='999'
                    clearable
                    onChange={() => {
                      this.changeScore(row)
                    }}
                  />
                </div>
              ]
            }
          }
        },
        {
          field: 'allocateScore', //symbol  score
          title: i18n.t('分配分值'),
          width: '200',
          slots: {
            default: ({ row }) => {
              return [
                <div style='width:100%;flex-direction: row; display: inline-flex;justify-content: space-between;'>
                  <span v-show={!row.parentDimensionCode} class='row-demension-text'>
                    {row.allocateScore}
                  </span>
                  <vxe-input
                    v-show={row.parentDimensionCode}
                    v-model={row.allocateScore}
                    type='integer'
                    size='mini'
                    min={0}
                    max={100}
                    clearable
                    onChange={() => {
                      this.allocateScoreChange(row)
                    }}
                  />
                </div>
              ]
            }
          }
        },
        {
          field: 'reviewStandard',
          title: i18n.t('评分标准')
        }
      ],
      treeConfig: {
        transform: true,
        rowField: 'reviewCode',
        parentField: 'parentDimensionCode',
        expandAll: true
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    }
  },
  mounted() {
    this.getFormDetail(this.$route.query.id, this.$route.query.status)
    this.reviewTemplateId = this.$route.query.id
  },
  methods: {
    // 合格线标准选择
    symbolChange(e) {
      for (let i = 0; i < reviewTemplateData.length; i++) {
        if (reviewTemplateData[i].reviewCode === e.reviewCode) {
          reviewTemplateData[i].qualifiedSymbol = e.qualifiedSymbol
          break
        }
        reviewTemplateData[i]?.indexDTOS.forEach((item) => {
          if (item.reviewCode === e.reviewCode) {
            item.qualifiedSymbol = e.qualifiedSymbol
          }
        })
      }
    },
    // 合格线改变
    changeScore(e) {
      for (let i = 0; i < reviewTemplateData.length; i++) {
        if (reviewTemplateData[i].reviewCode === e.reviewCode) {
          reviewTemplateData[i].qualifiedScore = e.qualifiedScore
          break
        }
        reviewTemplateData[i]?.indexDTOS.forEach((item) => {
          if (item.reviewCode === e.reviewCode) {
            item.qualifiedScore = e.qualifiedScore
          }
        })
      }
    },
    allocateScoreChange(e) {
      for (let i = 0; i < reviewTemplateData.length; i++) {
        if (reviewTemplateData[i].reviewCode === e.reviewCode) {
          reviewTemplateData[i].allocateScore = e.allocateScore
          break
        }
        reviewTemplateData[i]?.indexDTOS.forEach((item) => {
          if (item.reviewCode === e.reviewCode) {
            item.allocateScore = e.allocateScore
          }
        })
      }
    },
    showTooltipMethod({ column, row }) {
      // 重写默认的提示内容
      if (column.property === 'dimensionName' && row[column.property]?.length > 20) {
        return row[column.property]
      }
    },
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    getFormDetail(id, status) {
      if (status == '1') {
        this.insourcel = false
      }
      //模板详情
      this.reviewTemplateId = id
      this.$API.ModuleConfig.queryReviewTempDetail(id).then((res) => {
        this.soucor = res.data.templateFullScore //取满分
        this.detailInfo = res.data
        this.formObject = { ...res.data }
        let _detailGridList = []
        if (
          res &&
          res.data &&
          Array.isArray(res.data.dimensionDTOS) &&
          res.data.dimensionDTOS.length
        ) {
          _detailGridList = res.data.dimensionDTOS
        } else {
          _detailGridList = []
        }
        _detailGridList.forEach((e) => {
          //以下几个字段, 维度列表没有，需要补全
          e.indexFullScore = null //指标满分
          e.indexFromSource = null //指标来源
          e.scoreStandard = null //评分标准
          e.reviewCode = e.dimensionCode
          e.lowestScore = null
          e.highestScore = null
          e.parentDimensionCode = null
          e.name = e.dimensionName //维度Name
          e.id = e.dimensionId //维度ID
          e.showDeleteIcon = true
          e.keyid = this.randomString()
          if (Array.isArray(e.indexDTOS)) {
            e.indexDTOS.forEach((i) => {
              i.dimensionName = i.reviewName
              i.name = i.indexName //指标Name
              i.id = i.indexId //指标ID
              i.showDeleteIcon = Boolean(!i.indexFromSource) //存在指标来源，不可删除
              i.parentDimensionId = e.dimensionId
              i.parentDimensionName = e.dimensionName
            })
          }
          let _hiddenDeleteIcon = e.indexDTOS.filter((f) => !f.showDeleteIcon) //下级列表中，存在指标来源，不可删除
          if (_hiddenDeleteIcon.length) e.showDeleteIcon = false
        })
        reviewTemplateData.length = 0
        this.$nextTick(() => {
          cloneDeep(_detailGridList).forEach((e) => reviewTemplateData.push(e))
          this.orderData = reviewTemplateData
          this.tableData = this.flatTableData(reviewTemplateData)
        })
      })
    },
    // 数据重组
    flatTableData(list) {
      let arr = []
      list.forEach((item) => {
        arr = arr.concat(item.indexDTOS || [])
      })
      const res = arr.concat(list)
      res.forEach((item) => {
        item.indexFullScore = item.highestScore
          ? `${item.lowestScore || 0}~${item.highestScore}`
          : ''
        // item.range = item.scoreEnd ? `${item.lowestScore || 0}-${item.highestScore}` : ''
      })
      this.$nextTick(() => {
        this.$refs.xTable.setAllTreeExpand(true)
      })
      return res
    },
    //设置维度分值
    setDimensionScore(w) {
      if (+w) {
        return (+this.formObject.templateFullScore * +w) / 100
        // return (+this.formObject.score * +w) / 100
      } else {
        return 0
      }
    },
    //设置指标分值
    setIndexScore(dW, iW) {
      if (+dW && +iW) {
        return (+this.formObject.templateFullScore * +dW * +iW) / 10000
        // return (+this.formObject.score * +dW * +iW) / 10000
      } else {
        return 0
      }
    },
    //新增维度操作
    handleAddDimension() {
      let artost = reviewTemplateData
      if (artost[0] != undefined) {
        let a = 0
        let crr = []
        for (let i = 0; i < artost.length; i++) {
          a += Number(artost[i].weight)
          crr.push(artost[i].dimensionName)
        }
        if (a > 100 || a == 100) {
          this.$toast({
            content: this.$t('维度权重之和已满100'),
            type: 'warning'
          })
        } else {
          this.$dialog({
            modal: () => import('./components/dimension.vue'),
            data: {
              title: this.$t('添加维度信息'),
              sum: a,
              typename: crr,
              soucor: this.soucor
            },
            success: (data) => {
              this.editStatus = true //执行过数据编辑
              let _data = cloneDeep(data)
              _data.showDeleteIcon = true
              _data.allocateScore = this.setDimensionScore(data.weight)
              //以下几个字段, 维度列表没有，需要补全
              _data.indexFullScore = null //指标满分
              _data.indexFromSource = null //指标来源
              _data.scoreStandard = null //评分标准
              _data.reviewCode = data.keyid
              _data.lowestScore = null
              _data.highestScore = null
              _data.parentDimensionCode = null
              _data.name = data.dimensionName //维度Name
              _data.id = data.dimensionId //维度ID
              _data.showDeleteIcon = true
              reviewTemplateData.unshift(cloneDeep(_data))
              this.tableData = this.flatTableData(cloneDeep(reviewTemplateData))
            }
          })
        }
      } else {
        this.$dialog({
          modal: () => import('./components/dimension.vue'),
          data: {
            title: this.$t('添加维度信息'),
            soucor: this.soucor
          },
          success: (data) => {
            this.editStatus = true //执行过数据编辑
            let _data = cloneDeep(data)
            _data.showDeleteIcon = true
            _data.allocateScore = this.setDimensionScore(data.weight)
            //以下几个字段, 维度列表没有，需要补全
            _data.indexFullScore = null //指标满分
            _data.indexFromSource = null //指标来源
            _data.scoreStandard = null //评分标准
            _data.reviewCode = data.keyid
            _data.lowestScore = null
            _data.highestScore = null
            _data.parentDimensionCode = null
            _data.name = data.dimensionName //维度Name
            _data.id = data.dimensionId //维度ID
            _data.showDeleteIcon = true
            reviewTemplateData.unshift(cloneDeep(_data))
            this.tableData = this.flatTableData(cloneDeep(reviewTemplateData))
          }
        })
      }
    },
    //编辑维度操作
    handleEditDimension(e) {
      let artost = reviewTemplateData
      let crr = []
      let num = 0
      for (let i = 0; i < artost.length; i++) {
        if (e.dimensionName != artost[i].dimensionName) {
          crr.push(artost[i].dimensionName)
          num += Number(artost[i].weight)
        }
      }
      // if (crr.indexOf(e.data.dimensionName) != -1) {
      //   crr.splice(crr.indexOf(e.data.dimensionName), 1);
      // }

      this.$dialog({
        modal: () => import('./components/dimension.vue'),
        data: {
          title: this.$t('修改维度信息-'),
          soucor: this.soucor,
          data: e,
          sum: num,
          typename: crr
        },
        success: (data) => {
          this.editStatus = true //执行过数据编辑
          reviewTemplateData.forEach((e) => {
            if (e.keyid == data.keyid) {
              e.weight = data.weight
              e.dimensionName = data.dimensionName
              e.qualifiedScore = data.qualifiedScore
              e.allocateScore = this.setDimensionScore(e.weight)
            }
          })
          this.tableData = this.flatTableData(cloneDeep(reviewTemplateData))
          // this.dispose()
        }
      })
    },
    //删除维度操作
    handleDeleteDimension(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除当前维度？')
        },
        success: () => {
          for (let i = 0; i < reviewTemplateData.length; i++) {
            if (data.dimensionName == reviewTemplateData[i].dimensionName) {
              reviewTemplateData.splice(i, 1)
              i--
            }
          }
          this.tableData = this.flatTableData(cloneDeep(reviewTemplateData))
        }
      })
    },
    // 和平计算
    dispose() {
      this.tableData.forEach((item) => {
        if (item.indexDTOS) {
          let num = (item.weight / item.indexDTOS.length).toFixed(0)
          item.indexDTOS.forEach((e, i) => {
            if (i == item.indexDTOS.length - 1) {
              e.weight = item.weight - num * (item.indexDTOS.length - 1)
            } else {
              e.weight = num
            }
          })
        }
      })
    },
    //新增指标操作
    handleAddIndex(row, index) {
      this.$API.ModuleConfig.queryReviewDef({
        page: { current: 1, size: 10000 }
      }).then((res) => {
        let _indexDTOS = []
        if (res && res.data.records && Array.isArray(res.data.records)) {
          _indexDTOS = res.data.records
        } else {
          _indexDTOS = []
        }

        if (_indexDTOS.length < 1) {
          this.$toast({
            content: this.$t('当前维度下，未设置指标。或者指标未设置"启用"'),
            type: 'warning'
          })
          return
        }

        let _usedIndexList = [], //当前维度下，已经使用过的指标ID列表
          _unUseIndexList = [] //当前维度下，未使用的指标列表
        reviewTemplateData &&
          reviewTemplateData.forEach((f) => {
            //所有数据过滤
            f.indexDTOS &&
              f.indexDTOS.forEach((i) => {
                _usedIndexList.push(i)
              })
          })
        if (_usedIndexList && _usedIndexList.length > 0) {
          _unUseIndexList = _indexDTOS.filter(
            (item) => !_usedIndexList.some((el) => el.reviewId == item.reviewId)
          )
        } else {
          _unUseIndexList = _indexDTOS
        }
        if (_unUseIndexList.length) {
          this.$dialog({
            modal: () => import('./components/reviewItemDialog.vue'),
            data: {
              title: this.$t('添加指标信息'),
              indexDTOS: _usedIndexList,
              parent: row
            },
            success: (data) => {
              this.editStatus = true //执行过数据编辑
              reviewTemplateData.forEach((item, idx) => {
                if (item.dimensionCode === row.dimensionCode) {
                  if (!item.indexDTOS || item.indexDTOS.length < 1) {
                    item.indexDTOS = []
                  }
                  // let _dataLength = item.indexDTOS.length + data.length
                  data.forEach((ele) => {
                    if (index === idx) {
                      item.indexDTOS.push({
                        ...ele,
                        parentDimensionCode: row.reviewCode,
                        dimensionName: ele.reviewName,
                        // weight: (item.weight / _dataLength).toFixed(0),
                        showDeleteIcon: true,
                        allocateScore: 0
                      })
                    }
                  })
                }
              })
              this.tableData = this.flatTableData(cloneDeep(reviewTemplateData))
              // this.dispose()
            }
          })
        } else {
          this.$toast({
            content: this.$t('当前维度下，所有指标均已配置'),
            type: 'warning'
          })
        }
      })
    },
    //编辑指标操作
    handleEditIndex(e) {
      this.$dialog({
        modal: () => import('./components/reviewItemDialog.vue'),
        data: {
          title: this.$t('编辑指标信息'),
          data: e.data,
          soucor: this.soucor
        },
        success: (data) => {
          this.editStatus = true //执行过数据编辑
          this.tableData.forEach((item) => {
            if (item.dimensionId === data.parentDimensionId) {
              item.indexDTOS.forEach((el) => {
                if (el.indexId === data.indexId) {
                  el.weight = data.weight
                  el.allocateScore = this.setIndexScore(item.weight, el.weight)
                }
              })
            }
          })
        }
      })
    },
    //删除指标操作
    handleDeleteIndex(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除当前指标？')
        },
        success: () => {
          // let dimensionName = data.parentDimensionName
          reviewTemplateData.forEach((item) => {
            // if (item.dimensionName == dimensionName) {
            for (let i = 0; i < item.indexDTOS.length; i++) {
              if (item.indexDTOS[i].dimensionName == data.dimensionName) {
                item.indexDTOS.splice(i, 1)
                i--
              }
            }
            // }
          })
          this.tableData = this.flatTableData(JSON.parse(JSON.stringify(reviewTemplateData)))
          // this.dispose()
        }
      })
    },

    confirm() {
      //遍历数据，移除无用的临时字段
      let _dataSource = JSON.parse(JSON.stringify(reviewTemplateData))
      let a = 0
      for (let i = 0; i < _dataSource.length; i++) {
        a += Number(_dataSource[i].weight)
      }
      if (a < 100) {
        this.$toast({
          content: this.$t('维度权重之和小于100'),
          type: 'warning'
        })
      } else {
        _dataSource.forEach((item) => {
          this.cry = true
          if (item.indexDTOS == undefined) {
            this.cry = false
          }
        })
        if (this.cry == false) {
          this.$toast({
            content: this.$t('维度未关联指标，不可保存'),
            type: 'warning'
          })
        } else {
          let params = {
            dimensionDTOS: reviewTemplateData,
            reviewTemplateId: this.reviewTemplateId
          }
          this.abslue = true
          this.slue = true
          params.dimensionDTOS.forEach((e) => {
            if (Number(e.qualifiedScore) > this.soucor) {
              this.abslue = false
            }
            e.indexDTOS.forEach((e1) => {
              // if (Number(e1.qualifiedScore) > this.soucor) {
              //   this.abslue = false;
              // }
              if (
                Number(e1.qualifiedScore) < e1.lowestScore ||
                Number(e1.qualifiedScore) > e1.highestScore
              ) {
                this.slue = false
              }
            })
          })
          if (this.abslue == false) {
            this.$toast({
              content: this.$t('合格线值不能大于评分模板满分值'),
              type: 'warning'
            })
          } else if (this.slue == false) {
            this.$toast({
              content: this.$t('维度下指标值必须在分值范围内'),
              type: 'warning'
            })
          } else {
            this.$API.performanceScoreSetting.saveTemplate(params).then(() => {
              localStorage.setItem('tabIndex', 1)
              this.$router.push({
                path: '/supplier/pur/template-config'
              })
            })
          }
        }
      }
    },
    cancel() {
      if (this.editStatus) {
        //本页面，执行过数据修改操作。
        this.$dialog({
          data: {
            title: this.$t('确认'),
            message: this.$t('有未保存的操作，确认离开?')
          },
          success: () => {
            // 将 tabIndex 放到 localStorage 待对账列表 读
            localStorage.setItem('tabIndex', 1)
            this.$router.push({
              path: '/supplier/pur/template-config'
            })
          }
        })
      } else {
        // 将 tabIndex 放到 localStorage 待对账列表 读
        localStorage.setItem('tabIndex', 1)
        this.$router.push({
          path: '/supplier/pur/template-config'
        })
      }
    }
  },
  watch: {
    reviewTemplateData: {
      handler() {},
      deep: true
    }
  }
}
</script>
<style lang="scss" scoped>
.tempalte-detail {
  position: absolute;
  background: transparent;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  height: 100vh;
  width: 100vw;
  padding: 0;

  .page-bottom {
    flex: 1;
  }

  .page-content {
    background: #fff;
    flex: 1;
    padding: 20px;
    flex-direction: column;
  }

  .detail-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 30px 30px 20px 30px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid #e8e8e8ff;
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .right_but {
      display: inline-block;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #00469cff;
      margin: 0 10px;
    }

    .left {
      color: #292929;

      .title {
        font-size: 20px;
        font-weight: 600;
      }

      .form {
        padding: 10px 0;
        font-size: 12px;
        font-weight: normal;
      }

      .form_div {
        display: inline-block;
        padding-right: 20px;
      }

      .data {
        padding: 10px 0;
        font-size: 14px;
        font-weight: 600;

        span {
          margin-left: 3px;
          color: #00469c;
        }
      }
    }
  }

  .detail-content {
    background: #e8e8e8;
    height: calc(100vh - 350px);

    ::v-deep .vxe-toolbar {
      padding-bottom: 0;
    }
  }
}

::v-deep .e-treegrid .e-rowcell:not(.e-gridclip) .e-treecolumn-container {
  text-overflow: clip;
}

::v-deep .vxe-button + .vxe-button {
  margin-left: 0 !important;
}

.row-demension-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
