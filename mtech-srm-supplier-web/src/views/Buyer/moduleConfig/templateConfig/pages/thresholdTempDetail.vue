<template>
  <div class="full-height" v-if="this.pageConfig[0].grid.dataSource != null">
    <div class="detail-top">
      <div class="left">
        <div class="title">{{ formObject.templateName }}</div>
        <div class="form">
          <div class="form_div">{{ $t('模板编码：') }}{{ formObject.templateCode }}</div>
          <div class="form_div">
            {{ $t('创建人：') }}{{ formObject.createUserName }}
            {{ formObject.createDate }}
          </div>
          <div class="form_div">{{ $t('模板类型：') }}{{ formObject.templateTypeName }}</div>
        </div>
        <div class="data">
          <div class="form_div">{{ $t('适用组织：') }}{{ formObject.orgNames }}</div>
          <div class="form_div">{{ $t('适用品类：') }}{{ formObject.categoryNames }}</div>
          <div class="form_div">
            {{ $t('版本：') }}<span>{{ formObject.version }}</span>
          </div>
          <div class="form_div">
            {{ $t('创建时间：') }}<span>{{ formObject.createTime }}</span>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="right_but" @click="cancel">{{ $t('返回') }}</div>
        <div class="right_but" v-if="formObject.status == 2" @click="confirm">
          {{ $t('保存') }}
        </div>
      </div>
    </div>
    <div class="detail-content flex1">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
      ></mt-template-page>
    </div>
  </div>
</template>
<script>
import { isEqual, cloneDeep } from 'lodash'
import { thresholdTempDetailColumns, templateData } from './config/index'
import { orgList, categoryList, thresholdTypeList } from '../config/index'
export default {
  data() {
    return {
      thresholdTempId: '', // 门槛模板ID
      originData: [], // 原始数据
      ids: null, // 已添加的门槛id集合
      formObject: {},
      pageConfig: [
        {
          gridId: '409cbb06-7bb1-4e9d-92c3-ed4ae77b1798',
          toolbar: {
            tools: [[]]
          },
          grid: {
            columnData: [],
            dataSource: null,
            allowPaging: false
          }
        }
      ]
    }
  },
  mounted() {
    this.thresholdTempId = this.$route.query.id
    this.queryThresholdTempDetail(this.thresholdTempId)
  },
  methods: {
    // 获取门槛模板详情数据
    async queryThresholdTempDetail(id) {
      this.$loading()
      await this.getCategoryList()
      await this.getStatedLimitTree()
      await this.getThresholdTypeList()
      await this.getFormTypeList()
      await this.getThresholdProjectList()
      await this.$API.ModuleConfig.queryThresholdTempDetail(id).then((res) => {
        const { code } = res
        if (code == 200) {
          this.formObject = res.data
          if (this.formObject.status == 2) {
            this.pageConfig[0].toolbar.tools[0] = ['Add', 'Delete']
          }

          if (this.formObject.orgIds == '-99') {
            this.formObject.orgNames = this.$t('通用')
          } else {
            let orgIdList = this.formObject.orgIds.split(',')
            this.formObject.orgNames = orgList
              .filter((e) => orgIdList.includes(e.id))
              .map((x) => x.name)
              .toString()
          }

          if (this.formObject.categoryIds == '-99') {
            this.formObject.categoryNames = this.$t('通用')
          } else {
            let categoryIdList = this.formObject.categoryIds.split(',')
            this.formObject.categoryNames = categoryList
              .filter((e) => categoryIdList.includes(e.id))
              .map((x) => x.categoryName)
              .toString()
          }

          this.formObject.templateTypeName = thresholdTypeList.find(
            (e) => e.itemCode == this.formObject.templateType
          ).itemName
          this.pageConfig[0].grid.dataSource = res.data.items != null ? res.data.items : []
          this.$hloading()
          this.originData = cloneDeep(res.data.items != null ? res.data.items : [])
          templateData.length = 0
          if (res.data.items != null && res.data.items.length > 0) {
            res.data.items.forEach((e) => templateData.push(e))
          }
          this.ids = this.pageConfig[0].grid.dataSource.map((e) => e.id)
        }
      })
    },
    // 获取品类列表
    async getCategoryList() {
      categoryList.length = 0
      await this.$API.supplierInvitation
        .getCategoryListAll({
          condition: 'and',
          page: {
            current: 1,
            size: 10000
          },
          pageFlag: false,
          rules: [
            {
              field: 'statusId',
              type: 'int',
              operator: 'equal',
              value: 1
            },
            {
              field: 'tree_level',
              operator: 'equal',
              type: 'int',
              value: 1
            },
            {
              condition: 'and',
              rules: [
                {
                  field: 'categoryCode',
                  type: 'string',
                  operator: 'contains',
                  value: ''
                },
                {
                  condition: 'or',
                  field: 'categoryName',
                  type: 'string',
                  operator: 'contains',
                  value: ''
                }
              ]
            }
          ]
        })
        .then((res) => {
          res.data.records.forEach((e) => categoryList.push(e))
        })
    },
    // 获取组织树列表
    async getStatedLimitTree() {
      orgList.length = 0
      let query = 'ORG02'

      await this.$API.ModuleConfig.getStatedLimitTree({
        orgLevelCode: query,
        // orgType: "ORG001ADM",
        orgType: 'ORG001PRO'
      }).then((res) => {
        let flatData = this.flatTreeData(res.data).filter((e) => e.orgLeveLTypeCode === 'ORG02')
        flatData.forEach((e) => orgList.push(e))
      })
    },
    // 扁平化树形结构数据
    flatTreeData(tree, children_key = 'children') {
      if (!tree || Object.prototype.toString.call(tree) !== '[object Array]' || tree.length <= 0)
        return []
      return tree.reduce(
        (pre, cur) => pre.concat(cur, this.flatTreeData(cur[children_key], children_key)),
        []
      )
    },
    // 获取门槛模板类型列表
    async getThresholdTypeList() {
      thresholdTypeList.length = 0
      await this.$API.ModuleConfig.queryDict({
        dictCode: 'thresholdTempType'
      }).then((res) => {
        if (res.code == 200) {
          res.data.forEach((e) => thresholdTypeList.push(e))
        }
      })
    },
    // 获取门槛类型列表
    async getFormTypeList() {
      await this.$API.ModuleConfig.queryDict({
        dictCode: 'thresholdType'
      }).then((res) => {
        if (res.code == 200) {
          let _tempMap = {}
          res.data.forEach((e) => {
            _tempMap[e.itemCode] = e.itemName
          })
          thresholdTempDetailColumns[2].valueConverter.map = _tempMap
          sessionStorage.setItem('formTypeList', JSON.stringify(_tempMap))
          // this.pageConfig[0].grid.columnData = thresholdTempDetailColumns;
        }
      })
    },
    // 获取门槛项目列表
    async getThresholdProjectList() {
      await this.$API.ModuleConfig.queryDict({
        dictCode: 'thresholdProjectList'
      }).then((res) => {
        if (res.code == 200) {
          let _tempMap = { '': '' }
          res.data.forEach((e) => {
            _tempMap[e.itemCode] = e.itemName
          })
          thresholdTempDetailColumns[5].valueConverter.map = _tempMap
          this.pageConfig[0].grid.columnData = thresholdTempDetailColumns
          sessionStorage.setItem('thresholdProjectList', JSON.stringify(_tempMap))
        }
      })
    },
    beforeDestroy() {
      sessionStorage.removeItem('formTypeList')
      sessionStorage.removeItem('thresholdProjectList')
    },
    handleClickToolBar(e) {
      // 表格顶部 toolbar
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()

      if ((!sltList || sltList.length <= 0) && toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (toolbar.id === 'Add') {
        this.ids = this.pageConfig[0].grid.dataSource.map((e) => e.thresholdDefineId)
        this.$API.ModuleConfig.queryThresholdDefList({
          page: { current: 1, size: 10 },
          rules: [
            {
              label: this.$t('状态'),
              field: 'status',
              type: 'string',
              operator: 'equal',
              value: '1'
            }
          ]
        }).then((res) => {
          let _records = res.data.records.filter((e) => !this.ids.includes(e.id))
          if (_records.length > 0) {
            this.$dialog({
              modal: () => import('./components/thresholdDialog.vue'),
              data: {
                title: this.$t('新增门槛'),
                data: _records
              },
              success: (data) => {
                data.forEach((e) =>
                  templateData.push({
                    ...e,
                    bizId: this.thresholdTempId,
                    thresholdDefineId: e.id,
                    thresholdDefineName: e.thresholdName
                  })
                )
                this.pageConfig[0].grid.dataSource.length = 0

                templateData.forEach((e) => this.pageConfig[0].grid.dataSource.push(e))
              }
            })
          } else {
            this.$toast({
              content: this.$t('没有可新增的门槛定义'),
              type: 'warning'
            })
            return
          }
        })
      } else if (toolbar.id == 'Delete') {
        let ids = sltList.map((e) => e.id)
        this.delThresholdTempDetail(ids)
      }
      //  else if (toolbar.id == "_Refresh") {
      //   this.queryThresholdTempDetail(this.thresholdTempId);
      // }
    },
    handleClickCellTool(e) {
      const { tool, data } = e
      const { id } = tool || {}
      // if (id === "edit") {
      //   const _this = this;
      //   this.$dialog({
      //     modal: () => import("./components/thresholdDialog.vue"),
      //     data: {
      //       title: this.$t("编辑调查表模板"),
      //       isEdit: true,
      //       info: data,
      //     },
      //     success: () => {
      //       _this.$refs.templateRef.refreshCurrentGridData();
      //     },
      //   });
      // }
      if (id === 'delete') {
        this.delThresholdTempDetail([data.id])
      }
    },
    // 删除门槛
    delThresholdTempDetail(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('确定删除选中行?')
        },
        success: () => {
          ids.forEach((e) => {
            this.pageConfig[0].grid.dataSource.forEach((item) => {
              if (item.id == e) {
                this.pageConfig[0].grid.dataSource.splice(
                  this.pageConfig[0].grid.dataSource.indexOf(item),
                  1
                )
                templateData.splice(templateData.indexOf(item), 1)
              }
            })
          })
        }
      })
    },
    cancel() {
      if (isEqual(this.originData, templateData)) {
        // 将 tabIndex 放到 localStorage 待对账列表 读
        localStorage.setItem('tabIndex', 2)
        this.$router.push({
          path: '/supplier/pur/template-config'
        })
      } else {
        this.$dialog({
          data: {
            title: this.$t('确认'),
            message: this.$t('有未保存的操作，确认离开?')
          },
          success: () => {
            // 将 tabIndex 放到 localStorage 待对账列表 读
            localStorage.setItem('tabIndex', 2)
            this.$router.push({
              path: '/supplier/pur/template-config'
            })
          }
        })
      }
    },
    confirm() {
      this.$API.ModuleConfig.addThresholdTempItems({
        itemRequestList: templateData,
        thresholdTemplateId: this.thresholdTempId
      })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm-function')
            this.queryThresholdTempDetail(this.thresholdTempId)
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('系统异常'),
            type: 'error'
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.detail-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 30px 30px 20px 30px;
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid #e8e8e8ff;
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .right_but {
    display: inline-block;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #00469cff;
    margin: 0 10px;
  }
  .left {
    color: #292929;
    .title {
      font-size: 20px;
      font-weight: 600;
    }
    .form {
      padding: 10px 0;
      font-size: 12px;
      font-weight: normal;
    }
    .form_div {
      display: inline-block;
      padding-right: 20px;
    }
    .data {
      padding: 10px 0;
      font-size: 14px;
      font-weight: 600;
      span {
        margin-left: 3px;
        color: #00469c;
      }
    }
  }
}
.detail-content {
  background: #e8e8e8;
  /deep/.e-grid {
    .e-rowcell {
      text-align: left !important;
      .grid-edit-column {
        // display: inline-block !important;
      }
    }
  }
}
.full-height {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
