<template>
  <!-- 资质模板详情页 -->
  <div class="tempalte-detail mt-flex-direction-column">
    <div class="page-bottom mt-flex">
      <div class="page-content fbox">
        <div class="detail-top">
          <div class="left">
            <div class="title">
              {{ formObject.qualificationTemplateName }}
            </div>
            <div class="form">
              <div class="form_div">
                {{ $t('模板编码：') }}{{ formObject.qualificationTemplateCode }}
              </div>
              <div class="form_div">{{ $t('创建人：') }}{{ formObject.createUserName }}</div>
              <div class="form_div">
                {{ $t(' 类型：') }}{{ formObject.qualificationTemplateTypeName }}
              </div>
            </div>
            <div class="data">
              <div class="form_div">
                {{ $t(' 适用组织：') }}
                <span
                  v-if="
                    formObject.organizationInfoList && formObject.organizationInfoList.length > 0
                  "
                >
                  <span
                    v-if="
                      formObject.organizationInfoList && formObject.organizationInfoList.length > 0
                    "
                  >
                    <span v-for="(item, index) in formObject.organizationInfoList" :key="index"
                      >{{ item.organizationName }}
                      <span v-if="index != formObject.organizationInfoList.length - 1">、</span>
                    </span>
                  </span>
                </span>
                <span v-else>{{ $t('通用') }}</span>
              </div>
              <div class="form_div">
                {{ $t(' 适用品类：') }}
                <span v-if="formObject.categoryInfoList && formObject.categoryInfoList.length > 0">
                  <span v-for="(item, index) in formObject.categoryInfoList" :key="index"
                    >{{ item.categoryName }}
                    <span v-if="index != formObject.categoryInfoList.length - 1">、</span>
                  </span>
                </span>
                <span v-else>{{ $t('通用') }}</span>
              </div>
              <div class="form_div">
                {{ $t(' 创建时间：') }}<span>{{ formObject.createTime }}</span>
              </div>
            </div>
          </div>
          <div class="right">
            <div class="right_but" @click="cancel">{{ $t('返回') }}</div>
            <div class="right_but" v-if="formObject.status == 2" @click="saveBtn">
              {{ $t('保存') }}
            </div>
          </div>
        </div>
        <div class="detail-content flex1">
          <mt-template-page
            ref="templateRef"
            :template-config="pageConfig"
            @handleClickToolBar="handleClickToolBar"
            @handleClickCellTitle="handleClickCellTitle"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { qualificationPageConfig, qualificationPageConfig1 } from './config'
export default {
  data() {
    return {
      editStatus: false,
      pageConfig: [],
      formObject: {},
      dataParams: [] //接收保存接口参数
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    this.getFormDetail(this.$route.query.qualificationTemplateCode)
  },
  methods: {
    handleClickCellTitle(e) {
      const { data } = e
      if (e.field == 'file') {
        this.$API.fileService
          .downloadPrivateFileTypeOne(data.fileUrl)
          .then((res) => {
            let link = document.createElement('a') // 创建元素
            link.style.display = 'none'
            let blob = new Blob([res.data], {
              type: 'application/x-msdownload'
            })
            let url = window.URL.createObjectURL(blob)
            link.href = url
            link.setAttribute('download', `${e.data.file}`) // 给下载后的文件命名
            link.click() // 点击下载
            window.URL.revokeObjectURL(url)
          })
          .catch(() => {
            this.$toast({
              content: this.$t('导出失败，请重试!'),
              type: 'warning'
            })
          })
      }
      if (e.field == 'modelName') {
        this.$API.fileService
          .downloadPrivateFileTypeOne(data.modelUrl)
          .then((res) => {
            let link = document.createElement('a') // 创建元素
            link.style.display = 'none'
            let blob = new Blob([res.data], {
              type: 'application/x-msdownload'
            })
            let url = window.URL.createObjectURL(blob)
            link.href = url
            link.setAttribute('download', `${data.modelName}`) // 给下载后的文件命名
            link.click() // 点击下载
            window.URL.revokeObjectURL(url)
          })
          .catch(() => {
            this.$toast({
              content: this.$t('导出失败，请重试!'),
              type: 'warning'
            })
          })
      }
    },
    // =====【接口】=====
    //列表==接口
    getFormDetail(id) {
      this.$API.ModuleConfig.qualificationTemplateQuery({
        qualificationTemplateCode: id
      }).then((res) => {
        this.formObject = { ...res.data }
        window.sessionStorage.setItem(
          'qualiftctionModelItemList',
          JSON.stringify(this.formObject.itemList)
        )
        // this.formObject.itemList = [...this.dataParams, ...this.formObject.itemList];
        this.formObject.itemList = JSON.parse(
          window.sessionStorage.getItem('qualiftctionModelItemList')
        )
        if (this.formObject.status == 2) {
          this.pageConfig = qualificationPageConfig(this.formObject.itemList)
        } else if (this.formObject.status == 1) {
          this.pageConfig = qualificationPageConfig1(this.formObject.itemList)
        }
      })
    },
    //保存==接口
    qualificationTemplateSaveItemInterface() {
      let dataParamsParam = JSON.parse(window.sessionStorage.getItem('qualiftctionModelItemList'))
      let arr = dataParamsParam
        ? dataParamsParam.map((item) => {
            let newItem = {}
            newItem.qualificationCode = item.qualificationCode
            newItem.confirmDept = item.confirmDept
            newItem.confirmDeptName = item.confirmDeptName
            return newItem
            // return item.qualificationCode;
          })
        : []
      this.$API.ModuleConfig.qualificationTemplateSaveItem({
        qualificationList: arr,
        qualificationTemplateCode: this.formObject.qualificationTemplateCode
      }).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          this.getFormDetail(this.$route.query.qualificationTemplateCode)
        }
      })
    },
    //表格顶部按钮点击
    handleClickToolBar(e) {
      // let _selectRecords = e.treeGrid.getSelectedRecords();
      let _selectRecords = e.gridRef.getMtechGridRecords()
      if (e.toolbar.id == 'add') {
        this.handleAddDimension()
      }
      if (e.toolbar.id == 'delete') {
        if (_selectRecords.length == 0)
          return this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        let qualificationCode = _selectRecords.map((i) => {
          return i.qualificationCode
        })
        qualificationCode.forEach((i) => {
          //删除本地。
          this.formObject.itemList.forEach((e, eIdx) => {
            if (i == e.qualificationCode) {
              this.formObject.itemList.splice(eIdx, 1)
            }
          })
        })
        window.sessionStorage.setItem(
          'qualiftctionModelItemList',
          JSON.stringify(this.formObject.itemList)
        )
      }
    },
    //新增维度操作
    handleAddDimension() {
      this.formObject.itemList = JSON.parse(
        window.sessionStorage.getItem('qualiftctionModelItemList')
      )
      this.$dialog({
        modal: () => import('./components/qualificationDialog.vue'),
        data: {
          title: this.$t('添加资质模板资质项'),
          info: this.formObject.itemList
        },
        success: (data) => {
          // let arrCode = this.formObject.itemList.map( i => {return i.qualificationCode});
          // let arr = data.filter( ic => { return !arrCode.includes(ic.qualificationCode)})
          this.formObject.itemList = data
          window.sessionStorage.setItem(
            'qualiftctionModelItemList',
            JSON.stringify(this.formObject.itemList)
          )
          if (this.formObject.status == 2) {
            this.pageConfig = qualificationPageConfig(this.formObject.itemList)
          } else if (this.formObject.status == 1) {
            this.pageConfig = qualificationPageConfig1(this.formObject.itemList)
          }
        }
      })
    },
    //返回==按钮
    cancel() {
      this.$router.go(-1)
      localStorage.tabIndex = 3
      // this.tabSltIndex = e;
    },
    //保存==按钮
    saveBtn() {
      this.qualificationTemplateSaveItemInterface()
    }
  }
}
</script>
<style lang="scss" scoped>
.tempalte-detail {
  position: absolute;
  background: transparent;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  // height: 100vh;
  width: 100%;
  padding: 0;

  .page-bottom {
    flex: 1;
  }

  .page-content {
    background: #fff;
    flex: 1;
    padding: 20px;
    flex-direction: column;
    width: 100%;
  }

  .detail-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 30px 30px 20px 30px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid #e8e8e8ff;
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    .right_but {
      display: inline-block;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #00469cff;
      margin: 0 10px;
    }
    .left {
      color: #292929;
      .title {
        font-size: 20px;
        font-weight: 600;
      }
      .form {
        padding: 10px 0;
        font-size: 12px;
        font-weight: normal;
      }
      .form_div {
        display: inline-block;
        padding-right: 20px;
      }
      .data {
        padding: 10px 0;
        font-size: 14px;
        font-weight: 600;
        span {
          margin-left: 3px;
          // color: #00469c;
        }
      }
    }
  }
  .detail-content {
    background: #e8e8e8;
    /deep/.e-grid {
      .e-rowcell {
        text-align: left !important;
      }
    }
  }
  .template-page-style {
    height: calc(100% - 130px);
  }
  /deep/.common-template-page .grid-container {
    overflow: auto !important;
  }
}
</style>
