import Vue from 'vue'
import { symbolList } from '../../config/index'
import { i18n } from '@/main'
import MtSwitch from '@mtech-ui/switch'
import Select from '../components/Select.vue'
import cellChanged from '../components/cellChanged.vue'

export default {
  components: {
    MtSwitch
  }
}

// 门槛模板详情内列表临时数据(用于对比元数据是否变更)
export let templateData = []

// 评审模板详情内列表临时数据(用于对比元数据是否变更)
export let reviewTemplateData = []
const symbolMap = {
  1: '>',
  2: '<',
  3: '≥',
  4: '≤',
  5: '=',
  6: i18n.t('非空'),
  7: i18n.t('为空')
}
const symbolMapArr = [
  { text: i18n.t('>'), value: 1 },
  { text: i18n.t('<'), value: 2 },
  { text: i18n.t('≥'), value: 3 },
  { text: i18n.t('≤'), value: 4 },
  { text: i18n.t('='), value: 5 },
  { text: i18n.t('非空'), value: 6 },
  { text: i18n.t('为空'), value: 7 }
]

//评审模板Tab
const toolbar = (enabled) => {
  return {
    useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
    tools: [
      [
        {
          id: 'Add',
          icon: 'icon_solid_edit',
          title: i18n.t('新增维度'),
          visibleCondition: () => {
            return enabled !== 0
          }
        }
      ],
      []
    ]
  }
}
const columnData = (enabled) => {
  return [
    {
      width: 240,
      field: 'dimensionName',
      headerText: i18n.t('名称'),
      cssClass: '',
      cellTools: [
        {
          id: 'Add',
          icon: 'icon_card_plus',
          title: i18n.t('添加指标'),
          visibleCondition: (data) => {
            return data['weight'] && !data['reviewStandard'] && enabled !== 0
          }
        },
        {
          id: 'Edit',
          icon: 'icon_list_edit',
          title: i18n.t('编辑'),
          visibleCondition: (data) => {
            return data['weight'] && !data['reviewStandard'] && enabled !== 0
          }
        },
        {
          id: 'Delete',
          icon: 'icon_list_delete',
          title: i18n.t('删除'),
          visibleCondition: (data) => {
            return data['showDeleteIcon'] && enabled !== 1
          }
        }
      ]
    },
    {
      field: 'weight',
      headerText: i18n.t('分配权重（%）')
    },
    {
      field: 'indexFullScore',
      headerText: i18n.t('分值范围'),
      template: function () {
        return {
          template: Vue.component('fileTypeOption', {
            template: `<span v-if="'lowestScore' in data&&'highestScore' in data">{{data.lowestScore}}~{{data.highestScore}}</span>`,
            data() {
              return {
                data: {}
              }
            }
          })
        }
      }
    },

    {
      field: 'useSet',
      headerText: i18n.t('允许不适用'),
      template: function () {
        return {
          template: Vue.component('useSet', {
            template: `<div>
            <mt-switch v-if="data.level!=0" v-model="data.useSet" activeValue="1" inactiveValue="0" @change="useSetChange"></mt-switch>
            </div>

         `,
            data() {
              return {
                data: {},
                symbolList: symbolList,
                i18n: i18n
              }
            },
            mounted() {
              console.log('==this.data==', this.data)
              // this.symbol = symbolMap[this.data.qualifiedSymbol];
            },
            methods: {
              useSetChange(e) {
                if (Object.hasOwnProperty.call(this.data, 'parentItem')) {
                  reviewTemplateData
                    .find((e) => e.dimensionName === this.data.parentItem.dimensionName)
                    .indexDTOS.find((x) => x.reviewId === this.data.reviewId).useSet = e
                } else {
                  reviewTemplateData.find(
                    (e) => e.dimensionName === this.data.dimensionName
                  ).useSet = e
                }
                console.log('=reviewTemplateData=', reviewTemplateData)
              }
            }
          })
        }
      }
    },
    {
      field: 'qualifiedScore',
      headerText: i18n.t('合格线'),
      width: 220,
      template: function () {
        return {
          template: Vue.component('fileTypeOption', {
            template: `<div style="width:100%;flex-direction: row; display: inline-flex;justify-content: space-between;">
            <mt-select
                  :width="60"
                  :data-source="symbolList"
                  :placeholder='i18n.t("请输入")'
                  @change="symbolChange"
                  css-class="input-select"
                  v-model="data.qualifiedSymbol"
                ></mt-select>
                <mt-inputNumber ref="inputNumber" :min="0" :max="999" width="120" height="26"
                @change="handleInputNum" v-model="data.qualifiedScore"></mt-inputNumber>
            </div>
         `,
            data() {
              return {
                data: {},
                symbolList: symbolList,
                i18n: i18n,
                symbol: null
              }
            },
            mounted() {},
            methods: {
              symbolChange(e) {
                console.log(reviewTemplateData)
                if (Object.hasOwnProperty.call(this.data, 'parentItem')) {
                  reviewTemplateData
                    .find((item) => item.dimensionName === this.data.parentItem.dimensionName)
                    .indexDTOS.find((x) => x.reviewId === this.data.reviewId).qualifiedSymbol =
                    e.value
                } else {
                  reviewTemplateData.find(
                    (item) => item.dimensionName === this.data.dimensionName
                  ).qualifiedSymbol = e.value
                }
                console.log('=reviewTemplateData=', reviewTemplateData)
              },
              handleInputNum(e) {
                if (Object.hasOwnProperty.call(this.data, 'parentItem')) {
                  reviewTemplateData
                    .find((e) => e.dimensionName === this.data.parentItem.dimensionName)
                    .indexDTOS.find((x) => x.reviewId === this.data.reviewId).qualifiedScore =
                    Number(e)
                } else {
                  reviewTemplateData.find(
                    (e) => e.dimensionName === this.data.dimensionName
                  ).qualifiedScore = Number(e)
                }
                console.log('=reviewTemplateData=', reviewTemplateData)
              }
            }
          })
        }
      }
    },
    {
      field: 'weight',
      headerText: i18n.t('分配分值')
    },
    {
      field: 'reviewStandard',
      headerText: i18n.t('评分标准')
    }
  ]
}

export const pageConfig = (enabled) => {
  return [
    {
      gridId: '8f8fe6ba-0264-42c0-8f76-8552f2d036b9',
      useToolTemplate: false,
      toolbar: toolbar(enabled),
      treeGrid: {
        allowPaging: false,
        clipMode: 'EllipsisWithTooltip',
        columnData: columnData(enabled),
        childMapping: 'indexDTOS',
        dataSource: []
      }
    }
  ]
}

export const thresholdTempDetailColumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'thresholdDefineName',
    headerText: i18n.t('门槛名称'),
    width: '180'
    // cellTools: [
    //   {
    //     id: "edit",
    //     icon: "icon_Editor",
    //     title: i18n.t("编辑"),
    //   },
    //   {
    //     id: "delete",
    //     icon: "icon_Delete",
    //     title: i18n.t("删除"),
    //   },
    // ],
  },
  // {
  //   field: "bizType",
  //   // width: "150",
  //   headerText: i18n.t("数据源"),
  // },
  {
    field: 'formType',
    // width: "150",
    headerText: i18n.t('信息类型'),
    valueConverter: {
      type: 'map',
      map: {}
    }
  },
  {
    field: 'fieldName',
    headerText: i18n.t('监控字段')
    // width: "180",
  },
  {
    field: 'originalValue',
    // width: "150",
    headerText: i18n.t('门槛原值')
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template: `<div style="flex-direction: row; display: inline-flex;">
    //       <mt-input v-if="data.thresholdProject" ref="inputOriginalValue" width="120" height="26" @change="handleInput" v-model="originalValue"></mt-input>
    //       </div>`,
    //       data() {
    //         return { data: {}, originalValue: "" };
    //       },
    //       mounted() {
    //         this.originalValue = this.data.originalValue;
    //       },
    //       methods: {
    //         handleInput(e) {
    //           templateData[this.data.index].originalValue = e;
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'thresholdProject',
    // width: "150",
    headerText: i18n.t('门槛项目'),
    valueConverter: {
      type: 'map',
      map: {}
    }
  },
  {
    field: 'symbol',
    // width: "150",
    headerText: i18n.t('操作符'),
    valueConverter: {
      type: 'map',
      map: symbolMap
    },
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-select
                :width="140"
                :data-source="symbolList"
                :placeholder='$t("请输入")'
                @change="symbolChange"
                css-class="input-select"
                v-model="symbolValue"
              ></mt-select>
          </div>`,
          data() {
            return {
              data: {},
              symbolList: symbolList,
              symbolValue: null
            }
          },
          mounted() {
            this.symbolValue = this.data.symbol.toString()
          },
          methods: {
            symbolChange(e) {
              templateData[this.data.index].symbol = e.value
            }
          }
        })
      }
    }
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template: `<div style="flex-direction: row; display: inline-flex;">
    //       <mt-select
    //             :width="140"
    //             :data-source="symbolList"
    //             @change="symbolChange"
    //             css-class="input-select"
    //             v-model="symbolValue"
    //           ></mt-select>
    //       </div>`,
    //       data() {
    //         return { data: {}, symbolList: symbolList, symbolValue: null };
    //       },
    //       mounted() {
    //         this.symbolValue = this.data.symbol.toString();
    //       },
    //       methods: {
    //         symbolChange(e) {
    //           templateData[this.data.index].symbol = e.value;
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'defaultValue',
    // width: "150",
    headerText: i18n.t('目标值')
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template: `<div style="flex-direction: row; display: inline-flex;">
    //       <mt-input ref="inputNumber" width="120" height="26" @change="handleInput" v-model="number"></mt-input>
    //       </div>`,
    //       data() {
    //         return { data: {}, number: 0 };
    //       },
    //       mounted() {
    //         this.number = this.data.defaultValue;
    //       },
    //       methods: {
    //         handleInput(e) {
    //           templateData[this.data.index].defaultValue = e;
    //         },
    //       },
    //     }),
    //   };
    // },
  }
]

export const thresholdDefcolumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'thresholdName',
    headerText: i18n.t('门槛名称'),
    width: '180',
    allowEditing: false
  },
  {
    field: 'fieldName',
    headerText: i18n.t('监控字段'),
    allowEditing: false
    // width: "180",
  },
  {
    field: 'formType',
    headerText: i18n.t('门槛类型'),
    // width: "180",
    valueConverter: {
      type: 'map',
      map: {}
    },
    allowEditing: false
  },
  {
    field: 'originalValue',
    // width: "150",
    headerText: i18n.t('门槛原值'),
    allowEditing: false
  },
  {
    field: 'thresholdProject',
    // width: "150",
    headerText: i18n.t('门槛项目'),
    valueConverter: {
      type: 'map',
      map: {}
    },
    allowEditing: false
  },
  {
    field: 'symbol',
    headerText: i18n.t('操作符'),
    // width: "180",
    valueConverter: {
      type: 'map',
      map: symbolMap
    },
    // template: () => {
    //   return {
    //     template: Vue.component('template-span', {
    //       template: `
    //           <span>{{ getStatusLabel(data.symbol) }}</span>`,
    //       methods: {
    //         getStatusLabel(status) {
    //           let label = symbolMapArr.filter((j) => j.value === status)
    //           return label[0]['text']
    //         }
    //       }
    //     })
    //   }
    // },
    // editTemplate: () => {
    //   return {
    //     template: Vue.component('template-span', {
    //       template: `
    //           <span style = 'color:rgba(0, 0, 0, 0.42);'>{{ getStatusLabel(data.symbol) }}</span>`,
    //       methods: {
    //         getStatusLabel(status) {
    //           let label = symbolMapArr.filter((j) => j.value === status)
    //           return label[0]['text']
    //         }
    //       }
    //     })
    //   }
    // },
    editorRender(h, scoped) {
      return (
        <div>
          <mt-select
            v-model={scoped.symbol}
            fields={{ text: 'text', value: 'value' }}
            dataSource={symbolMapArr}
            allow-filtering={true}
            filter-type='Contains'
            placeholder={i18n.t('请选择操作符')}
            onChange={(e) => {
              const val = e.value || ''
              const exitTitem = symbolMapArr.find((item) => item.value === val)
              scoped.symbol = exitTitem ? exitTitem.text : i18n.t('未匹配')
            }}
          />
        </div>
      )
    }
  },
  {
    field: 'defaultValue',
    headerText: i18n.t('默认目标值')
    // width: "180",
  }
]

export const reviewItemColumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'reviewCode',
    headerText: i18n.t('编码'),
    width: '180'
  },
  {
    field: 'reviewName',
    headerText: i18n.t('名称')
    // width: "180",
  },
  {
    field: 'reviewStandard',
    headerText: i18n.t('评分标准')
    // width: "180",
  },
  {
    field: 'lowestScore',
    headerText: i18n.t('最低分')
    // width: "180",
  },
  {
    field: 'qualifiedScore',
    headerText: i18n.t('默认合格线'),
    // width: "180",
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><span>{{symbol}}{{data.qualifiedScore}}</span></div>`,
          data() {
            return {
              data: {},
              symbol: null
            }
          },
          mounted() {
            this.symbol = symbolMap[this.data.qualifiedSymbol]
          }
        })
      }
    }
  },
  {
    field: 'highestScore',
    headerText: i18n.t('最高')
    // width: "180",
  }
]

export let qualificationColumns = (deptEdit = false) => {
  return [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      field: 'qualificationCode',
      headerText: i18n.t('资质编码'),
      width: '120',
      allowEditing: false
    },
    {
      field: 'qualificationName',
      width: '120',
      headerText: i18n.t('资质名称'),
      allowEditing: false
    },
    {
      field: 'auditRemark',
      width: '150',
      headerText: i18n.t('评审重点说明'),
      allowEditing: false
    },
    {
      field: 'qualificationType',
      width: '120',
      headerText: i18n.t('资质类型'),
      allowEditing: false,
      valueConverter: {
        type: 'map', //(map为key/value对象)：此时，fields可不传。
        map: {
          1: i18n.t('三证'),
          2: i18n.t('代理资质'),
          3: i18n.t('专利资质'),
          4: i18n.t('奖项'),
          5: i18n.t('专项')
        }
      }
      ////////
      // template: () => {
      //   return {
      //     template: Vue.component("common", {
      //       template: `<span>{{data.qualificationType}}</span>`,
      //       data() {
      //         return {
      //           data: {},
      //         };
      //       },
      //       create() {
      //         switch (data.qualificationType) {
      //           case '1':
      //             i18n.t('三证')
      //             break;
      //           case '2':
      //             i18n.t('代理资质')
      //             break;
      //           case '3':
      //             i18n.t('专利资质')
      //             break;
      //           case 4:
      //             i18n.t('奖项')
      //             break;
      //           case 5:
      //             i18n.t('其他')
      //             break;
      //           default:
      //             break;
      //         }
      //       }
      //     }),
      //   };
      // },
    },
    {
      field: 'modelName',
      headerText: i18n.t('附件模版'),
      width: '120',
      cellTools: [],
      allowEditing: false
    },
    {
      // 资质维度，0-集团-供应商，1-集团-供应商-品类，2-公司-供应商，3-公司-供应商-品类
      field: 'businessName',
      headerText: i18n.t('业务类型'),
      width: '120',
      allowEditing: false
    },
    {
      field: 'confirmName',
      width: '120',
      headerText: i18n.t('确认人'),
      editTemplate: () => {
        return { template: Select }
      }
    },
    {
      field: 'confirmDeptName',
      width: '120',
      headerText: i18n.t('确认部门'),
      clipMode: 'Ellipsis',
      allowEditing: false,
      editTemplate: () => {
        return { template: cellChanged }
      },
      template: function () {
        return {
          template: Vue.component('confirmDeptName', {
            template: `<mt-tooltip :content="data.confirmDeptName" target="#box" position="BottomCenter">
              <div v-if="deptEdit" id="box" style="display: flex;align-items: end;">
                <mt-input
                  style="flex: 1;"
                  :readonly="true"
                  v-model="data.confirmDeptName"
                  :placeholder="$t('请选择部门')"
                  float-label-type="Never"
                  :maxlength="30"
                ></mt-input>
                <a href="javascript:void(0);" style="margin-left: 10px;" @click="editDepartment()">
                  <MtIcon name="icon_list_edit" />
                </a>
              </div>
              <span v-else>{{data.confirmDeptName}}</span>
            </mt-tooltip>`,
            data() {
              return {
                deptEdit: false
              }
            },
            mounted() {
              this.deptEdit = deptEdit
            },
            methods: {
              // 编辑（选择）确认部门
              editDepartment() {
                this.$dialog({
                  modal: () =>
                    import(
                      '../../../parameterConfig/components/components/qualificationItemDialogDepartment.vue'
                    ),
                  data: {
                    title: this.$t('选择部门')
                  },
                  success: (val) => {
                    this.data['confirmDeptName'] = val.department
                    this.data['confirmDept'] = val.departmentCode

                    let dataSource = JSON.parse(
                      window.sessionStorage.getItem('qualiftctionModelItemList')
                    )
                    dataSource[this.data.index]['confirmDeptName'] = val.department
                    dataSource[this.data.index]['confirmDept'] = val.departmentCode
                    window.sessionStorage.setItem(
                      'qualiftctionModelItemList',
                      JSON.stringify(dataSource)
                    )
                  }
                })
              }
            }
          })
        }
      }
    },
    {
      field: 'remindInvalid',
      width: '120',
      headerText: i18n.t('失效提前提醒'),
      allowEditing: false
    },
    {
      field: 'qualificationDimension',
      headerText: i18n.t('所属维度'),
      width: '120',
      // template: function () {
      //   return {
      //     template: Vue.component("spanState", {
      //       template: `<span>{{paramsName}}</span>`,
      //       data() {
      //         return {
      //           data: {},
      //           paramsName: "",
      //         };
      //       },
      //       mounted() {
      //         switch (this.data.qualificationDimension) {
      //           case 0:
      //             this.paramsName = "集团-供应商"
      //             break;
      //           case 1:
      //             this.paramsName = "集团-供应商-品类"
      //             break;
      //           case 2:
      //             this.paramsName = "公司-供应商"
      //             break;
      //           case 3:
      //             this.paramsName = "公司-供应商-品类"
      //             break;
      //           default:
      //             break;
      //         }
      //       },
      //     }),
      //   };
      // },
      valueConverter: {
        type: 'map', //(map为key/value对象)：此时，fields可不传。
        map: {
          0: i18n.t('集团-供应商'),
          1: i18n.t('集团-供应商-品类'),
          2: i18n.t('公司-供应商'),
          3: i18n.t('公司-供应商-品类')
        }
      },
      allowEditing: false
    }
    // {
    //   field: 'auditRemark',
    //   width: '150',
    //   headerText: i18n.t('评审重点说明'),
    //   allowEditing: false
    // }
  ]
}
export let qualificationColumns1 = [
  {
    field: 'qualificationCode',
    headerText: i18n.t('资质编码'),
    width: '120'
  },
  {
    field: 'qualificationName',
    width: '120',
    headerText: i18n.t('资质名称')
  },
  {
    field: 'auditRemark',
    width: '150',
    headerText: i18n.t('评审重点说明')
  },
  {
    field: 'qualificationType',
    width: '120',
    headerText: i18n.t('资质类型'),
    valueConverter: {
      type: 'map', //(map为key/value对象)：此时，fields可不传。
      map: {
        1: i18n.t('三证'),
        2: i18n.t('代理资质'),
        3: i18n.t('专利资质'),
        4: i18n.t('奖项'),
        5: i18n.t('专项')
      }
    }
    ////////
    // template: () => {
    //   return {
    //     template: Vue.component("common", {
    //       template: `<span>{{data.qualificationType}}</span>`,
    //       data() {
    //         return {
    //           data: {},
    //         };
    //       },
    //       create() {
    //         switch (data.qualificationType) {
    //           case '1':
    //             i18n.t('三证')
    //             break;
    //           case '2':
    //             i18n.t('代理资质')
    //             break;
    //           case '3':
    //             i18n.t('专利资质')
    //             break;
    //           case 4:
    //             i18n.t('奖项')
    //             break;
    //           case 5:
    //             i18n.t('其他')
    //             break;
    //           default:
    //             break;
    //         }
    //       }
    //     }),
    //   };
    // },
  },
  {
    field: 'modelName',
    headerText: i18n.t('附件模版'),
    width: '120',
    cellTools: []
  },
  {
    // 资质维度，0-集团-供应商，1-集团-供应商-品类，2-公司-供应商，3-公司-供应商-品类
    field: 'businessName',
    headerText: i18n.t('业务类型'),
    width: '120'
  },
  {
    field: 'confirmDeptName',
    width: '120',
    headerText: i18n.t('确认部门')
  },
  {
    field: 'remindInvalid',
    width: '120',
    headerText: i18n.t('失效提前提醒')
  },
  {
    field: 'qualificationDimension',
    headerText: i18n.t('所属维度'),
    width: '120',
    // template: function () {
    //   return {
    //     template: Vue.component("spanState", {
    //       template: `<span>{{paramsName}}</span>`,
    //       data() {
    //         return {
    //           data: {},
    //           paramsName: "",
    //         };
    //       },
    //       mounted() {
    //         switch (this.data.qualificationDimension) {
    //           case 0:
    //             this.paramsName = "集团-供应商"
    //             break;
    //           case 1:
    //             this.paramsName = "集团-供应商-品类"
    //             break;
    //           case 2:
    //             this.paramsName = "公司-供应商"
    //             break;
    //           case 3:
    //             this.paramsName = "公司-供应商-品类"
    //             break;
    //           default:
    //             break;
    //         }
    //       },
    //     }),
    //   };
    // },
    valueConverter: {
      type: 'map', //(map为key/value对象)：此时，fields可不传。
      map: {
        0: '集团-供应商',
        1: '集团-供应商-品类',
        2: '公司-供应商',
        3: '公司-供应商-品类'
      }
    }
  }
  // {
  //   field: 'auditRemark',
  //   width: '150',
  //   headerText: i18n.t('评审重点说明')
  // }
]

// let params =  qualificationColumns[0].type === "checkbox"?
//                  qualificationColumns.unshift({width: "50",  type: "checkbox",}):
//                  qualificationColumns.shift({width: "50",  type: "checkbox",})
export const qualificationPageConfig = (val) => [
  {
    // gridId:"3ede0e7d-38bf-49a1-83b7-657196334a0f",
    toolbar: {
      tools: [
        [
          { id: 'add', icon: 'icon_table_new', title: i18n.t('新增') },
          { id: 'delete', icon: 'icon_table_delete', title: i18n.t('删除') }
        ]
      ]
    },
    grid: {
      allowPaging: false,
      columnData: qualificationColumns(true),
      dataSource: val
    }
  }
]

// let params1 =  qualificationColumns[0].type === "checkbox"?
//                  qualificationColumns.shift({width: "50",  type: "checkbox",}):
//                  qualificationColumns.unshift({width: "50",  type: "checkbox",})
export const qualificationPageConfig1 = (val) => [
  {
    // gridId:"63404361-7514-443a-b15e-93e6a4e5506c",
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false,
      tool: []
    },
    grid: {
      allowPaging: false,
      columnData: qualificationColumns1,
      dataSource: val
    }
  }
]
