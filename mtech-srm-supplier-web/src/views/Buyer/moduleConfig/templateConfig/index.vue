<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleSelectTab="handleSelectTab"
      :current-tab="currentTab"
    />
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      :upload-params="uploadParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>
<script>
import {
  templateTypesList,
  categoryList,
  orgList,
  questionnaireTypeList,
  questionnaireTempColumns,
  reviewTempColumns,
  thresholdTempColumns,
  qualificationColumns,
  reviewColumns
} from './config/index'

export default {
  components: {
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    const currentTab = JSON.parse(localStorage.getItem('tabIndex'))
      ? JSON.parse(localStorage.getItem('tabIndex'))
      : 0
    localStorage.removeItem('tabIndex')
    return {
      currentTab,
      pageConfig: [
        {
          gridId: 'b59296e6-1f3c-4d62-96ed-e1dc134c374a',
          title: this.$t('调查表模板'),
          toolbar: {
            tools: [
              [
                'Add',
                'Delete',
                {
                  id: 'enable',
                  title: this.$t('启用'),
                  icon: 'icon_table_enable'
                },
                {
                  id: 'disable',
                  title: this.$t('停用'),
                  icon: 'icon_table_disable'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: questionnaireTempColumns,
            // dataSource: [],
            asyncConfig: {
              url: '/supplier/tenant/buyer/template/task/query'
            }
          }
        },
        {
          gridId: 'd697d841-ce7b-4d4e-9023-67f38cc3dff3',
          title: this.$t('评审模板'),
          toolbar: {
            tools: [
              [
                'Add',
                'Delete',
                {
                  id: 'enable',
                  title: this.$t('启用'),
                  icon: 'icon_table_enable'
                },
                {
                  id: 'disable',
                  title: this.$t('停用'),
                  icon: 'icon_table_disable'
                },
                {
                  id: 'import',
                  title: this.$t('导入'),
                  icon: 'icon_table_disable'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: reviewTempColumns,
            asyncConfig: {
              url: '/supplier/tenant/buyer/template/review/query'
            }
          }
        },
        {
          gridId: '5ecffe5c-6156-4f09-881b-81aaa1b533c0',
          title: this.$t('门槛模板'),
          toolbar: {
            tools: [
              [
                'Add',
                'Delete',
                {
                  id: 'enable',
                  title: this.$t('启用'),
                  icon: 'icon_table_enable'
                },
                {
                  id: 'disable',
                  title: this.$t('停用'),
                  icon: 'icon_table_disable'
                },
                {
                  id: 'audit',
                  title: this.$t('查看OA审批'),
                  icon: 'icon_solid_editsvg'
                },
                {
                  id: 'import',
                  title: this.$t('导入'),
                  icon: 'icon_solid_editsvg'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: thresholdTempColumns,
            asyncConfig: {
              url: '/supplier/tenant/buyer/threshold/template/query'
            }
          }
        },
        {
          gridId: '0fb96b64-3155-4e5b-8414-ab8f1a215ad5',
          title: this.$t('资质模板'),
          toolbar: {
            tools: [
              [
                { id: 'add', icon: 'icon_table_new', title: this.$t('新增') },
                {
                  id: 'delete',
                  icon: 'icon_table_delete',
                  title: this.$t('删除')
                },
                {
                  id: 'enable',
                  title: this.$t('启用'),
                  icon: 'icon_table_enable'
                },
                {
                  id: 'disable',
                  title: this.$t('停用'),
                  icon: 'icon_table_disable'
                }
                // {
                //   id: 'qualificationDefinitionImport',
                //   title: this.$t('导入'),
                //   icon: 'icon_table_disable'
                // }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: qualificationColumns,
            asyncConfig: {
              url: this.$API.ModuleConfig.qualificationTemplatePageQuery
            }
          }
        },
        {
          gridId: '27a50a65-b544-49f6-8588-61784cd2ebb8',
          title: this.$t('评审包'),
          toolbar: {
            tools: [
              [
                'Add',
                'Delete',
                {
                  id: 'enable',
                  title: this.$t('启用'),
                  icon: 'icon_table_enable'
                },
                {
                  id: 'disable',
                  title: this.$t('停用'),
                  icon: 'icon_table_disable'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: reviewColumns,
            asyncConfig: {
              url: '/supplier/tenant/buyer/review/package/query'
            }
          }
        }
      ],
      tabSltIndex: currentTab,
      isEdit: false,
      downTemplateParams: {},
      uploadParams: {},
      requestUrls: {},

      /*--- 评审模板 ---*/
      templateTypeList: [] //评审模板-模板类型列表（下拉）
    }
  },
  mounted() {
    this.getInitList()
  },
  methods: {
    //========【接口】========
    //停启用调查表模板==接口
    async editQuestionnaireTempStatus(status, ids) {
      await this.$API.QuestionnaireConfig.changeStatus({
        status: status,
        surveyTemplateId: ids
      })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('系统异常'),
            type: 'error'
          })
        })
    },
    // 停启用资质模板==接口
    editaptitudeStatus(type, ids) {
      const methodName =
        type == 'enable' ? 'qualificationTemplateEnable' : 'qualificationTemplateDisable'
      this.$API.ModuleConfig[methodName](ids)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('系统异常'),
            type: 'error'
          })
        })
    },
    //停启用评审模板==接口
    editReviewTempStatus(type, ids) {
      const methodName = type == 'enable' ? 'enableReviewTemp' : 'disableReviewTemp'
      this.$API.ModuleConfig[methodName](ids)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('系统异常'),
            type: 'error'
          })
        })
    },
    //停启用门槛模板==接口
    editThresholdTempStatus(params) {
      this.$API.ModuleConfig.editThresholdTempStatus(params).then((res) => {
        const { code } = res
        if (code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //停启用评审包==接口
    editReviewPkgStatus(type, ids) {
      const methodName = type == 'enable' ? 'enableReviewPackage' : 'disableReviewPackage'
      this.$API.ModuleConfig[methodName](ids)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('系统异常'),
            type: 'error'
          })
        })
    },

    //=====【表格顶部操作toolbar】=====
    handleClickToolBar(e) {
      console.log(e, 'handleClickToolBar111')
      const _this = this
      const { toolbar, gridRef, tabIndex } = e
      let sltList = gridRef.getMtechGridRecords()
      if (
        (!sltList || sltList.length <= 0) &&
        ['Delete', 'delete', 'enable', 'disable'].includes(toolbar.id)
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      //-------新增按钮--------
      if (toolbar.id === 'Add' || toolbar.id === 'add') {
        //调查表模板==新增
        if (tabIndex === 0) {
          this.$dialog({
            modal: () => import('./components/questionnaireDialog.vue'),
            data: {
              title: this.$t('新增调查表模板')
            },
            success: (id) => {
              if (id) {
                this.$router.push({
                  path: '/supplier/questionnaire-template-detail',
                  query: {
                    id
                  }
                })
              } else {
                _this.$refs.templateRef.refreshCurrentGridData()
              }
            }
          })
        }
        //评审模板==新增
        else if (tabIndex === 1) {
          this.$dialog({
            modal: () => import('./components/reviewTempDialog.vue'),
            data: {
              title: this.$t('新增评审模板')
            },
            success: (id) => {
              if (id) {
                this.$router.push({
                  path: '/supplier/review-template-detail',
                  query: {
                    id
                  }
                })
              } else {
                _this.$refs.templateRef.refreshCurrentGridData()
              }
            }
          })
        }
        //门槛模板==新增
        else if (tabIndex === 2) {
          this.$dialog({
            modal: () => import('./components/thresholdTempDialog.vue'),
            data: {
              title: this.$t('新增门槛模板')
            },
            success: (id) => {
              if (id) {
                this.$router.push({
                  path: '/supplier/threshold-template-detail',
                  query: {
                    id
                  }
                })
              } else {
                _this.$refs.templateRef.refreshCurrentGridData()
              }
            }
          })
        }
        //资质模板==新增
        else if (tabIndex === 3) {
          this.$dialog({
            modal: () => import('./components/qualificationTempDialog.vue'),
            data: {
              title: this.$t('新增资质模板')
            },
            success: (qualificationTemplateCode) => {
              if (qualificationTemplateCode) {
                this.$router.push({
                  path: '/supplier/qualificationTempDetail',
                  query: {
                    qualificationTemplateCode
                  }
                })
              } else {
                _this.$refs.templateRef.refreshCurrentGridData()
              }
            }
          })
        }
        //评审包==新增
        else if (tabIndex === 4) {
          this.$dialog({
            modal: () => import('./components/reviewDialog.vue'),
            data: {
              title: this.$t('新增评审包')
            },
            success: () => {
              _this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      }

      //-------编辑按钮--------
      else if (['Edit', 'edit'].includes(toolbar.id)) {
        //相关页面==编辑
        // if (tabIndex === 3) {
        //   this.$dialog({
        //     modal: () => import("./components/qualificationTempDialog.vue"),
        //     data: {
        //       title: this.$t("编辑资质模板"),
        //       // TODO传入参数,组件接收
        //     },
        //     success: () => {
        //       _this.$refs.templateRef.refreshCurrentGridData();
        //     },
        //   });
        // }e
        // if (tabIndex === 3) {
        //   this.editQualification(e);
        // }
      }

      //-------删除按钮--------
      else if (toolbar.id === 'Delete' || toolbar.id === 'delete') {
        //相关页面==删除
        if (tabIndex === 0) {
          let ids = sltList.map((e) => e.id)
          this.deleteQuestionnaireTemp(ids)
        } else if (tabIndex === 1) {
          let ids = sltList.map((e) => e.templateId)
          this.deleteReviewTemp(ids)
        } else if (tabIndex === 2) {
          let ids = sltList.map((e) => e.id)
          this.deleteThresholdTemp(ids)
        } else if (tabIndex === 3) {
          let ids = sltList.map((e) => e.qualificationTemplateCode)
          this.deleteQualification(ids)
        } else if (tabIndex === 4) {
          let ids = sltList.map((e) => e.packageId)
          this.deleteReviewPkg(ids)
        }
      }

      //-------启动/停用按钮--------
      else if (toolbar.id === 'disable' || toolbar.id === 'enable') {
        let toStatus = toolbar.id === 'enable' ? 1 : 2
        let _status = sltList.map((e) => Number(e.status))
        if (_status.indexOf(toStatus) > -1) {
          this.$toast({
            content: `${
              this.$t('已') +
              (toolbar.id === 'enable' ? this.$t('启用') : this.$t('停用')) +
              this.$t('的行无法进行该操作')
            }`,
            type: 'warning'
          })
          return
        }
        //相关页面==启动停用
        if (tabIndex === 0) {
          let ids = sltList.map((e) => e.id)
          this.editQuestionnaireTempStatus(toStatus, ids)
        } else if (tabIndex === 1) {
          let ids = sltList.map((e) => e.templateId)
          this.editReviewTempStatus(toolbar.id, ids)
        } else if (tabIndex === 2) {
          if (_status.indexOf(3) > -1) {
            this.$toast({
              content: this.$t('待审批的数据无法进行该操作'),
              type: 'warning'
            })
            return
          }
          let ids = sltList.map((e) => e.id)
          let params = { idList: ids, status: toStatus }
          this.editThresholdTempStatus(params)
        } else if (tabIndex === 3) {
          let ids = sltList.map((e) => e.qualificationTemplateCode)
          let params = { qualificationTemplateCode: ids }
          this.editaptitudeStatus(toolbar.id, params)
        } else if (tabIndex === 4) {
          let ids = sltList.map((e) => e.packageId)
          this.editReviewPkgStatus(toolbar.id, ids)
        }
      } else if (toolbar.id === 'Delete') {
        if (tabIndex === 0) {
          let ids = sltList.map((e) => e.id)
          this.deleteQuestionnaireTemp(ids)
        } else if (tabIndex === 1) {
          let ids = sltList.map((e) => e.templateId)
          this.deleteReviewTemp(ids)
        } else if (tabIndex === 2) {
          let _status = sltList.map((e) => Number(e.status))
          if (_status.includes(1) || _status.includes(3)) {
            this.$toast({
              content: this.$t('启用或待审核的数据无法删除'),
              type: 'warning'
            })
            return
          }
          let ids = sltList.map((e) => e.id)
          this.deleteThresholdTemp(ids)
        } else if (tabIndex === 4) {
          let ids = sltList.map((e) => e.packageId)
          this.deleteReviewPkg(ids)
        }
      } else if (toolbar.id === '_Refresh') {
        if (tabIndex === 0) {
          this.queryQuestionnaireTemplate()
        }
      } else if (toolbar.id === 'audit') {
        this.audit(sltList)
      }
      //-------导入按钮--------
      else if (toolbar.id === 'import') {
        switch (tabIndex) {
          case 1:
            this.handleImportReviewTemp()
            break
          case 2:
            this.handleImportThresholdTemp()
            break
          default:
            break
        }
      }
    },
    audit(sltList) {
      if (sltList.length < 1) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (sltList.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      let params = {
        applyId: sltList[0].applyId,
        businessType: 'thresholdTemplate'
      }
      this.$API.assessManage.infoGetOALink(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    //=====【点击表格触发】=======(相关参数设置了cellTools后触发，具体在config)
    handleClickCellTool(e) {
      console.log(e, 'handleClickCellTitle1')
      const { tool, data, tabIndex } = e
      const { id } = tool || {}
      const { status } = data
      if (id === 'edit' || id === 'delete') {
        if (status === 1) {
          this.$toast({
            content: this.$t('当前已处于启用状态，不可编辑'),
            type: 'warning'
          })
          return
        }
        if (tabIndex === 0) {
          id === 'edit' && this.editQuestionnaireTemp(data)
          id === 'delete' && this.deleteQuestionnaireTemp([data.id])
        }
        if (tabIndex === 1) {
          id === 'edit' && this.editReviewTemp(data)
          id === 'delete' && this.deleteReviewTemp([data.templateId])
        }
        if (tabIndex === 2) {
          id === 'edit' && this.editThresholdTemp(data)
          id === 'delete' && this.deleteThresholdTemp([data.id])
        }
        if (tabIndex === 3) {
          id === 'edit' && this.editQualification(data)
          id === 'delete' && this.deleteQualification([data.qualificationTemplateCode])
        }
        if (tabIndex === 4) {
          id === 'edit' && this.editReviewPkg(data)
          id === 'delete' && this.deleteReviewPkg([data.packageId])
        }
      } else if (['enable', 'disable'].includes(tool.id)) {
        const toStatus = data.status == 1 ? 2 : 1
        if (tabIndex === 0) {
          this.editQuestionnaireTempStatus(toStatus, [data.id])
        } else if (tabIndex === 1) {
          this.editReviewTempStatus(id, [data.templateId])
        } else if (tabIndex === 2) {
          let params = { idList: [data.id], status: toStatus }
          this.editThresholdTempStatus(params)
        } else if (tabIndex === 3) {
          let params = {
            qualificationTemplateCode: [data.qualificationTemplateCode]
          }
          this.editaptitudeStatus(tool.id, params)
        } else if (tabIndex === 4) {
          this.editReviewPkgStatus(id, [data.packageId])
        }
      }
    },
    //=====【点击表格列内容触发】=====(表格列设置了CellTitle后触发)
    handleClickCellTitle(e) {
      let newdata = e.data
      const { field, data } = e
      if (field === 'surveyTemplateCode' && data && data.id) {
        this.$router.push({
          path: '/supplier/questionnaire-template-detail',
          query: {
            id: data.id
          }
        })
      } else if (field === 'templateCode' && this.tabSltIndex === 1 && data && data.templateId) {
        this.$router.push({
          path: '/supplier/review-template-detail',
          query: {
            id: data.templateId,
            status: newdata.status
          }
        })
      } else if (field === 'templateCode' && this.tabSltIndex === 2 && data && data.id) {
        this.$router.push({
          path: '/supplier/threshold-template-detail',
          query: {
            id: data.id
          }
        })
      } else if (field === 'questionnaireCode') {
        this.$router.push({
          path: '/supplier/qualification-temp-detail',
          query: {
            id: data.id
          }
        })
      } else if (field === 'qualificationTemplateCode') {
        this.$router.push({
          path: '/supplier/qualificationTempDetail',
          query: {
            qualificationTemplateCode: data.qualificationTemplateCode
          }
        })
      }
    },
    // 获取页面初始数据
    async getInitList() {
      await this.getQuestionnaireTypeList()
      await this.getCategoryList()
      await this.getStatedLimitTree()
      await this.getTypeList()
      // this.$refs.templateRef.refreshCurrentGridData()
    },
    handleSelectTab(e) {
      console.log(e, 'handleSelectTab')
      this.tabSltIndex = e

      // 评审模板、评审包-异步请求搜索筛选下拉数据
      // if (this.tabSltIndex == 1 || this.tabSltIndex == 3) {
      //   this.getTemplateTypeList()
      // }
    },
    // 获取品类列表
    async getCategoryList() {
      categoryList.length = 0
      // await this.$API.ModuleConfig.getCategoryList({
      //   level: 1,
      // }).then((res) => {
      //   res.data.forEach((e) => categoryList.push(e));
      // });
      await this.$API.supplierInvitation
        .getCategoryListAll({
          condition: 'and',
          page: {
            current: 1,
            size: 10000
          },
          pageFlag: false,
          rules: [
            {
              field: 'statusId',
              type: 'int',
              operator: 'equal',
              value: 1
            },
            {
              field: 'tree_level',
              operator: 'equal',
              type: 'int',
              value: 1
            },
            {
              condition: 'and',
              rules: [
                {
                  field: 'categoryCode',
                  type: 'string',
                  operator: 'contains',
                  value: ''
                },
                {
                  condition: 'or',
                  field: 'categoryName',
                  type: 'string',
                  operator: 'contains',
                  value: ''
                }
              ]
            }
          ]
        })
        .then((res) => {
          res.data.records.forEach((e) => categoryList.push(e))
        })
    },
    // 获取调查表模板类型
    async getQuestionnaireTypeList() {
      questionnaireTypeList.length = 0
      await this.$API.ModuleConfig.queryDict({
        dictCode: 'surveyTempType'
      }).then((res) => {
        if (res.code == 200) {
          res.data.forEach((e) => questionnaireTypeList.push(e))
        }
      })
    },
    // 获取组织树
    async getStatedLimitTree() {
      orgList.length = 0
      let query = 'ORG02'

      await this.$API.ModuleConfig.getStatedLimitTree({
        orgLevelCode: query,
        // orgType: "ORG001ADM",
        orgType: 'ORG001PRO'
      }).then((res) => {
        let flatData = this.flatTreeData(res.data).filter((e) => e.orgLeveLTypeCode === 'ORG02')
        flatData.forEach((e) => orgList.push(e))
      })
    },
    // 扁平化树形结构数据
    flatTreeData(tree, children_key = 'children') {
      if (!tree || Object.prototype.toString.call(tree) !== '[object Array]' || tree.length <= 0)
        return []
      return tree.reduce(
        (pre, cur) => pre.concat(cur, this.flatTreeData(cur[children_key], children_key)),
        []
      )
    },
    // 获取评审模板类型列表
    async getTypeList() {
      templateTypesList.length = 0
      await this.$API.ModuleConfig.queryDict({
        dictCode: 'reviewType'
      }).then((res) => {
        if (res.code == 200) {
          res.data.forEach((e) => templateTypesList.push(e))
        }
      })
    },
    // 编辑调查表模板
    editQuestionnaireTemp(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/questionnaireDialog.vue'),
        data: {
          title: this.$t('编辑调查表模板'),
          isEdit: true,
          info: data
        },
        success: (id) => {
          if (id) {
            this.$router.push({
              path: '/supplier/questionnaire-template-detail',
              query: {
                id
              }
            })
          } else {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        }
      })
    },
    // 删除调查表模板
    deleteQuestionnaireTemp(ids) {
      const _this = this
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选调查表模板？'),
          confirm: () => _this.$API.QuestionnaireConfig.delFormTemplate(ids)
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    // 编辑评审模板
    editReviewTemp(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/reviewTempDialog.vue'),
        data: {
          title: this.$t('编辑评审模板'),
          isEdit: true,
          info: data
        },
        success: (id) => {
          if (id) {
            this.$router.push({
              path: '/supplier/review-template-detail',
              query: {
                id
              }
            })
          } else {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        }
      })
    },
    // 删除评审模板
    deleteReviewTemp(ids) {
      const _this = this
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选评审模板？'),
          confirm: () => _this.$API.ModuleConfig.deleteReviewTemp(ids)
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    // 编辑门槛模板
    editThresholdTemp(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/thresholdTempDialog.vue'),
        data: {
          title: this.$t('编辑门槛模板'),
          isEdit: true,
          info: data
        },
        success: (id) => {
          if (id) {
            this.$router.push({
              path: '/supplier/threshold-template-detail',
              query: {
                id
              }
            })
          } else {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        }
      })
    },
    // 删除门槛模板
    deleteThresholdTemp(ids) {
      const _this = this
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选门槛模板？'),
          confirm: () =>
            _this.$API.ModuleConfig.delThresholdTemp({
              idList: ids
            })
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    //====【资质模板==编辑删除】====
    // 编辑资质模板
    editQualification(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/qualificationTempDialog.vue'),
        data: {
          title: this.$t('编辑资质模板'),
          isEdit: true,
          info: data
        },
        success: (qualificationTemplateCode) => {
          if (qualificationTemplateCode) {
            this.$router.push({
              path: '/supplier/qualificationTempDetail',
              query: {
                qualificationTemplateCode
              }
            })
          } else {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        }
      })
    },
    // 删除资质模板
    deleteQualification(ids) {
      const _this = this
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选资质模板？'),
          confirm: () =>
            _this.$API.ModuleConfig.qualificationTemplateDelete({
              qualificationTemplateCode: ids
            })
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData() //更新视图
        }
      })
    },
    //====【评审包==编辑删除】====
    // 编辑评审包
    editReviewPkg(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/reviewDialog.vue'),
        data: {
          title: this.$t('编辑评审包'),
          isEdit: true,
          info: data
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 删除评审包
    deleteReviewPkg(ids) {
      const _this = this
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选评审包？'),
          confirm: () => _this.$API.ModuleConfig.deleteReviewPackage(ids)
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },

    /*-------- 评审模板 --------*/
    // 获取评审模板类型列表
    async getTemplateTypeList() {
      await this.$API.ModuleConfig.queryDict({
        dictCode: 'reviewType'
      }).then((res) => {
        if (res.code == 200) {
          let result = res.data
          let _max = Math.max.apply(
            null,
            result.map((e) => e.treeLevel)
          )
          this.templateTypeList.length = 0
          for (let i = _max; i > 0; i--) {
            result
              .filter((e) => e.treeLevel === i)
              .forEach((item) => {
                // result.find((x) => x.id == item.parentId).hasChild = true;
                item.itemName = `${result.find((x) => x.id == item.parentId).itemName}-${
                  item.itemName
                }`
                // item["cssClass"] = "title-#ed5633";//通过cssClass控制单项的样式（要加这个，不加的话会把整个结果显示出来）
                item['cssClass'] = 'title-#6386c1' //通过cssClass控制单项的样式（要加这个，不加的话会把整个结果显示出来）
                this.templateTypeList.push(item)
              })
          }
          // this.$set(this.pageConfig[1].grid, 'columnData', reviewTempColumns(this.templateTypeList))
          // this.$set(this.pageConfig[1].grid, "asyncConfig", {url: "/supplier/tenant/buyer/template/review/query"});
        }
      })
    },
    // 导入评审模板
    handleImportReviewTemp() {
      this.requestUrls = {
        templateUrlPre: 'ModuleConfig',
        templateUrl: 'downloadReviewTempTemplate',
        uploadUrl: 'importReviewTemp',
        file: 'importFile'
      }
      this.showUploadExcel(true)
    },
    // 导入门槛模板
    handleImportThresholdTemp() {
      console.log('111-----门槛模板')
    },
    // 展示/隐藏 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({ content: this.$t('导入成功'), type: 'success' })
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
