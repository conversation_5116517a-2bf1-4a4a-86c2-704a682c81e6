<template>
  <div v-if="showflag" class="history">
    <ul v-if="dataSource.length > 0">
      <li v-for="item in dataSource" :key="item.id">
        <span class="name">{{ $t('姓名：') }}{{ item.createUserName }}</span
        ><span class="data">{{ $t('日期：') }}{{ item.createDate }}</span
        ><span class="version" @click="clickHistory(item)">{{ $t('历史版本') }}</span>
      </li>
    </ul>
    <div v-else class="empty-container">
      <img src="../../../../assets/emptyData.png" />
      <div class="empty-txt">{{ $t('暂无数据') }}</div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    supplierInternalCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dataSource: [],
      showflag: false
    }
  },
  mounted() {
    this.initialCall()
  },
  methods: {
    initialCall() {
      this.$loading()
      let params = {
        page: {
          current: 1,
          size: 20
        },
        condition: 'and',
        rules: [
          {
            label: '供应商编号-SRM',
            field: 'supplierInternalCode',
            type: 'string',
            operator: 'contains',
            value: this.supplierInternalCode
          }
        ]
      }
      this.$API.supplierlifecycle
        .applyQuery(params)
        .then((res) => {
          this.showflag = true
          this.dataSource = res.data.records
          this.$hloading()
        })
        .catch(() => {
          this.$hloading()
        })
    },
    clickHistory(data) {
      this.$router.push({
        name: 'infoChangePurDetail',
        query: {
          type: 'edit',
          id: data.applyId
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.history {
  width: 100%;
  box-sizing: border-box;
  padding: 20px 40px;
  ul {
    li {
      display: flex;
      width: 650px;
      height: 30px;
      justify-content: space-between;
      position: relative;
      border-left: 1px solid;
      .name {
        display: block;
        width: 120px;
      }
      .data {
        display: block;
        width: 180px;
      }
      .version {
        display: block;
        width: 60px;
        color: #00469c;
        text-decoration: underline;
      }
      .version:hover {
        font-size: 15px;
      }
    }
    li:before {
      display: block;
      content: '';
      width: 15px;
      height: 15px;
      border: 3px solid #139dde;
      background: #fff;
      border-radius: 50%;
      margin-left: -8px;
    }
    li:nth-last-child(1) {
      border: none;
    }
    li:hover {
      cursor: pointer;
    }
  }
  .empty-container {
    height: 240px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate3d(-50%, -50%, 0);

    img {
      display: block;
      width: 300px;
      height: 200px;
    }

    .empty-txt {
      text-align: center;
      margin-top: 20px;
      font-size: 14px;
      color: #9a9a9a;
    }
  }
}
</style>
