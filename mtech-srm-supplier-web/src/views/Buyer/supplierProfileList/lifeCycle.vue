<template>
  <div class="lifeCycle-container">
    <!-- 顶部信息 start -->
    <div class="header-status fbox">
      <!-- <div class="header-logo">G</div> -->
      <div class="header-content flex1 fbox">
        <div class="titles-box">
          <div class="mian-title" v-if="!!info.supplierEnterpriseName">
            {{ info.supplierEnterpriseName || '--' }}
          </div>
          <div class="sub-title fbox">
            <div class="normal-title" v-if="!!info.supplierEnterpriseCode">
              {{ $t('供应商编码：') }}{{ info.supplierEnterpriseCode }}
            </div>
            <div class="normal-title" v-if="!!info.contactName">
              {{ $t('联系人：') }}{{ info.contactName }}
            </div>
            <div class="normal-title" v-if="!!info.contactPhone">
              {{ $t('联系方式：') }}{{ info.contactPhone }}
            </div>
            <div class="normal-title" v-if="!!info.contactMail">
              {{ $t('联系人邮箱：') }}{{ info.contactMail }}
            </div>
          </div>
        </div>
        <div class="btns-box fbox">
          <div class="invite-btn" @click="onBack">{{ $t('返回') }}</div>
        </div>
      </div>
    </div>
    <!-- 顶部信息 end -->
    <mt-tabs
      id="stage-config-tabs"
      :e-tab="false"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>

    <task-center v-if="selectIndex === 0" :id="id"></task-center>
    <access-process v-if="selectIndex === 1" :id="id"></access-process>
  </div>
</template>

<script>
import TaskCenter from './components/taskCenter.vue'
import AccessProcess from './components/accessProcess.vue'
export default {
  components: {
    TaskCenter,
    AccessProcess
  },
  filters: {
    filterNo: function (value) {
      if (!value) {
        return 'D'
      }
      return value.substr(0, 1)
    }
  },
  data() {
    return {
      selectIndex: 0,
      dataSource: [
        {
          title: this.$t('任务总览')
        },
        {
          title: this.$t('阶段总览')
        }
      ],
      id: '',
      info: {}
    }
  },
  methods: {
    // 返回
    onBack() {
      this.$router.go(-1)
    },
    handleSelectTab(index) {
      this.selectIndex = index
    },

    // 获取任务详情
    getAccessDetail(id) {
      this.$API.supplierlifecycle.getDetail({ partnerArchiveId: id }).then((res) => {
        this.info = res.data
      })
    }
  },
  created() {
    let id = this.$route.query.id
    if (!id) {
      this.$toast({
        content: this.$t('获取ID失败，请重试!'),
        type: 'warning'
      })
      return
    }
    this.id = id
    this.getAccessDetail(id)
  }
}
</script>

<style lang="scss" scoped>
.fbox {
  display: flex;
  align-items: stretch;
}

.flex1 {
  flex: 1;
}

.lifeCycle-container {
  height: 100%;
  margin-top: 20px;

  .header-status {
    padding: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .header-logo {
      border-radius: 100%;
      width: 60px;
      height: 60px;
      line-height: 60px;
      background: rgba(0, 70, 156, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      font-size: 40px;
      font-family: DINAlternate;
      font-weight: bold;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      margin-right: 20px;
    }
    .header-content {
      justify-content: space-between;
      .titles-box {
        justify-content: space-evenly;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .mian-title {
          font-size: 20px;

          font-weight: 600;
          color: rgba(41, 41, 41, 1);
        }
        .sub-title {
          font-size: 12px;

          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-top: 16px;
          .normal-title {
            font-size: 12px;

            font-weight: normal;
            color: rgba(41, 41, 41, 1);
            margin-right: 30px;
          }
        }
      }

      .btns-box {
        align-items: center;
        font-size: 14px;
        max-width: 300px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        .invite-btn {
          cursor: pointer;
        }
        .invite-btn:nth-child(1) {
          margin-right: 30px;
        }
      }
    }
  }
}
</style>
