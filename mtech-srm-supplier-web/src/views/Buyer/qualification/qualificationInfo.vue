<template>
  <div class="full-height">
    <div class="detail-header--wrap">
      <p class="detail-header-name">
        {{ detail.applyName }}
        <span class="detail-header-button--wrap">
          <mt-button type="text" class="detail-header-button" @click="refresh">{{
            $t('刷新')
          }}</mt-button>
          <mt-button
            type="text"
            v-if="detail.status == '10'"
            class="detail-header-button"
            @click="publish"
            >{{ $t('发布') }}</mt-button
          >
          <mt-button type="text" class="detail-header-button" @click="backDetail">{{
            $t('返回')
          }}</mt-button>
          <!-- <mt-button
            v-if="!isPur"
            type="text"
            class="detail-header-button"
            @click="saveDetail"
            >{{ $t("保存") }}</mt-button
          >
          <mt-button
            v-if="!isPur"
            type="text"
            class="detail-header-button"
            @click="saveAndSubmitDetail"
            >{{ $t("保存并提交") }}</mt-button
          >-->
        </span>
      </p>
      <p class="detail-header-category detail-header-items">
        <span class="form_div">{{ $t('审查单编码：') }}{{ detail.applyCode }}</span>
        <span class="form_div">{{ $t('创建人：') }}{{ detail.createUserName }}</span>
        <span class="form_div">{{ $t('创建时间：') }}{{ detail.createDate }}</span>
      </p>
      <p class="detail-header-items1">
        {{ $t('公司：') }}
        <span class="form_div">{{ detail.customerName }}</span>
        {{ $t('供应商：') }}
        <span class="form_div">{{ detail.supplierName }}</span>
        {{ $t('品类：') }}
        <span class="form_div">{{ detail.categoryName }}</span>
        {{ $t('任务类型：') }}
        <span class="form_div">{{
          detail.bizType == 0 ? this.$t('其它') : this.$t('品类认证')
        }}</span>
      </p>
    </div>
    <div class="detail-content">
      <mt-template-page
        ref="templateRef"
        :template-config="componentConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
      ></mt-template-page>
    </div>
  </div>
</template>
<script>
// import Parser from '@mtech-form-design/form-parser'
import { columnDataMain, columnDataMainTwo } from './config/qualificationInfo'
import Vue from 'vue'
export default {
  // components: {
  //   'mt-parser': Parser
  // },
  data() {
    return {
      res1: '',
      sltList1: '',
      detail: {},
      formInstanceResponseList: [],
      emptyMsg: '',
      //公司准入关系
      buyerPartnerFactoryRelationList: {},
      //表单详情
      buyerFormInstanceList: {},
      // 表单模板
      formTemplateArr: [],
      dataMainSource: [],
      // isPur: checkIsPur(),
      componentConfig: [
        {
          // gridId: 'cefd9d37-196e-4a69-bde8-63405c0ff2dc',
          useToolTemplate: false,
          isUseCustomEditor: true,
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'affirm',
                  icon: 'icon_table_new',
                  title: this.$t('确认')
                },
                {
                  id: 'reject',
                  icon: 'icon_table_new',
                  title: this.$t('驳回')
                },
                {
                  id: 'urge',
                  title: this.$t('催办')
                }
              ],
              [] //"Filter", "export", "Refresh", "Setting"
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              mode: 'Normal' // 选择默认模式，双击整行可以进行编辑
            },
            lineIndex: true,
            allowPaging: false,
            columnData: null,
            rowDataBound: (args) => {
              console.log(args)
              if (Number(args.data['showFlag']) === 0) {
                args.row.classList.add('showFlag')
              }
            },
            dataSource: [] //asgc方式不能赋值，具体为啥咱也不知道。得在赋值传递(直接搜)==》componentConfig[0].grid.dataSource = this.dataMainSource;
          }
        }
      ],
      field_P: []
    }
  },
  computed: {
    infoId() {
      return this.$route.query.id
    }
  },
  mounted() {
    this.managerQueryItemList()
  },
  methods: {
    refresh() {
      this.managerQueryItemList()
    },
    managerQueryItemList() {
      this.$API.qualification
        .getQualificationDesc({ applyCode: this.$route.query.applyCode })
        .then((res) => {
          if (res.code === 200) {
            this.detail = res.data
            this.dataMainSource = []
            this.field_P = []

            res.data.qualificationItemList.forEach((item) => {
              let obj = { ...item }
              console.log(item, 'fieldListfieldListfieldListITIUSDI')
              if (item.fieldList) {
                item.fieldList.forEach((i) => {
                  let bol = this.field_P.some((e) => {
                    return e.field == i.fieldCode
                  })

                  // 如果是附件
                  if (i.fieldCode == 'file') {
                    if (!bol) {
                      this.field_P.push({
                        width: '140',
                        field: i.fieldCode,
                        headerText: i.fieldName,
                        allowEditing: false,
                        template: () => {
                          return {
                            template: Vue.component('modelName', {
                              template: `<div>
                  <p v-for="(item,index) in fileList" :key="index" style="color:#00469c;cursor:pointer;" @click="fieldDownloadBtn(item)">{{item.fileName}}</p>
                </div>`,
                              data() {
                                return {}
                              },
                              computed: {
                                fileList() {
                                  try {
                                    if (this.data.file && this.data.file != '') {
                                      return JSON.parse(this.data.file)
                                    } else {
                                      return []
                                    }
                                  } catch (err) {
                                    return []
                                  }
                                }
                              },
                              methods: {
                                fieldDownloadBtn(item) {
                                  console.log(this.data, 'this.data.file')
                                  this.$loading()
                                  if (this.data.file) {
                                    let _id = item.fileValue ? item.fileValue : item.id
                                    this.$API.fileService
                                      .downloadPrivateFileTypeOne(_id)
                                      .then((res) => {
                                        if (res.status !== 200) return
                                        this.$hloading()
                                        let link = document.createElement('a') // 创建元素
                                        link.style.display = 'none'
                                        let blob = new Blob([res.data], {
                                          type: 'application/x-msdownload'
                                        })
                                        let url = window.URL.createObjectURL(blob)
                                        link.href = url
                                        link.setAttribute('download', item.fileName) //文件命名
                                        link.click() // 点击下载
                                        window.URL.revokeObjectURL(url)
                                      })
                                      .catch((err) => {
                                        if (err) throw err
                                        this.$toast({
                                          content: this.$t('导出失败，请重试!'),
                                          type: 'warning'
                                        })
                                      })
                                  }
                                }
                              }
                            })
                          }
                        }
                      })
                    }
                    if (i.fieldValue && i.fieldValue != '') {
                      if (i.fieldCode === 'file') {
                        obj[i.fieldCode] = i.fieldValue
                      }
                    }
                  } else {
                    if (!bol) {
                      this.field_P.push({
                        width: '140',
                        field: i.fieldCode,
                        headerText: i.fieldName,
                        allowEditing: false
                      })
                    }
                    obj[i.fieldCode] = i.fieldValue
                  }
                })
              }
              this.dataMainSource.push(obj)
            })
            this.componentConfig[0].grid.columnData = [
              ...columnDataMain,
              ...this.field_P,
              ...columnDataMainTwo
            ]
            this.componentConfig[0].grid.dataSource = this.dataMainSource
          } else {
            this.detail = {}
          }
        })
    },
    publish() {
      // console.log(this.$route.query.applyCode,"点击发布");
      let ids = [this.$route.query.applyCode]
      this.$dialog({
        data: {
          title: this.$t('发布'),
          message: this.$t('是否发布资质审查单？'),
          confirm: () =>
            this.$API.qualification.managerBatchPublish({
              applyCodeList: ids
            })
        },
        success: () => {
          this.$toast({ content: this.$t('发布成功'), type: 'success' })
          this.$router.go(-1)
          // _this.$refs.templateRef.refreshCurrentGridData();
        }
      })
    },
    // 确认/驳回==接口
    managerAuditInterface(auditResult, itemCodeList, remark) {
      let params = {
        auditResult, //审核状态，0-驳回，1-通过
        applyCode: this.detail.applyCode, //资质审查单编码
        itemCodeList, //资质项编码数组
        remark //驳回原因，auditResult=0时非空
      }
      this.$API.qualification.managerAudit(params).then((res) => {
        this.res1 = res
        this.$toast({
          content: res.message ? res.message : this.$t('操作成功'),
          type: 'success'
        })
        this.managerQueryItemList()
      })
    },
    //驳回按钮
    qualification(ids) {
      // if (condition) return this.$toast({ content: "", type: "warning" });
      this.$dialog({
        modal: () => import('./components/qualificationItemDialog.vue'),
        success: (remark) => {
          this.managerAuditInterface('0', ids, remark)
        }
      })
    },
    //返回==按钮
    backDetail() {
      this.$router.go(-1)
    },
    handleClickToolBar(e) {
      const { toolbar } = e
      let sltList = e.gridRef.getMtechGridRecords()
      this.sltList1 = sltList

      if (['affirm', 'reject', 'urge'].includes(toolbar.id) && sltList.length === 0) {
        this.$toast({
          content: this.$t('至少选择一个资质项清单'),
          type: 'warning'
        })
        return
      }

      let ids = sltList.map((e) => {
        return e.qualificationCode
      })

      if (e.toolbar.id == 'affirm') {
        let mapsltList = sltList.map((item) => item.showFlag)
        if (mapsltList.indexOf(0) > -1)
          return this.$toast({
            content: this.$t('请联系相关人员进行确认'),
            type: 'warning'
          })
        this.managerAuditInterface('1', ids, '')
      } else if (e.toolbar.id == 'reject') {
        let mapsltList = sltList.map((item) => item.showFlag)
        if (mapsltList.indexOf(0) > -1)
          return this.$toast({
            content: this.$t('请联系相关人员进行确认'),
            type: 'warning'
          })
        if (this.sltList1[0].status != '30') {
          return this.$toast({
            content: this.$t('只有待确认状态可进行驳回'),
            type: 'warning'
          })
        }
        //驳回
        this.qualification(ids)
      } else if (toolbar.id === 'urge') {
        this.hanldeUrge(sltList)
      }
    },
    handleClickCellTool(e) {
      let arr = []
      arr.push(e.data.qualificationCode)
      if (e.tool.id == 'confirm') {
        // return this.$toast({
        //   content: this.$t("只有待确认状态可进行确认"),
        //   type: "warning",
        // });
        this.managerAuditInterface('1', arr, '')
      } else if (e.tool.id == 'reject') {
        this.qualification(arr) //驳回
      }
    },
    /**
     * 点击标题跳转
     */
    handleClickCellTitle(e) {
      const { data } = e
      if (e.field == 'file') {
        this.$API.fileService
          .downloadPrivateFileTypeOne(data.fileUrl)
          .then((res) => {
            let link = document.createElement('a') // 创建元素
            link.style.display = 'none'
            let blob = new Blob([res.data], {
              type: 'application/x-msdownload'
            })
            let url = window.URL.createObjectURL(blob)
            link.href = url
            link.setAttribute('download', `${e.data.file}`) // 给下载后的文件命名
            link.click() // 点击下载
            window.URL.revokeObjectURL(url)
          })
          .catch(() => {
            this.$toast({
              content: this.$t('导出失败，请重试!'),
              type: 'warning'
            })
          })
        // let link = document.createElement("a"); // 创建元素
        // link.style.display = "none";
        // link.id = new Date().getTime();
        // link.href = e.data.fileUrl;
        // // link.setAttribute("download", `${e.data.file}`); // 给下载后的文件命名
        // document.body.appendChild(link);
        // link.click(); // 点击下载
      }
      if (e.field == 'modelName') {
        this.$API.fileService
          .downloadPrivateFileTypeOne(data.modelUrl)
          .then((res) => {
            let link = document.createElement('a') // 创建元素
            link.style.display = 'none'
            let blob = new Blob([res.data], {
              type: 'application/x-msdownload'
            })
            let url = window.URL.createObjectURL(blob)
            link.href = url
            link.setAttribute('download', `${data.modelName}`) // 给下载后的文件命名
            link.click() // 点击下载
            window.URL.revokeObjectURL(url)
          })
          .catch(() => {
            this.$toast({
              content: this.$t('导出失败，请重试!'),
              type: 'warning'
            })
          })
        // let link = document.createElement("a"); // 创建元素
        // link.style.display = "none";
        // link.id = new Date().getTime();
        // link.href = e.data.modelUrl;
        // link.setAttribute("download", `${e.data.modelName}`); // 给下载后的文件命名
        // document.body.appendChild(link);
        // link.click(); // 点击下载
      }
    },
    actionBegin(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        const validateMap = {
          // confirmDeptId: {
          //   value: data.confirmDeptId,
          //   msg: this.$t('请选择确认部门')
          // },
          confirmUserId: {
            value: data.confirmUserId,
            msg: this.$t('请选择人员')
          }
        }
        for (const key in validateMap) {
          if (Object.hasOwnProperty.call(validateMap, key)) {
            const element = validateMap[key]
            if (!element.value) {
              this.$toast({ content: element.msg, type: 'warning' })
              args.cancel = true
              break
            }
          }
        }
      }
    },
    actionComplete(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        this.handleSaveRow(data)
      }
    },
    // 保存明细行
    async handleSaveRow(row) {
      const {
        id,
        confirmDeptId,
        confirmDept,
        confirmDeptName,
        confirmUserId,
        confirmUserCode,
        confirmUserName
      } = row
      const params = {
        id,
        confirmDeptId,
        confirmDept,
        confirmDeptName,
        confirmUserId,
        confirmUserCode,
        confirmUserName
      }
      const res = await this.$API.qualification.updateConfirmInfo(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.managerQueryItemList()
      }
    },
    // 催办
    async hanldeUrge(list) {
      const itemIdList = []
      for (let item of list) {
        if (['40', '50'].includes(item.status)) {
          this.$toast({
            content: this.$t('【已确认、已驳回】状态不可催办'),
            type: 'warning'
          })
          return
        }
        itemIdList.push(item.id)
      }
      const params = {
        applyCode: this.$route.query.applyCode || '',
        itemIdList
      }
      const res = await this.$API.qualification.urgeQualifications(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
  width: 100%;
  background-color: #fff;
  padding: 20px;
  .operateButton {
    right: 18px;
    top: 18px;
  }
}
.detail-header--wrap {
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 20px 30px;
  // margin-bottom: 16px;

  .detail-header-name {
    font-size: 20px;
    line-height: 32px;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
  }
  .detail-header-category {
    font-size: 12px;
    line-height: 16px;
    color: rgba(41, 41, 41, 1);
  }
  .detail-header-items {
    font-size: 14px;
    font-weight: 600;
    margin-top: 20px;
    color: rgba(41, 41, 41, 1);
    .detail-header-item {
      margin-right: 24px;
    }
  }
  .detail-header-items1 {
    font-size: 14px;
    font-weight: 600;
    margin-top: 20px;
    color: rgba(41, 41, 41, 1);
    .detail-header-item {
      margin-right: 24px;
    }
    span {
      // color: #00469c;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 600;
    }
  }

  .detail-header-button--wrap {
    float: right;
    .detail-header-button {
      margin-right: 24px;
    }
  }
}
.form-content {
  height: calc(100% - 120px);
  overflow: auto;
}
.parse-title {
  color: #292929;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 20px;
  // border-left: 2px solid #00469c;
  padding-left: 10px;
}
.form_div {
  font-size: 12px;
  font-weight: normal;
  display: inline-block;
  padding-right: 20px;
}

.form {
  padding: 10px 0;
  font-size: 12px;
  font-weight: normal;
}
.left {
  // color: #292929;
  .title {
    font-size: 20px;
    font-weight: 600;
  }
  .form {
    padding: 10px 0;
    font-size: 12px;
    font-weight: normal;
  }
  .form_div {
    display: inline-block;
    padding-right: 20px;
  }
}

.detail-content {
  background: #e8e8e8;
  height: calc(100% - 144px);
  /deep/.e-grid {
    .e-rowcell {
      text-align: left !important;
      .grid-edit-column {
        display: inline-block !important;
      }
    }
  }
  /deep/.showFlag {
    background: #eee;
  }
}
/deep/.common-template-page .grid-container {
  // overflow: auto !important;
}
/deep/.common-template-page .grid-container .mt-data-grid .e-gridcontent {
  max-height: calc(100% - 130px) !important;
}
/deep/.common-template-page .grid-container .mt-data-grid .e-gridcontent .e-content {
  height: auto !important;
  max-height: calc(100% - 130px) !important;
}
::v-deep {
  .mt-data-grid {
    height: 100% !important;
  }
  .e-grid {
    height: 100% !important;
  }
}
</style>
