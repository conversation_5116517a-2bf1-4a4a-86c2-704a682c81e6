<template>
  <div class="full-height" v-if="isShow">
    <div class="detail-card">
      <div class="desc">
        <div class="desc-title-box">
          <span class="title">{{ formObject.projectName }}</span>
          <!-- <span class="tag status"></span>
          <span class="tag group">abc</span>-->
        </div>
        <div class="desc-detail-box">
          <div class="detail-item">
            <span>{{ $t('认证项目编码：') }}</span>
            <span>{{ formObject.projectCode }}</span>
          </div>
          <div class="detail-item" v-if="formObject.source == 1">
            <span>{{ $t('需求部门：') }}</span>
            <span>{{ formObject.buyerAuthDemandResponse.deptName }}</span>
          </div>
          <div class="detail-item">
            <span>{{ $t('品类：') }}</span>
            <span>{{ formObject.categoryName }}</span>
          </div>
          <div class="detail-item" v-if="formObject.source == 1">
            <span style="display: inline-block">{{ $t('原因：') }}</span>
            <mt-tooltip :content="formObject.buyerAuthDemandResponse.demandReason">
              <span class="reason">{{ formObject.buyerAuthDemandResponse.demandReason }}</span>
            </mt-tooltip>
          </div>
          <div class="detail-item">
            <span>{{ $t('需求创建日期：') }}</span>
            <span>{{
              $utils.formateTime(Number(formObject.createTime), 'yyyy-MM-dd hh:mm:ss')
            }}</span>
          </div>
        </div>
      </div>
      <div class="buttons-box">
        <span class="btn" @click="cancel">{{ $t('返回') }}</span>
      </div>
    </div>
    <mt-tabs
      :tab-id="tabId"
      :e-tab="false"
      :data-source="pageConfig"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <task-detail
      :project-code="formObject.projectCode"
      :project-id="formObject.id"
      :project-name="formObject.projectName"
      v-if="currentTabIndex == 0 && formObject.projectCode"
    ></task-detail>
    <supplier-view
      :project-code="formObject.projectCode"
      v-if="currentTabIndex == 1"
    ></supplier-view>
    <require-detail :info="formObject" v-if="currentTabIndex == 2"></require-detail>
  </div>
</template>
<script>
import { masterInit } from '@/views/Buyer/purReview/scoreSetting/components/template/config/index.js'
export default {
  beforeRouteEnter(to, from, next) {
    window.sessionStorage.setItem('fromName', from.name)
    next()
  },
  components: {
    taskDetail: require('./pages/taskDetail.vue').default,
    supplierView: require('./pages/supplierView.vue').default,
    requireDetail: require('./pages/requireDetail.vue').default
  },
  data() {
    return {
      isShow: false,
      formObject: {
        projectCode: null
      },
      tabId: 'categoryCertifiManageDetail',
      currentTabIndex: 0,
      pageConfig: [
        { title: this.$t('任务详情') },
        { title: this.$t('供应商视图') },
        { title: this.$t('需求详情') }
      ]
    }
  },

  computed: {
    queryId() {
      return this.$route.query.id
    },
    queryType() {
      return this.$route.query.type
    }
  },
  mounted() {
    masterInit()
    this.$API.CategoryCertification.queryProjectDetail({
      id: this.queryId
    }).then((res) => {
      this.formObject = { ...res.data }
      this.isShow = true
    })
  },
  methods: {
    handleSelectTab(e) {
      this.currentTabIndex = e
    },
    cancel() {
      let _from = sessionStorage.getItem('fromName')
      if (_from == 'pur-category-certification') {
        localStorage.setItem('tabIndex', this.queryType)
        this.$router.push({
          name: 'pur-category-certification'
        })
      } else {
        this.$router.go(-1)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.detail-card {
  width: 100%;
  height: 100px;
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 20px 70px 20px 20px;
  display: flex;
  .logo {
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    background: #00469c;
    font-size: 40px;
    font-weight: bold;
    color: #fff;
    border-radius: 50%;
    margin-right: 22px;
  }
  .desc {
    flex: 1;
    .desc-title-box {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      .title {
        font-size: 20px;
        font-weight: bold;
        color: #292929;
      }
      .tag {
        font-size: 12px;
        display: inline-block;
        padding: 4px;
        border-radius: 2px;
      }
      .status {
        color: #9a9a9a;
        background: rgba(154, 154, 154, 0.1);
        margin: 0 10px;
      }
      .group {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
    .desc-detail-box {
      display: flex;
      .detail-item {
        font-size: 12px;
        color: #9a9a9a;
        margin-right: 20px;
      }
    }
  }
  .buttons-box {
    display: flex;
    align-items: center;
    .btn {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      color: #00469c;
      margin-left: 50px;
      cursor: pointer;
    }
    .is-disabled {
      color: #ccc;
    }
  }
}
.full-height {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.operateButton {
  position: absolute;
  right: 20px;
  z-index: 1;
  top: 10px;
}
.mt-tabs {
  width: 100%;
  background-color: #fafafa;
  /deep/.mt-tabs-container {
    width: 100%;
    margin-right: 155px;
  }
}
.reason {
  max-width: 350px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mt-tooptip {
  display: inline-block;
}
</style>
