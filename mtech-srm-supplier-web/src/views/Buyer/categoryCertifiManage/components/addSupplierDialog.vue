<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog dialog-payment"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
  >
    <div class="full-size">
      <div class="item">
        <span>{{ $t('选择供应商') }}</span>
        <mt-multi-select
          ref="businessTypeRef"
          v-model="supplierInternalCode"
          :data-source="supplierList"
          :allow-filtering="true"
          :filtering="onFiltering"
          :placeholder="$t('请选择供应商编码/供应商名称/状态')"
          @change="supplierInternalCodeChange"
        ></mt-multi-select>
      </div>

      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      ></mt-template-page>
    </div>
  </mt-dialog>
</template>
<script>
import { cloneDeep } from 'lodash'
import { supplierColumn, importSupData } from '../config/index.js'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      supplierCode: null,
      supplierInternalCode: '',
      supplierList: [],
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          gridId: '53094aa1-fb80-4950-8936-267754e69133',
          grid: {
            height: '280',
            allowPaging: false,
            columnData: supplierColumn,
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    onFiltering(e) {
      e.updateData(
        this.supplierList.filter((x) => x.text.toUpperCase().includes(e.text.toUpperCase()))
      )
    },
    async initData() {
      importSupData.length = 0
      this.pageConfig[0].grid.dataSource = importSupData
      this.dialogTitle = this.modalData.title

      this.supplierList = this.modalData.supplierList
      this.$nextTick(() => {
        this.$API.CategoryCertification.projectSupplier({
          projectId: this.modalData.info.projectId,
          categoryId: this.modalData.info.categoryId
        }).then((res) => {
          this.supplierInternalCode = res.data.map((item) => item.id)
        })
      })
      await this.$refs.dialog.ejsRef.show()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      console.log(111111, importSupData[0].supplierStatus, importSupData[0])
      if (importSupData.length == 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else if (
        (importSupData[0].supplierStatus == '10' ||
          importSupData[0].supplierStatus == '40' ||
          importSupData[0].supplierStatus == '20' ||
          importSupData[0].supplierStatus == '30') &&
        importSupData[0].sceneCode == 'SC2204101428272028'
      ) {
        this.$toast({
          content: this.$t('当前供应商为老供应商，不可引入全新供应商场景'),
          type: 'warning'
        })
        return
      } else if (
        (importSupData[0].supplierStatus == '1' || importSupData[0].supplierStatus == '2') &&
        importSupData[0].sceneCode == 'SC188958879397007397'
      ) {
        this.$toast({
          content: this.$t('当前供应商为新供应商，不可引入老供应商场景'),
          type: 'warning'
        })
        return
      } else {
        for (let i of importSupData) {
          if (!Object.prototype.hasOwnProperty.call(i, 'sceneId')) {
            this.$toast({
              content: this.$t('请选择引入场景'),
              type: 'warning'
            })
            return
          } else {
            i.status = i.supplierStatus
          }
        }
        this.$API.CategoryCertification.addSupplier(importSupData)
          .then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.$emit('confirm-function')
            }
          })
          .catch((err) => {
            this.$toast({
              content: err.msg || this.$t('系统异常'),
              type: 'error'
            })
          })
      }
    },
    supplierInternalCodeChange(e) {
      this.pageConfig[0].grid.dataSource = []
      let _oldList = importSupData.map((item) => item.supplierInternalCode)
      let _newList = this.supplierList
        .filter((item) => {
          return e.value.includes(item.id)
        })
        .map((item) => {
          return item.supplierInternalCode
        })
      let addArray = _oldList.concat(_newList).filter((v) => !_oldList.includes(v))

      let _tempList = []
      if (_newList.length > 0) {
        _tempList = cloneDeep(importSupData)
        _tempList = this.supplierList
          .filter((item) => addArray.includes(item.supplierInternalCode))
          .concat(_tempList)
        _tempList = _tempList.filter((item) => _newList.includes(item.supplierInternalCode))
      }
      importSupData.length = 0
      _tempList.forEach((element) =>
        importSupData.push({ ...element, categoryRelationId: element.id })
      )
      this.pageConfig[0].grid.dataSource = importSupData
    },
    handleClickToolBar(e) {
      if (e.gridRef.getMtechGridRecords().length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-size {
  /deep/.item {
    padding-top: 10px;
    height: 80px;
  }
  /deep/.e-multiselect.e-checkbox .e-multi-select-wrapper .e-searcher {
    width: 100%;
  }
}
</style>
