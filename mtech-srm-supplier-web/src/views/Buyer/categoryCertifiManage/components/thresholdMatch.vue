// 门槛匹配
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>
<script>
import {
  thresholdMatchColumn,
  thresholdDetailColumns,
  formTypeList,
  thresholdProjectList
} from '../config/index.js'
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
    taskTemplateId: {
      type: String,
      default: ''
    },
    initData: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: [],
      pageConfig: [
        {
          gridId: '6e49a750-06cd-4c1c-82d6-3b4b8ec34da6',
          useToolTemplate: false,
          toolbar: [
            {
              id: 'submit',
              icon: 'icon_solid_submit',
              title: this.$t('提交审批'),
              visibleCondition: () => !this.isView
            },
            {
              id: 'audit',
              icon: 'icon_solid_editsvg',
              title: this.$t('查看OA审批')
            }
          ],
          grid: {
            columnData: thresholdMatchColumn,
            asyncConfig: {
              url: '/supplier/tenant/buyer/auth/threshold/pageQuery',
              params: {
                authProjectCode: this.info.projectCode
              }
            }
          }
        }
      ]
    }
  },
  computed: {
    isView() {
      return this.info.status != 10 && this.info.status != 30
    }
  },
  created() {
    this.getThresholdTypeList()
    this.getThresholdProjectList()
    this.$API.qualification
      .checkThreshold({
        authProjectCode: this.info.projectCode
      })
      .then((res) => {
        if (res.data) {
          this.$dialog({
            data: {
              title: this.$t('提交'),
              message: this.$t('是否确认提交门槛校验？')
            },
            success: () => {
              if (!this.validList()) {
                return
              }
              this.$API.qualification
                .submitQualifications({
                  authProjectCode: this.info.projectCode,
                  taskTemplateId: this.taskTemplateId
                })
                .then(() => {
                  this.initData('thresholdTempType')
                  this.$toast({ content: this.$t('操作成功'), type: 'success' })
                })
            }
          })
        }
      })
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      const _selectGridRecords = gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id == 'submit') {
        this.handleSubmitApprove(_selectGridRecords)
      } else if (e.toolbar.id == 'audit') {
        if (_selectGridRecords.length > 1) {
          this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
          return
        }
        this.handleViewOaApprove(_selectGridRecords[0])
      }
    },
    handleClickCellTitle(e) {
      if (e.data[e.field] > 0) {
        this.$API.CategoryCertification.queryThresholdMatchDetail(e.data.id).then((res) => {
          if (res.code === 200) {
            let title = ''
            let data = []
            switch (e.field) {
              case 'failReachStandardRedLineItem':
                title = this.$t('未达标红线项')
                data = res.data.itemDTOList.filter(
                  (item) => item.isRedLine == 1 && item.status === '0'
                )
                break
              case 'thresholdPass':
                title = this.$t('门槛符合项')
                data = res.data.itemDTOList.filter((item) => item.status == 1)
                break
              case 'thresholdFail':
                title = this.$t('门槛不符合项')
                data = res.data.itemDTOList.filter((item) => item.status == 0)
                break
              default:
                break
            }
            this.$dialog({
              modal: () => import('./dialog/thresholdMatchDetail.vue'),
              data: {
                title,
                columnData: thresholdDetailColumns,
                dataList: data
              }
            })
          }
        })
      } else {
        this.$toast({ content: this.$t('无可查看数据'), type: 'warning' })
      }
    },
    // 获取门槛类型列表
    getThresholdTypeList() {
      this.$API.ModuleConfig.queryDict({
        dictCode: 'thresholdType'
      }).then((res) => {
        if (res.code == 200) {
          formTypeList.length = 0
          res.data.forEach((e) => {
            formTypeList.push(e)
          })
        }
      })
    },
    // 获取门槛项目列表
    getThresholdProjectList() {
      this.$API.ModuleConfig.queryDict({
        dictCode: 'thresholdProjectList'
      }).then((res) => {
        if (res.code == 200) {
          thresholdProjectList.length = 0
          res.data.forEach((e) => {
            thresholdProjectList.push(e)
          })
        }
      })
    },
    // 校验列表是否都是已通过状态
    validList() {
      const dataList = this.$refs.templateRef.getCurrentTabRef()?.grid.getCurrentViewRecords()
      const index = dataList.findIndex((item) => item.redLineItemStatus !== 30)
      if (index > -1) {
        this.$toast({
          content: this.$t('门槛校验任务未完成，无法提交'),
          type: 'warning'
        })
        return false
      }
      return true
    },
    // 提交审批
    handleSubmitApprove(list) {
      this.$dialog({
        data: {
          title: this.$t('提交'),
          message: this.$t('是否确认提交审批？')
        },
        success: () => {
          const thresholdIdList = []
          list.map((item) => thresholdIdList.push(item.id))
          this.$loading()
          this.$API.qualification
            .submitApprove({
              thresholdIdList
            })
            .then(() => {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$hoading()
              this.$refs.templateRef.refreshCurrentGridData()
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    // 从查看OA审批
    handleViewOaApprove(data) {
      this.$API.qualification
        .viewOaApprove({
          applyId: data.id,
          businessKey: '',
          businessType: 'thresholdRedLine'
        })
        .then((res) => {
          res.code === 200 && window.open(res.data)
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
