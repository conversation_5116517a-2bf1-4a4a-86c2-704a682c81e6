// 认证需求
<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item class="form-item" :label="$t('需求标题')" label-style="top" prop="demandName">
          <mt-input
            v-model="formInfo.demandName"
            :maxlength="30"
            :placeholder="$t('请输入需求标题名称')"
            width="414"
            :disabled="disable"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('所属公司')" label-style="top" prop="orgCode">
          <!-- <mt-select
            v-model="formInfo.orgId"
            :data-source="orgList"
            :fields="{ text: 'orgName', value: 'id' }"
            :placeholder="$t('请选择所属公司')"
            width="414"
            @change="orgChange"
            :disabled="disable"
          ></mt-select> -->
          <RemoteAutocomplete
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
            v-model="formInfo.orgCode"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            @change="orgChange"
            :disabled="disable"
            :title-switch="false"
            :width="414"
            :placeholder="$t('请选择')"
            select-type="administrativeCompany"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('需求部门')" label-style="top" prop="deptCode">
          <mt-select
            v-model="formInfo.deptCode"
            :data-source="deptList"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :placeholder="$t('请选择需求部门')"
            width="414"
            @change="deptChange"
            :disabled="disable"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('需求品类')"
          label-style="top"
          prop="categoryCode"
        >
          <mt-select
            :show-clear-button="true"
            :allow-filtering="true"
            v-model="formInfo.categoryCode"
            v-if="isCateShow"
            :data-source="categoryList"
            :fields="{ text: 'categoryName', value: 'categoryCode' }"
            :placeholder="$t('请选择需求品类')"
            width="414"
            :filtering="filteringResource"
            @change="categoryChange"
            :disabled="disable"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item form-item-spec"
          :label="$t('推荐供应商')"
          label-style="top"
          prop="supplierCode"
        >
          <mt-multi-select
            v-model="formInfo.supplierCode"
            v-if="isSupShow"
            :data-source="supplierList"
            :show-clear-button="true"
            :allow-filtering="true"
            :filtering="filteringSupplier"
            :placeholder="$t('请选择')"
            width="414"
            @change="supplierChange"
            :disabled="disable"
            css-class="d72562-c40d-4933-9a24-98c3298365ac"
            :item-template="iTemplate"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('需求人')" label-style="top" prop="demandUserId">
          <mt-select
            v-model="formInfo.demandUserId"
            v-if="isEmpShow"
            :data-source="userList"
            :fields="{ text: 'text', value: 'employeeId' }"
            :placeholder="$t('请选择')"
            float-label-type="Never"
            width="414"
            @change="demandUserChange"
            :disabled="disable"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item full-width"
          :label="$t('原因')"
          label-style="top"
          prop="demandReason"
        >
          <mt-input
            v-model="formInfo.demandReason"
            :multiline="true"
            :placeholder="$t('字数不超过800字')"
            :maxlength="800"
            :rows="5"
            :disabled="disable"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item full-width"
          :label="$t('需求详细描述')"
          label-style="top"
          prop="demandDesc"
        >
          <rich-text-editor
            :disabled="disable"
            :height="300"
            ref="editor"
            v-model="formInfo.demandDesc"
          ></rich-text-editor>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import itemVue from './itemVue'
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
import RichTextEditor from '@/components/RichTextEditor/RichTextEditor.vue'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    itemVue,
    RichTextEditor,
    RemoteAutocomplete
  },
  data() {
    return {
      demandDesc: '',
      imgUrl: '',

      iTemplate: function () {
        return {
          template: itemVue
        }
      },
      isCateShow: true,
      isEmpShow: true,
      isSupShow: true,
      employeeId: null,
      // orgList: [],
      deptList: [],
      categoryList: [],
      supplierList: [],
      userList: [],
      // demandDesc: ``,
      toolbarSettings: {
        enable: true,
        enableFloating: true,
        type: 'Expand',
        items: [
          'Bold',
          'Italic',
          'Underline',
          '|',
          'Formats',
          'Alignments',
          'OrderedList',
          'UnorderedList',
          '|',
          'CreateLink',
          'Image',
          'backgroundColor',
          '|',
          'SourceCode',
          'Undo',
          'Redo'
        ],
        itemConfigs: {}
      },
      backgroundColor: {
        columns: 10,
        modeSwitcher: true,
        colorCode: {
          Custom: [
            '#ffff00',
            '#00ff00',
            '#00ffff',
            '#ff00ff',
            '#0000ff',
            '#ff0000',
            '#000080',
            '#008080',
            '#008000',
            '#800080',
            '#800000',
            '#808000',
            '#c0c0c0',
            '#000000',
            '#000000'
          ]
        }
      },
      rules: {
        demandName: [
          {
            required: true,
            message: this.$t('请输入需求标题'),
            trigger: 'blur'
          }
        ],
        orgCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        deptCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        categoryCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        // supplierCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        demandUserId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        demandDesc: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }]
      },
      formInfo: {
        demandName: '',
        demandDesc: '',
        supplierCode: [],
        buyerAuthDemandSupplierDTOList: [
          { supplierId: null, supplierCode: null, supplierName: null }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    },
    disable() {
      return this.modalData.disable
    },
    buttons() {
      if (this.disable) {
        return [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          }
        ]
      } else {
        return [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('保存') }
          }
        ]
      }
    }
  },
  async created() {
    await this.getOrgCompanys()
    this.initData()
  },
  mounted() {
    this.show()
    // this.filteringSupplier = utils.debounce(this.filteringSupplier, 500);
    this.filteringResource = utils.debounce(this.filteringResource, 300)
  },
  methods: {
    // 用户输入搜索
    filteringSupplier(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          this.supplierList.filter((item) => {
            if (
              item?.supplierName.indexOf(e.text) > -1 ||
              item?.supplierInternalCode.indexOf(e.text) > -1 ||
              item?.supplierCode.indexOf(e.text) > -1
            ) {
              return true
            } else {
              return false
            }
          })
        )
      } else {
        e.updateData(this.supplierList)
      }
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    initData() {
      if (this.isEdit) {
        this.$nextTick(() => {
          this.employeeId = this.info.demandUserId
          this.formInfo = {
            ...this.info,
            supplierCode: this.info.buyerAuthDemandSupplierDTOList.map(
              (item) => item.supplierEnterpriseCode
            )
          }
        })
      }
      this.getOrgCompanys()
      this.getCategorys('')
      this.getEmployees()
    },
    async getOrgCompanys() {
      await this.$API.masterData.getUserInfo().then((res) => {
        if (res.code == 200) {
          this.employeeId = res.data.employeeId
          // this.$API.masterData
          //   .getOrgCompanysByEmpId({ employeeId: this.employeeId })
          // this.$API.masterData
          //   .getOrgCompanysByEmpId2({
          //     organizationLevelCodes: ["ORG02", "ORG01"],
          //     orgType: "ORG001PRO",
          //     includeItself: true,
          //   })
          //   .then((res) => {
          //     this.orgList = res.data;
          //   });
        }
      })
    },
    async getCategorys(val) {
      await this.$API.supplierInvitation
        .getCategoryLists({
          dataLimit: 20,
          patternKeyword: val,
          permissionFlag: 1,
          permissionUserId: JSON.parse(sessionStorage.getItem('userInfo')).uid
        })
        .then((res) => {
          // this.isCateShow = false;
          // this.categoryList = res.data.records
          let _data = cloneDeep(res.data)
          _data.map((item) => {
            item.categoryName = item.categoryCode + '-' + item.categoryName
          })
          this.categoryList = _data
          // setTimeout(() => {
          //   this.isCateShow = true;
          // }, 10);
        })

      // this.$API.masterData
      //   .getCategoryList({
      //     level: 1,
      //   })
      //   .then((res) => {
      //     this.isCateShow = false;
      //     this.categoryList = res.data;
      //     setTimeout(() => {
      //       this.isCateShow = true;
      //     }, 10);
      //   });
    },
    getDepartments() {
      setTimeout(() => {
        if (!this.formInfo.orgCode) {
          return
        }
        this.$API.masterData
          .getDepartmentsByEmpId({
            employeeId: this.employeeId,
            companyOrganizationId: this.formInfo.orgId
          })
          .then((res) => {
            this.deptList = res.data
          })
      }, 10)
    },
    // 获取 员工列表
    getEmployees() {
      this.$API.masterData.getCurrentTenantEmployees().then((res) => {
        res.data.forEach((item) => {
          this.isEmpShow = false
          this.userList.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        if (this.userList.length > 0 && !this.isEdit) {
          this.formInfo.demandUserId = this.userList[0].employeeId
          this.formInfo.demandUserName = this.userList[0].employeeName
        }
        if (this.isEdit && this.formInfo.demandUserId != this.userList[0].employeeId) {
          this.$API.masterData
            .getCurrentTenantEmployees({
              fuzzyName: this.formInfo.demandUserName
            })
            .then((res1) => {
              res1.data.forEach((item) => {
                if (item.employeeId == this.formInfo.demandUserId) {
                  this.userList.push({
                    ...item,
                    text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
                  })
                }
              })
              setTimeout(() => {
                this.isEmpShow = true
              }, 10)
            })
        } else {
          setTimeout(() => {
            this.isEmpShow = true
          }, 10)
        }
      })
    },
    async filteringResource(e) {
      await this.getCategorys(e.text)
      e.updateData(this.categoryList)
      this.$forceUpdate()
      // if (typeof e.text === "string" && e.text) {
      //   e.updateData(
      //     this.categoryList.filter((f) => f?.categoryName.indexOf(e.text) > -1)
      //   );
      // } else {
      //   e.updateData(this.categoryList);
      // }
    },
    orgChange(e) {
      console.log('orgChangeorgChange', e)
      // 有级联
      this.getDepartments()
      this.formInfo.orgCode = e.itemData.orgCode
      this.formInfo.orgName = e.itemData.orgName
      this.formInfo.orgId = e.itemData.id
      if (e?.value) {
        this.getSupplier(e.itemData.id)
      } else {
        // 清空供应商绑定值 和 dataSource
        this.formInfo.supplierCode = ''
        this.supplierList = []
      }
      // if (this.formInfo.categoryId) {
      //   let param = {
      //     categoryId: this.formInfo.categoryId,
      //     orgId: e.value,
      //   };
      //   this.getSupplier(param);
      // }
    },
    deptChange(e) {
      console.log(e)
      this.formInfo.deptId = e.itemData.id
      this.formInfo.deptName = e.itemData.orgName
    },
    categoryChange(e) {
      console.log(e)
      this.formInfo.categoryId = e.itemData.id
      this.formInfo.categoryName = e.itemData.categoryName
      // if (this.formInfo.orgId) {
      //   let param = {
      //     categoryId: this.formInfo.categoryId,
      //     orgId: this.formInfo.orgId,
      //   };
      //   this.getSupplier(param);
      // }
    },
    supplierChange(e) {
      if (e.value) {
        let arr = []
        this.supplierList.forEach((item) => {
          if (e.value.indexOf(item.value) != -1) {
            arr.push({
              ...item,
              partnerRelationId: item.id
            })
          }
        })
        this.formInfo.buyerAuthDemandSupplierDTOList = arr
      }
    },
    // 获取供应商下拉
    getSupplier(param) {
      this.supplierList.length = 0
      this.$API.CategoryCertification.listByCompanyId({
        orgId: param
      }).then((res) => {
        this.isSupShow = false
        res.data.forEach((e) => {
          this.supplierList.push({
            ...e,
            value: e.supplierEnterpriseCode,
            text: e.supplierEnterpriseName,
            supplierName: e.supplierEnterpriseName
          })
        })
        // if (
        //   !res.data
        //     .map((x) => x.supplierEnterpriseCode)
        //     .includes(this.formInfo.supplierCode)
        // ) {
        //   this.formInfo.supplierCode = null;
        // }

        setTimeout(() => {
          this.isSupShow = true
        }, 10)
      })
    },
    changeText(value) {
      console.log(value)
    },
    demandUserChange(e) {
      if (e.itemData) {
        this.formInfo.demandUserName = e.itemData.employeeName
        this.formInfo.deptCode = ''
        this.employeeId = e.itemData.employeeId
        this.getDepartments()
      }
    },
    createdTextEditor(value) {
      console.log('created', value)
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$loading()
          const methodName = this.isEdit ? 'updateDemand' : 'addDemand'
          console.log('this.formInfo', this.formInfo)
          this.$API.CategoryCertification[methodName](this.formInfo)
            .then((res) => {
              if (res.code == 200) {
                this.$hloading()
                console.log('res', res)
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              } else {
                this.$hloading()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-rich-text-editor {
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-rte-content {
    height: 170px !important;
    padding: 0px 7px;
    em {
      font: revert;
    }
    ol,
    ul {
      list-style: revert;
    }
  }
}
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.form-item-spec {
  width: 414px;
}
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
/deep/ .full-width {
  width: 100% !important;
}
</style>
<style lang="scss">
.d72562-c40d-4933-9a24-98c3298365ac {
  li {
    display: flex;
    align-items: center;
    & > div {
      flex-shrink: 0;
    }
    & > div:first-child {
      margin-left: 20px;
    }
  }
}
</style>
