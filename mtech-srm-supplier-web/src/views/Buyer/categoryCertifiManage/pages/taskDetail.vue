<template>
  <div class="outer-box" v-if="isShow">
    <div class="tree-list">
      <mt-treeView
        ref="taskDetailTreeView"
        :fields="filedsTemplate"
        :node-template="Template"
        :show-line="false"
        :selected-nodes="checkedNodes"
        @nodeSelected="nodeSelected"
        v-if="filedsTemplate.dataSource.length > 0"
        @hook:mounted="taskTreeMounted"
      ></mt-treeView>
    </div>
    <div class="mian-content">
      <div class="task-title">
        <span>{{ taskTitle }}</span>
        <div class="buttons-box">
          <!-- <span class="btn" @click="save">{{ $t("保存") }}</span> -->
          <span class="btn" v-if="info.status == 10 || info.status == 30" @click="submit">{{
            $t('提交')
          }}</span>
        </div>
      </div>
      <div class="table-box">
        <setup-team ref="template1" :info="info" v-if="showID == 'groupTempType'"></setup-team>
        <strategy-consensus
          ref="template2"
          :info="info"
          v-if="showID == 'commonViewTempType'"
        ></strategy-consensus>
        <importSupplier
          ref="template3"
          :info="info"
          v-if="showID == 'supplierTempType'"
        ></importSupplier>
        <qualification-inspection
          ref="template4"
          :info="info"
          task-code="investigationTempType"
          :task-template-id="taskTemplateId"
          v-if="showID == 'investigationTempType' && info.status"
          :init-data="initData"
        ></qualification-inspection>
        <thresholdMatch
          ref="template5"
          :init-data="initData"
          :task-template-id="taskTemplateId"
          :info="info"
          v-if="showID == 'thresholdTempType'"
        ></thresholdMatch>
        <agreement-sign
          ref="template6"
          :info="info"
          task-code="signTempType"
          v-if="showID == 'signTempType' && info.status"
        ></agreement-sign>
        <comprehensiveApproval
          ref="template7"
          :info="info"
          v-if="showID == 'approveTempType'"
        ></comprehensiveApproval>
        <supplier-selfcheck
          ref="template8"
          :info="info"
          task-code="selfTempType"
          v-if="showID == 'selfTempType' && info.status"
        ></supplier-selfcheck>
        <scene-selfcheck
          ref="template9"
          :info="info"
          task-code="sceneSelfTempType"
          task-flag="1"
          v-if="showID == 'sceneSelfTempType' && info.status"
        ></scene-selfcheck>
        <preEffective
          ref="template10"
          :info="info"
          task-code="preEffectiveTempType"
          v-if="showID == 'preEffectiveTempType' && info.status"
        ></preEffective>
        <sampleCertification
          ref="template11"
          :info="info"
          v-if="showID == 'sampleAuthTempType'"
        ></sampleCertification>
        <smallLotExperiment
          ref="template12"
          :info="info"
          v-if="showID == 'smallBatchTempType'"
        ></smallLotExperiment>
        <effectiveApply
          ref="template13"
          :info="info"
          task-code="effectiveTempType"
          v-if="showID == 'effectiveTempType' && info.status"
        ></effectiveApply>
        <supplierPublicity
          ref="template14"
          :info="info"
          v-if="showID == 'publicityTempType'"
        ></supplierPublicity>
        <supplierPublish
          ref="template15"
          :info="info"
          v-if="showID == 'publishTempType'"
        ></supplierPublish>
        <preReviewInvestigation ref="template15" :info="info" v-if="showID == 'surveyTempType'" />
      </div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
import MtTreeView from '@mtech-ui/tree-view'
import { cloneDeep } from 'lodash'
export default {
  components: {
    MtTreeView,
    setupTeam: require('../components/setupTeam').default,
    strategyConsensus: require('../components/strategyConsensus.vue').default,
    importSupplier: require('../components/importSupplier').default,
    preReviewInvestigation: require('../components/preReviewInvestigation').default,
    sampleCertification: require('../components/sampleCertification').default,
    smallLotExperiment: require('../components/smallLotExperiment').default,
    effectiveApply: require('../../supplierEffectiveApply/index.vue').default,
    supplierPublicity: require('../components/supplierPublicity').default,
    supplierPublish: require('../components/supplierPublish').default,
    preEffective: require('../../supplierEffectiveApply/index.vue').default,
    comprehensiveApproval: require('../components/comprehensiveApproval').default,
    thresholdMatch: require('../components/thresholdMatch').default,
    qualificationInspection: require('../../qualification/index.vue').default,
    agreementSign: require('../../qualification/index.vue').default,
    supplierSelfcheck: require('../../purReview/scoreSetting/components/template/index.vue')
      .default,
    sceneSelfcheck: require('../../purReview/scoreSetting/components/template/index.vue').default
  },
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    projectId: {
      type: String,
      default: ''
    },
    projectName: {
      type: String,
      default: ''
    }
  },
  mounted() {
    let _path = ''
    if (sessionStorage.getItem('fromName') !== 'pur-category-certification') {
      _path = sessionStorage.getItem('cateTaskCode')
    }
    this.initData(_path)
  },
  data() {
    return {
      isShow: true,
      info: {
        orgId: '',
        categoryId: '',
        projectCode: this.projectCode,
        projectId: this.projectId,
        projectName: this.projectName
      },
      checkedNodes: [],
      taskTitle: '',
      taskTemplateId: '',
      showID: '',
      filedsTemplate: {
        dataSource: [],
        id: 'id',
        text: 'taskTemplateClassify',
        child: 'taskList'
      },
      Template: function () {
        return {
          template: Vue.component('common', {
            template: `<div class="treeviewdiv"  style="width: 246px">
                        <div :class="['nodetext', 'nodetext' + '-' + data.status]">
                          <span class="treeName">{{ data.stageName ||  data.taskTemplateClassifyName}}</span>
                          <span :class="['sub-tips', 'sub-tips' + '-' + data.status]">{{ statusName}}</span>
                        </div>
                        <span v-if="[10].includes(data.status)" style="color: #2783fe" @click="handleJump">跳过</span>
                      </div>`,
            data() {
              return {
                data: {},
                statusList: {
                  10: this.$t('待填写'),
                  20: this.$t('待审批'),
                  30: this.$t('已驳回'),
                  40: this.$t('已完成'),
                  50: this.$t('已关闭')
                }
              }
            },
            computed: {
              statusName() {
                return this.statusList[this.data.status]
              }
            },
            props: {
              mtData: {
                // 拿到数据
                type: Object,
                default: () => {}
              }
            },
            methods: {
              handleJump() {
                const _this = this
                this.$dialog({
                  data: {
                    title: this.$t('提示'),
                    message: this.$t('跳过后不可恢复，确认是否跳过此步骤？')
                  },
                  success: () => {
                    this.$dialog({
                      data: {
                        title: this.$t('跳过')
                      },
                      modal: () => import('../components/dialog/skipDialog.vue'),
                      success: async (skipReason) => {
                        this.$store.commit('startLoading')
                        this.$API.CategoryCertification.skipTaskApi({
                          id: this.data.id,
                          skipReason
                        })
                          .then((res) => {
                            if (res.code === 200) {
                              this.$toast({ content: this.$t('跳过成功'), type: 'success' })
                              _this.$parent.$parent.$parent.handleSearch()
                            }
                          })
                          .finally(() => this.$store.commit('endLoading'))
                      }
                    })
                  }
                })
              }
            }
          })
        }
      }
    }
  },
  methods: {
    handleSearch() {
      let _path = ''
      if (sessionStorage.getItem('fromName') !== 'pur-category-certification') {
        _path = sessionStorage.getItem('cateTaskCode')
      }
      this.initData(_path)
    },
    taskTreeMounted() {
      this.$refs.taskDetailTreeView.$refs.ejsRef.expandAll()
    },
    initData(fromName) {
      let _this = this
      _this.$API.CategoryCertification.projectStageTask({
        projectCode: _this.projectCode
      }).then((res) => {
        this.isShow = true
        _this.info.orgId = res.data.orgId
        _this.info.orgCode = res.data.orgCode
        _this.info.orgName = res.data.orgName
        _this.info.categoryId = res.data.categoryId
        _this.info.categoryCode = res.data.categoryCode
        _this.info.categoryName = res.data.categoryName
        _this.info.sourceNo = res.data.sourceNo
        _this.filedsTemplate.dataSource = cloneDeep(
          res.data.buyerAuthProjectTemplateResponse.stageList
        )
        // 默认选择第一个节点
        this.checkedNodes.length = 0
        if (fromName?.length) {
          let flatData = _this
            .flatTreeData(_this.filedsTemplate.dataSource)
            .find((e) => e.taskTemplateClassify === fromName)
          _this.checkedNodes.push(flatData.id)
          _this.taskTitle = flatData.taskTemplateClassifyName
          _this.taskTemplateId = flatData.id
          _this.info.status = flatData.status
        } else {
          _this.checkedNodes.push(_this.filedsTemplate.dataSource[0].taskList[0].id)
          _this.taskTitle = _this.filedsTemplate.dataSource[0].taskList[0].taskTemplateClassifyName
          _this.taskTemplateId = _this.filedsTemplate.dataSource[0].taskList[0].id
          _this.info.status = _this.filedsTemplate.dataSource[0].taskList[0].status
        }
        _this.showID = fromName
        this.$nextTick(() => {
          this.taskTreeMounted()
        })
      })
    },
    // 扁平化树形结构数据
    flatTreeData(tree, children_key = 'taskList') {
      if (!tree || Object.prototype.toString.call(tree) !== '[object Array]' || tree.length <= 0)
        return []
      return tree.reduce(
        (pre, cur) => pre.concat(cur, this.flatTreeData(cur[children_key], children_key)),
        []
      )
    },
    nodeSelected(event) {
      console.log('event.nodeData', event.nodeData)
      if (event.nodeData.parentID) {
        let _nodeData = this.filedsTemplate.dataSource
          .find((e) => e.id == event.nodeData.parentID)
          .taskList.find((x) => x.id == event.nodeData.id)
        this.showID = _nodeData.taskTemplateClassify
        this.taskTitle = _nodeData.taskTemplateClassifyName
        this.taskTemplateId = _nodeData.id
        this.info.status = _nodeData.status
      } else {
        this.checkedNodes.length = 0
        this.checkedNodes.push(this.showID)
        console.log('event', this.showID)
      }
      sessionStorage.setItem('cateTaskCode', this.showID)
    },
    // save() {},
    submit() {
      if (this.showID == 'thresholdTempType' && !this.$refs.template5.validList()) {
        return
      }
      this.$API.CategoryCertification.submitTask({
        authProjectCode: this.projectCode,
        taskTemplateId: this.taskTemplateId
      }).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.isShow = false
          this.initData(this.showID)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .e-ul {
  padding-left: 0px;
  .e-list-item {
    text-align: center;
    line-height: 1;
    font-size: 14px;
    color: #292929;
    padding-top: 9px;
    padding-bottom: 9px;
    &.e-active {
      background: #f5f6f9;
      font-weight: 500;
    }
  }
}

.outer-box {
  display: flex;
  background-color: #fff;
  flex: 1;
  border: 1px solid #e8e8e8;
  margin-top: 16px;
  overflow: hidden;
  .tree-list {
    width: 400px;
    height: 100%;
    overflow-y: auto;
    /deep/ .mt-tree-view {
      padding-top: 50px;
      float: left;
    }
    /deep/ .treeviewdiv {
      width: 100%;
      height: 100%;
      .nodetext {
        float: left;
        font-size: 14px;
        &-10,
        &-30 {
          color: #6386c1;
          width: 100%;
          display: flex;
          justify-content: space-between;
        }
        &-20,
        &-40,
        &-50 {
          color: #9a9a9a;
          width: 100%;
          display: flex;
          justify-content: space-between;
        }
        .sub-tips {
          display: inline-block;
          padding: 0 4px;
          height: 20px;
          line-height: 20px;
          border-radius: 2px;
          margin-left: 15px;
          &-10,
          &-30 {
            color: #eda133;
            background: rgba(237, 161, 51, 0.1);
            margin: 5px 25px 0 0;
          }
          &-20,
          &-40,
          &-50 {
            color: #9a9a9a;
            background: rgba(154, 154, 154, 0.1);
            margin: 5px 25px 0 0;
          }
        }
      }
      .nodebadge {
        float: left;
        margin-left: 5px;
      }
    }
    /deep/.e-treeview .e-list-item.e-active > .e-fullrow {
      height: 50px;
      margin-top: -10px;
      background: rgba(245, 246, 249, 0);
    }
  }
}

/deep/ .mian-content {
  .task-title {
    width: 100%;
    line-height: 62px;
    font-size: 15px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
    .buttons-box {
      display: flex;
      float: right;
      margin-right: 20px;
      .btn {
        display: inline-block;
        margin-left: 50px;
        cursor: pointer;
        color: rgba(0, 70, 156, 1);
        font-weight: 600;
      }
      .is-disabled {
        color: #ccc;
      }
    }
  }

  width: calc(100% - 400px);
  float: left;
  height: 100%;
  padding: 0 25px;
}

/deep/.table-box {
  height: calc(100% - 62px);
  overflow-y: auto;
}
// /deep/.e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
// .treeviewdiv {
//   width: 100%;
// }
// }
</style>
