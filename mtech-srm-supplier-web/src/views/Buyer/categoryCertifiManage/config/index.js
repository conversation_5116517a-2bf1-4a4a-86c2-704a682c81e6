import Vue from 'vue'
import { i18n } from '@/main.js'
import utils from '@/utils/utils'
export let sceneDefineList = []
export let importSupData = []

const symbolMap = {
  1: '>',
  2: '<',
  3: '≥',
  4: '≤',
  5: '=',
  6: i18n.t('非空'),
  7: i18n.t('为空')
}

export const formTypeList = []

export const thresholdProjectList = []

export const supplierViewColumn = (isView) => [
  {
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编号-SRM'),
    cellTools: [
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => !isView
      }
    ],
    cssClass: ''
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编号-SAP')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('注册'),
        2: i18n.t('潜在'),
        10: i18n.t('合格'),
        20: i18n.t('冻结'),
        30: i18n.t('黑名单'),
        40: i18n.t('退出')
      }
    }
  },
  {
    field: 'role',
    headerText: i18n.t('引入场景'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
              <span>{{sceneName}}</span>
          </div>`,
          data() {
            return { data: {}, sceneName: '' }
          },
          mounted() {
            this.sceneName = sceneDefineList.find((e) => e.id == this.data.sceneId).sceneName
          }
        })
      }
    }
  },
  {
    field: 'complianceScreeningResult',
    headerText: i18n.t('合规筛查结果'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('通过'),
        0: i18n.t('风险供应商'),
        '': ''
      }
    }
  },
  {
    field: 'legalApproveResult',
    headerText: i18n.t('法务审核结果'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('通过'),
        0: i18n.t('不通过'),
        2: i18n.t('审核中'),
        '': ''
      }
    }
  },
  {
    field: 'blocked',
    headerText: i18n.t('是否违规失信合作方'),
    valueConverter: {
      type: 'map',
      map: {
        false: i18n.t('否'),
        true: i18n.t('是'),
        '': ''
      }
    }
  },
  {
    field: 'exitSupplier',
    headerText: i18n.t('是否退出供方'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是'),
        '': ''
      }
    }
  },
  {
    field: 'exitSupplierCode',
    headerText: i18n.t('退出供应商编码')
  },
  {
    field: 'exitOrgCode',
    headerText: i18n.t('退出公司')
  },
  {
    field: 'exitReason',
    headerText: i18n.t('退出原因')
  }
]

export const certificationColumnData = [
  {
    width: '190',
    field: 'demandCode',
    headerText: i18n.t('需求编码'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          // 终止认证的单据不可编辑、不可删除。（估计没啥用，加上保险点）
          return !data['authProjectCode'] && data['status'] != 3
        }
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete1',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          // 终止认证的单据不可编辑、不可删除。（估计没啥用，加上保险点）
          return !data['authProjectCode'] && data['status'] != 3
        }
      }
    ]
  },
  {
    width: '190',
    field: 'demandName',
    headerText: i18n.t('需求标题')
  },
  {
    width: '190',
    field: 'authProjectCode',
    headerText: i18n.t('认证项目编号'),
    cellTools: []
  },
  {
    width: '190',
    field: 'authProjectName',
    headerText: i18n.t('认证项目名称')
  },
  {
    width: '190',
    field: 'orgName',
    headerText: i18n.t('公司')
  },
  {
    width: '150',
    field: 'deptName',
    headerText: i18n.t('需求部门')
  },
  {
    width: '210',
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    width: '150',
    field: 'demandDesc',
    headerText: i18n.t('需求详细描述'),
    template: function () {
      return {
        template: Vue.component('demandDesc', {
          template: `<a style="font-size: 14px;color: rgba(99,134,193,1);" @click="showDetail">{{ $t("查看需求描述") }}</a>`,
          data() {
            return { data: {} }
          },
          methods: {
            showDetail() {
              this.$dialog({
                modal: () => import('../components/requireDetailDescribe.vue'),
                data: {
                  title: i18n.t('查看需求详情'),
                  info: this.data.demandDesc
                }
              })
            }
          }
        })
      }
    }
  },
  {
    width: '210',
    field: 'demandReason',
    headerText: i18n.t('原因')
  },
  {
    width: '210',
    field: 'buyerAuthDemandSupplierDTOList',
    headerText: i18n.t('推荐供应商'),
    ignore: true,
    valueAccessor: function (field, data) {
      return data['buyerAuthDemandSupplierDTOList'] &&
        data.buyerAuthDemandSupplierDTOList.length > 0
        ? data.buyerAuthDemandSupplierDTOList.map((item) => item.supplierEnterpriseName).join('、')
        : ''
    }
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未认证'),
        1: i18n.t('认证中'),
        2: i18n.t('认证完成'),
        3: i18n.t('终止认证')
      }
    }
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('需求创建日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'yyyy-MM-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      type: 'date'
    }
  }
]
/**
 * 时间戳转日期显示
 * @param data: { formatString: <"YYYY-MM-dd"/"HH:MM:SS">, value: "时间戳" }
 * @returns String
 */
export const timeNumberToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(Number(value))
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}
export const handleColumnData = [
  {
    width: '220',
    field: 'projectCode',
    headerText: i18n.t('品类认证编号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          // 终止认证的单据不可编辑、不可删除。
          return data['source'] != 1 && data['status'] != 3
        }
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete1',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          // 终止认证的单据不可编辑、不可删除。
          return data['status'] != 2 && data['status'] != 3
        }
      }
    ]
  },
  {
    width: '150',
    field: 'projectName',
    headerText: i18n.t('名称')
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未认证'),
        1: i18n.t('认证中'),
        2: i18n.t('认证完成'),
        3: i18n.t('终止认证')
      }
    }
  },
  {
    width: '210',
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    width: '210',
    field: 'orgName',
    headerText: i18n.t('公司')
  },
  {
    width: '150',
    field: 'source',
    headerText: i18n.t('来源'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('认证需求'), 2: i18n.t('手动创建') }
    }
  },
  {
    width: '150',
    field: 'purchaseUserName',
    headerText: i18n.t('采购负责人')
  },
  {
    width: '150',
    field: 'finishTime',
    headerText: i18n.t('完成时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'yyyy-MM-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      type: 'date'
    }
  },
  {
    width: '150',
    field: 'demandUserName',
    headerText: i18n.t('需求人')
  },
  {
    width: '150',
    field: 'demandCreateTime',
    headerText: i18n.t('需求创建日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'yyyy-MM-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      type: 'date'
    }
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  }
]

// 成立小组
export const teamColumn = (isView) => [
  {
    field: 'userName',
    headerText: i18n.t('姓名'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        visibleCondition: () => !isView
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => !isView
      }
    ],
    cssClass: ''
  },
  {
    field: 'mobile',
    headerText: i18n.t('电话')
  },
  {
    field: 'email',
    headerText: i18n.t('邮箱')
  },
  {
    field: 'deptName',
    headerText: i18n.t('部门')
  },
  {
    field: 'roleName',
    headerText: i18n.t('角色')
  },
  {
    field: 'dutyDesc',
    headerText: i18n.t('职责描述')
  }
]
// 策略共识
export const strategyConsensusColumn = () => [
  {
    field: 'fileName',
    headerText: i18n.t('附件文件'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div>
            <p style="margin-top:5px;"><a @click="preview()">{{data.fileName}}</a> <span style="margin-left:10px;cursor: pointer;" @click="upload()">{{ $t('下载') }}</span></p>
          </div>`,
          data() {
            return { data: {}, sceneList: [] }
          },
          methods: {
            preview() {
              let params = {
                id: this.data.fileId,
                useType: 1
              }
              this.$API.SupplierPunishment.filepreview(params).then((res) => {
                window.open(res.data)
              })
            },
            upload() {
              this.$API.SupplierPunishment.fileDownload(this.data.fileId).then((res) => {
                let link = document.createElement('a')
                link.style.display = 'none'
                let blob = new Blob([res.data], { type: 'application/x-msdownload' })
                let url = window.URL.createObjectURL(blob)
                link.href = url
                link.setAttribute('download', `${this.data.fileName}`) // 给下载后的文件命名
                link.click() // 点击下载
                window.URL.revokeObjectURL(url)
              })
            }
          }
        })
      }
    }
    // cellTools: [
    //   {
    //     id: "delete",
    //     icon: "icon_solid_Delete",
    //     title: i18n.t("删除"),
    //     visibleCondition: () => !isView,
    //   },
    // ],
  },
  {
    field: 'createUserName',
    headerText: i18n.t('上传人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'yyyy-MM-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  }
]
// 引入供应商
export const supplierColumn = [
  {
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编号-SRM')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编号-SAP')
  },

  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'supplierStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('注册'),
        2: i18n.t('潜在'),
        10: i18n.t('合格'),
        20: i18n.t('冻结'),
        30: i18n.t('黑名单'),
        40: i18n.t('退出')
      }
    }
  },
  {
    field: 'role',
    headerText: i18n.t('引入场景'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-select
                :width="140"
                popup-width="280px"
                :data-source="sceneListCopy"
                :placeholder="$t('请选择')"
                @change="sceneChange"
                css-class="input-select"
                :fields="{ text: 'sceneName', value: 'id' }"
                v-model="data.sceneId"
                :allow-filtering="true"
                filter-type="Contains"
              ></mt-select>
          </div>`,
          data() {
            return { data: {}, sceneList: [] }
          },
          mounted() {
            this.sceneList = sceneDefineList
          },
          computed: {
            sceneListCopy() {
              let arr = JSON.parse(JSON.stringify(this.sceneList))
              for (let i = 0; i < arr.length; i++) {
                if (
                  arr[i].appliesStatusList.indexOf('-99') == -1 &&
                  arr[i].appliesStatusList.indexOf(String(this.data.supplierStatus)) == -1
                ) {
                  arr.splice(i, 1)
                }
              }
              return arr
            }
          },
          methods: {
            sceneChange(e) {
              console.log('importSupData', importSupData, this.data.index)
              importSupData[this.data.index].sceneId = e.value
              importSupData[this.data.index].sceneCode = e.itemData.sceneCode
            }
          }
        })
      }
    }
  }
]
// 样品认证
export const sampleCertificationColumn = (isView) => [
  {
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编号-SRM'),
    cellTools: [
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => !isView
      }
    ],
    cssClass: ''
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编号-SAP')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'buyerAuthProjectFileResponses',
    headerText: i18n.t('样品认证'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div>
            <input
              ref="file"
              type="file"
              style="display: none"
              @change="chooseFiles"
              multiple="multiple"
              accept=".xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar,"
            />
            <mt-button v-if="data.status != 20 && data.status != 40" class="mgn-left-10" @click="browseBtn()">{{
              $t("上传文件")
            }}</mt-button>
            <p style="margin-top:5px;" v-for="(item,index) in data.buyerAuthProjectFileResponses" :key="index" >
              <a @click="preview(item)">{{item.fileName}}</a>
              <span v-if="data.status != 20 && data.status != 40" style="margin-left:10px;cursor: pointer;" @click="deleteFile(item)">{{ $t('删除') }}</span>
              <span style="margin-left:10px;cursor: pointer;" @click="upload(item)">{{ $t('下载') }}</span>
            </p>
          </div>`,
          data() {
            return {
              data: {},
              sceneList: [],
              allowFileType: [
                'xls',
                'xlsx',
                'doc',
                'docx',
                'pdf',
                'ppt',
                'pptx',
                'png',
                'jpg',
                'zip',
                'rar'
              ]
            }
          },
          methods: {
            chooseFiles(data) {
              this.$loading()
              let { files } = data.target
              files = Object.values(files)
              let params = {
                type: 'array',
                limit: 50 * 1024,
                msg: this.$t('单个文件，限制50M')
              }
              if (files.length < 1) {
                this.$hloading()
                // 您未选择需要上传的文件
                return
              } else if (files.length > 5) {
                this.$hloading()
                this.$toast({
                  content: this.$t('一次性最多选择5个文件'),
                  type: 'warning'
                })
                return
              }
              let bol = files.some((item) => {
                let _tempInfo = item.name.split('.')
                return (
                  _tempInfo.length < 2 ||
                  !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])
                )
              })
              if (bol) {
                this.$toast({
                  content: this.$t('文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'),
                  type: 'warning'
                })
                this.$hloading()
                return
              }
              bol = files.some((item) => {
                return item.size > params.limit * 1024
              })
              if (bol) {
                this.$hloading()
                this.$toast({
                  content: params.msg
                })
                return
              }
              this.$refs.file.value = ''
              this.uploadFile(files)
            },
            uploadFile(files) {
              let arr = []
              files.forEach((item) => {
                let _data = new FormData()
                _data.append('UploadFiles', item)
                _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
                arr.push(this.$API.SupplierPunishment.fileUpload(_data))
              })
              Promise.all(arr).then((res) => {
                res.forEach((item) => {
                  if (item.code == 200) {
                    item.data.fileId = item.data.fileId ? item.data.fileId : item.data.id
                    item.data.fileUrl = item.data.fileUrl ? item.data.fileUrl : item.data.url
                    this.data.buyerAuthProjectFileResponses.push(item.data)
                  }
                })
                this.$hloading()
                this.$parent.$emit('updateFileList', this.data)
              })
            },
            browseBtn() {
              this.$refs.file.click()
            },
            preview(item) {
              let params = {
                id: item.fileId,
                useType: 1
              }
              this.$API.SupplierPunishment.filepreview(params).then((res) => {
                window.open(res.data)
              })
            },
            upload(item) {
              this.$API.SupplierPunishment.fileDownload(item.fileId).then((res) => {
                let link = document.createElement('a')
                link.style.display = 'none'
                let blob = new Blob([res.data], { type: 'application/x-msdownload' })
                let url = window.URL.createObjectURL(blob)
                link.href = url
                link.setAttribute('download', `${item.fileName}`) // 给下载后的文件命名
                link.click() // 点击下载
                window.URL.revokeObjectURL(url)
              })
            },
            deleteFile(item) {
              const index = this.data.buyerAuthProjectFileResponses.findIndex(
                (file) => file.id === item.id
              )
              this.data.buyerAuthProjectFileResponses.splice(index, 1)
              this.$parent.$emit('updateFileList', this.data)
            }
          }
        })
      }
    }
  },
  {
    field: 'sampleQty',
    headerText: i18n.t('送样数量')
  },
  {
    field: 'approveRound',
    headerText: i18n.t('认证轮次')
  },
  {
    field: 'approveResult',
    headerText: i18n.t('认证结果'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('通过'), 2: i18n.t('不通过') }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  }
]
// 小批量认证
export const smallLotExperimentColumn = (isView) => [
  {
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编号-SRM'),
    cellTools: [
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => !isView
      }
    ],
    cssClass: ''
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编号-SAP')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'buyerAuthProjectFileResponses',
    headerText: i18n.t('小批量认证'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div>
            <input
              ref="file"
              type="file"
              style="display: none"
              @change="chooseFiles"
              multiple="multiple"
              accept=".xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar,"
            />
            <mt-button v-if="data.status != 20 && data.status != 40" class="mgn-left-10" @click="browseBtn()">{{ $t("上传文件") }}</mt-button>
            <p style="margin-top:5px;" v-for="(item,index) in data.buyerAuthProjectFileResponses" :key="index" >
              <a @click="preview(item)" >{{item.fileName}}</a>
              <span v-if="data.status != 20 && data.status != 40" style="margin-left:10px;cursor: pointer;" @click="deleteFile(item)">{{ $t('删除') }}</span>
              <span style="margin-left:10px;cursor: pointer;" @click="upload(item)">{{ $t('下载') }}</span>
            </p>
          </div>`,
          data() {
            return {
              data: {},
              sceneList: [],
              allowFileType: [
                'xls',
                'xlsx',
                'doc',
                'docx',
                'pdf',
                'ppt',
                'pptx',
                'png',
                'jpg',
                'zip',
                'rar'
              ]
            }
          },
          methods: {
            chooseFiles(data) {
              this.$loading()
              let { files } = data.target
              files = Object.values(files)
              let params = {
                type: 'array',
                limit: 50 * 1024,
                msg: this.$t('单个文件，限制50M')
              }
              if (files.length < 1) {
                this.$hloading()
                // 您未选择需要上传的文件
                return
              } else if (files.length > 5) {
                this.$hloading()
                this.$toast({
                  content: this.$t('一次性最多选择5个文件'),
                  type: 'warning'
                })
                return
              }
              let bol = files.some((item) => {
                let _tempInfo = item.name.split('.')
                return (
                  _tempInfo.length < 2 ||
                  !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])
                )
              })
              if (bol) {
                this.$toast({
                  content: this.$t('文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'),
                  type: 'warning'
                })
                this.$hloading()
                return
              }
              bol = files.some((item) => {
                return item.size > params.limit * 1024
              })
              if (bol) {
                this.$hloading()
                this.$toast({
                  content: params.msg
                })
                return
              }
              this.$refs.file.value = ''
              this.uploadFile(files)
            },
            uploadFile(files) {
              let arr = []
              files.forEach((item) => {
                let _data = new FormData()
                _data.append('UploadFiles', item)
                _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
                arr.push(this.$API.SupplierPunishment.fileUpload(_data))
              })
              Promise.all(arr).then((res) => {
                res.forEach((item) => {
                  if (item.code == 200) {
                    item.data.fileId = item.data.fileId ? item.data.fileId : item.data.id
                    item.data.fileUrl = item.data.fileUrl ? item.data.fileUrl : item.data.url
                    this.data.buyerAuthProjectFileResponses.push(item.data)
                  }
                })
                this.$hloading()
                this.$parent.$emit('updateFileList', this.data)
              })
            },
            browseBtn() {
              this.$refs.file.click()
            },
            preview(item) {
              let params = {
                id: item.fileId,
                useType: 1
              }
              this.$API.SupplierPunishment.filepreview(params).then((res) => {
                window.open(res.data)
              })
            },
            upload(item) {
              this.$API.SupplierPunishment.fileDownload(item.fileId).then((res) => {
                let link = document.createElement('a')
                link.style.display = 'none'
                let blob = new Blob([res.data], { type: 'application/x-msdownload' })
                let url = window.URL.createObjectURL(blob)
                link.href = url
                link.setAttribute('download', `${item.fileName}`) // 给下载后的文件命名
                link.click() // 点击下载
                window.URL.revokeObjectURL(url)
              })
            },
            deleteFile(item) {
              const index = this.data.buyerAuthProjectFileResponses.findIndex(
                (file) => file.id === item.id
              )
              this.data.buyerAuthProjectFileResponses.splice(index, 1)
              this.$parent.$emit('updateFileList', this.data)
            }
          }
        })
      }
    }
  },
  {
    field: 'sampleQty',
    headerText: i18n.t('试用数量')
  },
  {
    field: 'approveRound',
    headerText: i18n.t('认证轮次')
  },
  {
    field: 'approveResult',
    headerText: i18n.t('认证结果'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('通过'), 2: i18n.t('不通过') }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  }
]

// 供应商公示
export const supplierPublicityColumn = (isView) => [
  {
    field: 'indexData',
    headerText: i18n.t('编号'),
    cellTools: [
      {
        id: 'edit',
        title: i18n.t('编辑'),
        icon: 'icon_list_edit',
        visibleCondition: () => !isView
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => !isView
      }
    ]
  },
  {
    field: 'publicityStartTime',
    headerText: i18n.t('公示开始时间'),
    template: function () {
      return {
        template: Vue.component('publicityStartTime', {
          template: `<div>
            {{this.$utils.formateTime(Number(data.publicityStartTime),"yyyy-MM-dd hh:mm:ss")}}
          </div>`,
          data() {
            return { data: {}, sceneList: [] }
          }
        })
      }
    }
  },
  {
    field: 'publicityEndTime',
    headerText: i18n.t('公示结束时间'),
    template: function () {
      return {
        template: Vue.component('publicityEndTime', {
          template: `<div>
            {{this.$utils.formateTime(Number(data.publicityEndTime),"yyyy-MM-dd hh:mm:ss")}}
          </div>`,
          data() {
            return { data: {}, sceneList: [] }
          }
        })
      }
    }
  },
  {
    field: 'fileInfoResponses1',
    headerText: i18n.t('附件'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div>
            <p style="margin-top:5px;" v-for="(item,index) in data.fileInfoResponses" :key="index" ><a @click="preview(item)" >{{item.fileName}}</a><span style="margin-left:10px;cursor: pointer;" @click="upload(item)">{{ $t('下载') }}</span> </p>
          </div>`,
          data() {
            return { data: {}, sceneList: [] }
          },
          methods: {
            preview(item) {
              let params = {
                id: item.fileId,
                useType: 1
              }
              this.$API.SupplierPunishment.filepreview(params).then((res) => {
                window.open(res.data)
              })
            },
            upload(item) {
              this.$API.SupplierPunishment.fileDownload(item.fileId).then((res) => {
                let link = document.createElement('a')
                link.style.display = 'none'
                let blob = new Blob([res.data], { type: 'application/x-msdownload' })
                let url = window.URL.createObjectURL(blob)
                link.href = url
                link.setAttribute('download', `${item.fileName}`) // 给下载后的文件命名
                link.click() // 点击下载
                window.URL.revokeObjectURL(url)
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'messageNoticeInfoDTOS',
    headerText: i18n.t('通知人员'),
    template: function () {
      return {
        template: Vue.component('publicityEndTime', {
          template: `<div>
            {{data.messageNoticeInfoDTOS.map(item=>item.accountName).join("、")}}
          </div>`,
          data() {
            return { data: {}, sceneList: [] }
          }
        })
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  }
]
// 供应商发布
export const supplierPublishColumn = (isView) => [
  {
    field: 'indexData',
    headerText: i18n.t('编号'),
    cellTools: [
      {
        id: 'edit',
        title: i18n.t('编辑'),
        icon: 'icon_list_edit',
        visibleCondition: () => !isView
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => !isView
      }
    ]
  },
  {
    field: 'publishTime',
    headerText: i18n.t('发布时间'),
    template: function () {
      return {
        template: Vue.component('publishTime', {
          template: `<div>
            {{this.$utils.formateTime(Number(data.publishTime),"yyyy-MM-dd hh:mm:ss")}}
          </div>`,
          data() {
            return { data: {}, sceneList: [] }
          }
        })
      }
    }
  },
  {
    field: 'fileInfoResponses1',
    headerText: i18n.t('附件'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div>
            <p style="margin-top:5px;" v-for="(item,index) in data.fileInfoResponses" :key="index" ><a @click="preview(item)" >{{item.fileName}}</a><span style="margin-left:10px;cursor: pointer;" @click="upload(item)">{{ $t('下载') }}</span> </p>
          </div>`,
          data() {
            return { data: {}, sceneList: [] }
          },
          methods: {
            preview(item) {
              let params = {
                id: item.fileId,
                useType: 1
              }
              this.$API.SupplierPunishment.filepreview(params).then((res) => {
                window.open(res.data)
              })
            },
            upload(item) {
              this.$API.SupplierPunishment.fileDownload(item.fileId).then((res) => {
                let link = document.createElement('a')
                link.style.display = 'none'
                let blob = new Blob([res.data], { type: 'application/x-msdownload' })
                let url = window.URL.createObjectURL(blob)
                link.href = url
                link.setAttribute('download', `${item.fileName}`) // 给下载后的文件命名
                link.click() // 点击下载
                window.URL.revokeObjectURL(url)
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'messageNoticeInfoDTOS',
    headerText: i18n.t('通知人员'),
    template: function () {
      return {
        template: Vue.component('publicityEndTime', {
          template: `<div>
          <p v-for="(item,index)  in data.messageNoticeInfoDTOS" :key="index">{{(item.accountType ==1? i18n.t('公司员工'):'供应商') + '-' + item.accountName }}</p>
          </div>`,
          data() {
            return { data: {}, sceneList: [] }
          }
        })
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  }
]
// 预算调查表
export const preColumn = () => [
  { width: '50', type: 'checkbox' },
  {
    field: 'code',
    headerText: i18n.t('单据编码'),
    width: 200,
    cellTools: []
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'industryBelong',
    headerText: i18n.t('所属行业')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未发布'),
        1: i18n.t('待填写'),
        2: i18n.t('已填写'),
        3: i18n.t('已批准'),
        4: i18n.t('已驳回')
      }
    }
  },
  {
    field: 'oaApproveStatus',
    headerText: i18n.t('OA审批状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('待OA审批'),
        1: i18n.t('OA审批中'),
        2: i18n.t('已通过'),
        3: i18n.t('已驳回')
      }
    }
  },
  {
    field: 'purchaseAmount',
    headerText: i18n.t('预估年采购金额（万元)'),
    width: 200,
    template: function () {
      return {
        template: Vue.component('purchaseAmount', {
          template: `<div>
          <mt-input-number
            v-model="data.purchaseAmount"
            :min="0"
            @blur="dataChange"
          ></mt-input-number>
          </div>`,
          data() {
            return { data: {} }
          },
          methods: {
            dataChange() {
              let params = {
                key: 'purchaseAmount',
                value: this.data
              }
              this.$parent.$emit('cellEdit', params)
            }
          }
        })
      }
    }
  },
  {
    field: 'rejectReason',
    headerText: i18n.t('驳回理由')
  }
]
// 综合审批
export const approvalColumn = (isView) => [
  {
    field: 'approveCode',
    headerText: i18n.t('单据编码'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => !isView && (data.status == 10 || data.status == 30)
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => !isView && (data.status == 10 || data.status == 30)
      }
    ],
    cssClass: ''
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        10: i18n.t('待提交'),
        20: i18n.t('待审批'),
        30: i18n.t('已驳回'),
        40: i18n.t('已完成')
      }
    }
  },
  {
    field: 'sceneName',
    headerText: i18n.t('场景')
  },
  {
    field: 'effectiveMethod',
    headerText: i18n.t('生效方式'),
    valueConverter: {
      type: 'map',
      map: {
        10: i18n.t('线下采委会审批'),
        20: i18n.t('线上审批'),
        '': i18n.t('线上审批'),
        null: i18n.t('线上审批')
      }
    }
  },
  {
    field: 'qualificationApproveResult',
    headerText: i18n.t('资质审查结果'),
    clipMode: 'Ellipsis',
    template: function () {
      return {
        template: Vue.component('qualificationApproveResult', {
          template: `<div>
            <mt-select
              v-model="data.qualificationApproveResult"
              :data-source="roleList"
              :fields="{ text: 'text', value: 'value' }"
              :placeholder="$t('请选择')"
              float-label-type="Never"
              @blur="dataChange"
              :disabled="data.status == 20 || data.status == 40"
            ></mt-select>
          </div>`,
          data() {
            return {
              data: {},
              roleList: [
                {
                  text: i18n.t('通过'),
                  value: '40'
                },
                {
                  text: i18n.t('不通过'),
                  value: '50'
                }
              ]
            }
          },
          methods: {
            dataChange() {
              this.$parent.$emit('cellEdit', this.data, 'qualificationApproveResult')
            }
          }
        })
      }
    }
  },
  {
    field: 'fileInfos',
    headerText: i18n.t('附件'),
    template: function () {
      return {
        template: Vue.component('fileInfos', {
          template: `<div>
            <input
              ref="file"
              type="file"
              style="display: none"
              @change="chooseFiles"
              multiple="multiple"
              accept=".xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar,"
            />
            <mt-button v-if="data.status != 20 && data.status != 40" class="mgn-left-10" @click="browseBtn()">{{
              $t("上传文件")
            }}</mt-button>
            <p style="margin-top:5px;" v-for="(item,index) in data.fileInfos" :key="index" ><a @click="preview(item)">{{item.fileName}}</a> <span v-if="data.status != 20 && data.status != 40" style="margin-left:10px;cursor: pointer;" @click="deleteFile(item)">{{ $t('删除') }}</span> <span style="margin-left:10px;cursor: pointer;" @click="upload(item)">{{ $t('下载') }}</span></p>
          </div>`,
          data() {
            return {
              data: {},
              sceneList: [],
              allowFileType: [
                'xls',
                'xlsx',
                'doc',
                'docx',
                'pdf',
                'ppt',
                'pptx',
                'png',
                'jpg',
                'zip',
                'rar'
              ]
            }
          },
          methods: {
            chooseFiles(data) {
              this.$loading()
              let { files } = data.target
              files = Object.values(files)
              let params = {
                type: 'array',
                limit: 50 * 1024,
                msg: this.$t('单个文件，限制50M')
              }
              if (files.length < 1) {
                this.$hloading()
                // 您未选择需要上传的文件
                return
              } else if (files.length > 5) {
                this.$hloading()
                this.$toast({
                  content: this.$t('一次性最多选择5个文件'),
                  type: 'warning'
                })
                return
              }
              let bol = files.some((item) => {
                let _tempInfo = item.name.split('.')
                return (
                  _tempInfo.length < 2 ||
                  !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])
                )
              })
              if (bol) {
                this.$toast({
                  content: this.$t('文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'),
                  type: 'warning'
                })
                this.$hloading()
                return
              }
              bol = files.some((item) => {
                return item.size > params.limit * 1024
              })
              if (bol) {
                this.$hloading()
                this.$toast({
                  content: params.msg
                })
                return
              }
              this.$refs.file.value = ''
              this.uploadFile(files)
            },
            uploadFile(files) {
              let arr = []
              files.forEach((item) => {
                let _data = new FormData()
                _data.append('UploadFiles', item)
                _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
                arr.push(this.$API.SupplierPunishment.fileUpload(_data))
              })
              Promise.all(arr).then((res) => {
                res.forEach((item) => {
                  if (item.code == 200) {
                    item.data.fileId = item.data.fileId ? item.data.fileId : item.data.id
                    item.data.fileUrl = item.data.fileUrl ? item.data.fileUrl : item.data.url
                    this.data.fileInfos.push(item.data)
                  }
                })
                this.$hloading()
                this.$parent.$emit('cellEdit', this.data, 'fileInfos')
              })
            },
            browseBtn() {
              this.$refs.file.click()
            },
            preview(item) {
              let params = {
                id: item.fileId,
                useType: 1
              }
              this.$API.SupplierPunishment.filepreview(params).then((res) => {
                window.open(res.data)
              })
            },
            upload(item) {
              this.$API.SupplierPunishment.fileDownload(item.fileId).then((res) => {
                let link = document.createElement('a')
                link.style.display = 'none'
                let blob = new Blob([res.data], { type: 'application/x-msdownload' })
                let url = window.URL.createObjectURL(blob)
                link.href = url
                link.setAttribute('download', `${item.fileName}`) // 给下载后的文件命名
                link.click() // 点击下载
                window.URL.revokeObjectURL(url)
              })
            },
            deleteFile(item) {
              const index = this.data.fileInfos.findIndex((file) => file.id === item.id)
              this.data.fileInfos.splice(index, 1)
              this.$parent.$emit('cellEdit', this.data, 'fileInfos')
            }
          }
        })
      }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '180',
    clipMode: 'Ellipsis',
    template: function () {
      return {
        template: Vue.component('remark', {
          template: `<div>
          <mt-input
            v-model="data.remark"
            :placeholder="$t('请输入备注')"
            :disabled="data.status == 20 || data.status == 40"
            @blur="dataChange"
          ></mt-input>
          </div>`,
          data() {
            return { data: {} }
          },
          methods: {
            dataChange() {
              this.$parent.$emit('cellEdit', this.data, 'remark')
            }
          }
        })
      }
    }
  }
]
export const thresholdMatchColumn = [
  { width: '50', type: 'checkbox' },
  {
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商')
  },
  {
    field: 'failReachStandardRedLineItem',
    headerText: i18n.t('未达标红线项'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e?.toString()
      }
    },
    cellTools: []
  },
  {
    field: 'thresholdPass',
    headerText: i18n.t('门槛符合项'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e.toString()
      }
    },
    cellTools: []
  },
  {
    field: 'thresholdFail',
    headerText: i18n.t('门槛不符合项'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e.toString()
      }
    },
    cellTools: []
  },
  {
    field: 'redLineItemStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        10: i18n.t('待提交'),
        20: i18n.t('待审批'),
        30: i18n.t('已通过'),
        40: i18n.t('已驳回')
      }
    }
  }
]

export const thresholdDetailColumns = [
  {
    field: 'thresholdName',
    headerText: i18n.t('门槛名称'),
    width: '180'
  },
  {
    field: 'fieldName',
    headerText: i18n.t('监控字段')
    // width: "180",
  },
  {
    field: 'formType',
    headerText: i18n.t('门槛类型'),
    // width: "180",
    // 修改显示
    valueAccessor: function (field, data) {
      return formTypeList.find((e) => e.itemCode == data[field]).itemName
    }
  },
  {
    field: 'originalValue',
    // width: "150",
    headerText: i18n.t('门槛原值'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div>{{hibition()}}</div>`,
          data() {
            return {
              data: {}
            }
          },
          methods: {
            hibition() {
              if (this.data.originalValue === '0') {
                return i18n.t('否')
              } else if (this.data.originalValue === '1') {
                return i18n.t('是')
              } else {
                return this.data.originalValue
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'thresholdProject',
    // width: "150",
    headerText: i18n.t('门槛项目'),
    // 修改显示
    valueAccessor: function (field, data) {
      let tempRecord = thresholdProjectList.find((e) => e.itemCode == data[field])
      if (!tempRecord) {
        return ''
      } else {
        return tempRecord.itemName
      }
    }
  },
  {
    field: 'symbol',
    headerText: i18n.t('操作符'),
    // 修改显示
    valueAccessor: function (field, data) {
      return symbolMap[data[field]]
    }
  },
  {
    field: 'defaultValue',
    headerText: i18n.t('默认目标值'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div>{{hibition()}}</div>`,
          data() {
            return {
              data: {}
            }
          },
          methods: {
            hibition() {
              if (this.data.defaultValue === '0') {
                return i18n.t('否')
              } else if (this.data.defaultValue === '1') {
                return i18n.t('是')
              } else {
                return this.data.defaultValue
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('是否符合门槛'),
    width: 150,
    formatter: ({ field }, item) => {
      console.log('11111', field)
      const cellVal = item[field]
      switch (Number(cellVal)) {
        case 0:
          return i18n.t('否')
        case 1:
          return i18n.t('是')
        default:
          return cellVal
      }
    }
  }
]
