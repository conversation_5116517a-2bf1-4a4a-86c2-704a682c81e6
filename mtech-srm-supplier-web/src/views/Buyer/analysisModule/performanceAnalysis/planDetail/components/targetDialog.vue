<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="dimensionId" :label="$t('维度：')">
          <mt-select
            ref="indexRef"
            v-model="formObject.dimensionId"
            float-label-type="Never"
            :disabled="disabled"
            :data-source="dimensionList"
            :fields="{ text: 'dimensionName', value: 'id' }"
            :placeholder="$t('请选择维度')"
            @change="handleSelectChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-if="modalData.showNum == 2" prop="indexName" :label="$t('指标类名称：')">
          <mt-input
            :disabled="editStatus"
            v-model="formObject.indexName"
            type="text"
            :placeholder="$t('请输入指标类名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="modalData.showNum == 3" prop="indexCode" :label="$t('指标：')">
          <mt-select
            ref="indexRef"
            :disabled="editStatus"
            v-model="formObject.indexCode"
            float-label-type="Never"
            :data-source="indexList"
            :fields="{ text: 'indexName', value: 'indexCode' }"
            :placeholder="$t('请选择指标')"
            @change="indexSelectChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-if="modalData.showNum != 3" prop="scoreType" :label="$t('计分方式：')">
          <mt-select
            v-model="formObject.scoreType"
            float-label-type="Never"
            :disabled="editStatus"
            :allow-filtering="true"
            :data-source="approachesToList"
            :placeholder="$t('请选择计分方式')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          v-if="modalData.weightShow && formObject.indexKind != 2"
          prop="scoreWeight"
          :label="$t('在维度内权重(%)：')"
        >
          <mt-input-number
            ref="weightRef"
            :min="0"
            :max="100"
            v-model="formObject.scoreWeight"
            :placeholder="$t('输入权重1~100')"
            @change="changeWeight"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="minValue" class="form-item" :label="$t('最小值')">
          <mt-input-number
            v-if="formObject.indexKind != 2"
            v-model="formObject.minValue"
            :disabled="modalData.showNum == 3"
            :show-clear-button="true"
            type="number"
            :min="0"
            :placeholder="$t('请输入最小值')"
            @change="minChange(formObject)"
          ></mt-input-number>
          <mt-input-number
            v-else
            v-model="formObject.minValue"
            :disabled="modalData.showNum == 3"
            :show-clear-button="true"
            type="number"
            :placeholder="$t('请输入最小值')"
            @change="minChange(formObject)"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="maxValue" class="form-item" :label="$t('满分值')">
          <mt-input-number
            v-model="formObject.maxValue"
            :disabled="modalData.showNum == 3"
            :show-clear-button="true"
            @change="maxChange(formObject)"
            type="number"
            :min="0"
            :placeholder="$t('请输入满分值')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item
          v-if="(modalData.showNum == 2 || modalData.showNum == 3) && formObject.indexKind != 2"
          prop="oneVeto"
          :label="$t('是否一票否决：')"
        >
          <mt-select
            v-model="formObject.oneVeto"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="oneVetoList"
            :placeholder="$t('请选择是否一票否决')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
      <div style="display: flex; align-items: center">
        <div
          style="
            margin-right: 10px;
            color: #465b73;
            font-weight: 600;
            font-size: 14px;
            text-align: left;
            white-space: nowrap;
          "
        >
          {{ $t('描述：') }}
        </div>
        <!-- <div style="width: 100%" v-if="show"> -->
        <div style="width: 100%">
          <!-- <mt-input
            v-model="formObject.remark"
            :maxlength="500"
            type="text"
            :placeholder="$t('请输入描述信息')"
            @blur="show = false"
            :multiline="true"
            :rows="3"
          ></mt-input> -->
          <mt-input
            v-model="formObject.remark"
            :maxlength="500"
            type="text"
            :placeholder="$t('请输入描述信息')"
            :multiline="true"
            :rows="3"
          ></mt-input>
        </div>
        <!-- <div style="width: 100%" v-else>
          <span>{{ formObject.remark || $t("这是描述信息") }}</span>
          <span @click="show = true">
            <MtIcon name="icon_list_edit" />
          </span>
        </div> -->
      </div>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  props: {
    // 父组件传值 数据集合
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      show: false,
      disabled: false,
      header: '',
      oneVetoList: [
        {
          text: this.$t('是'),
          value: '1'
        },
        {
          text: this.$t('否'),
          value: '0'
        }
      ],
      approachesToList: [
        {
          text: this.$t('子项求和'),
          value: 'sum',
          key: '0'
        },
        {
          text: this.$t('权重式计算'),
          value: 'weight',
          key: '1'
        },
        {
          text: this.$t('子项最高值'),
          value: 'max',
          key: '2'
        },
        {
          text: this.$t('子项最低值'),
          value: 'min',
          key: '3'
        },
        {
          text: this.$t('子项平均值'),
          value: 'average',
          key: '4'
        }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        dimensionId: null
      },
      formRules: {
        scoreType: [{ required: true, message: this.$t('请选择计分方式'), trigger: 'blur' }],
        dimensionId: [{ required: true, message: this.$t('请选择维度'), trigger: 'blur' }],
        scoreWeight: [{ required: true, message: this.$t('请设置权重'), trigger: 'blur' }],
        minValue: [{ required: true, message: this.$t('请输入最小值'), trigger: 'blur' }],
        maxValue: [{ required: true, message: this.$t('请输入满分值'), trigger: 'blur' }]
      },
      indexList: [],
      dimensionList: [],
      editStatus: false,

      dimensionIdArr: [], // 维度id集合（同级唯一）
      indexNameObj: {}, // 指标类名称对象（该维度下唯一）{dimensionId: [indexNameArr]
      indexCodeArr: [] // 指标编码集合（唯一）
    }
  },
  created() {
    this.header = this.modalData.title
    this.editStatus = this.modalData.edit || false
    if (this.modalData.data && this.modalData.num != 1 && !this.editStatus) {
      this.formObject = {
        dimensionCode: this.modalData.data.dimensionCode,
        dimensionId: this.modalData.data.dimensionId,
        dimensionName: this.modalData.data.dimensionName,
        indexLevel: this.modalData.data.indexLevel,
        parentId: this.modalData.data.parentId
      }
    } else {
      this.formObject = { ...this.modalData.data }
      // 比如处理编辑
    }
    let str = this.modalData.data.calcType || this.modalData.data.scoreType
    console.log(str)
    if (str == 'weight') {
      this.approachesToList = [
        {
          text: this.$t('子项求和'),
          value: 'sum',
          key: '0'
        },
        {
          text: this.$t('权重式计算'),
          value: 'weight',
          key: '1'
        },
        {
          text: this.$t('子项最高值'),
          value: 'max',
          key: '2'
        },
        {
          text: this.$t('子项最低值'),
          value: 'min',
          key: '3'
        },
        {
          text: this.$t('子项平均值'),
          value: 'average',
          key: '4'
        }
      ]
    } else {
      this.approachesToList = [
        {
          text: this.$t('子项求和'),
          value: 'sum',
          key: '0'
        },
        {
          text: this.$t('子项最高值'),
          value: 'max',
          key: '2'
        },
        {
          text: this.$t('子项最低值'),
          value: 'min',
          key: '3'
        },
        {
          text: this.$t('子项平均值'),
          value: 'average',
          key: '4'
        }
      ]
    }
    if (this.modalData.showNum != 1) {
      this.disabled = true
    }
    if (this.editStatus) {
      this.disabled = true
    }
    if (this.modalData.showNum == 2) {
      this.formObject['indexType'] = 1
      let validateCheckIndexName = (rule, value, callback) => {
        if (value === '') {
          callback(new Error(this.$t('请输入指标类名称')))
        } else if (this.checkNewAddIndexNameIsRepeated(value) && !this.editStatus) {
          callback(new Error(this.$t('指标类名称不可重复')))
        } else {
          callback()
        }
      }
      this.formRules = {
        indexName: [
          {
            required: true,
            message: this.$t('请输入指标类名称'),
            trigger: 'blur'
          },
          { validator: validateCheckIndexName, trigger: 'blur' }
        ],
        scoreType: [{ required: true, message: this.$t('请选择计分方式'), trigger: 'blur' }],
        oneVeto: [{ required: true, message: this.$t('请选择是否一票否决'), trigger: 'blur' }],
        minValue: [{ required: true, message: this.$t('请输入最小值'), trigger: 'blur' }],
        maxValue: [{ required: true, message: this.$t('请输入满分值'), trigger: 'blur' }]
      }
    } else if (this.modalData.showNum == 3) {
      this.formObject['indexType'] = 2
      let validateCheckIndexCode = (rule, value, callback) => {
        if (value === '') {
          callback(new Error(this.$t('请选择指标')))
        } else if (this.checkNewAddIndexCodeIsRepeated(value) && !this.editStatus) {
          callback(new Error(this.$t('指标不可重复')))
        } else {
          callback()
        }
      }
      this.formRules = {
        indexCode: [
          { required: true, message: this.$t('请选择指标'), trigger: 'blur' },
          { validator: validateCheckIndexCode, trigger: 'blur' }
        ],
        oneVeto: [
          {
            required: true,
            message: this.$t('请选择是否一票否决'),
            trigger: 'blur'
          }
        ]
      }
    } else {
      let validateCheckDimensionId = (rule, value, callback) => {
        if (!value || value === '') {
          callback(new Error(this.$t('请选择维度')))
        } else if (this.checkNewAddDimensionIdIsRepeated(value) && !this.editStatus) {
          callback(new Error(this.$t('维度不可重复')))
        } else {
          callback()
        }
      }
      this.formRules = {
        scoreType: [
          {
            required: true,
            message: this.$t('请选择计分方式'),
            trigger: 'blur'
          }
        ],
        dimensionId: [
          { required: true, message: this.$t('请选择维度'), trigger: 'blur' },
          { required: true, validator: validateCheckDimensionId, trigger: 'blur' }
        ],
        minValue: [{ required: true, message: this.$t('请输入最小值'), trigger: 'blur' }],
        maxValue: [{ required: true, message: this.$t('请输入满分值'), trigger: 'blur' }]
      }
    }
    if (this.modalData.weightShow) {
      this.formRules['scoreWeight'] = [
        { required: true, message: this.$t('请设置权重'), trigger: 'blur' }
      ]
      this.formRules['minValue'] = [
        { required: true, message: this.$t('请输入最小值'), trigger: 'blur' }
      ]
      this.formRules['maxValue'] = [
        { required: true, message: this.$t('请输入满分值'), trigger: 'blur' }
      ]
    }
    this.init()
  },
  methods: {
    // 筛选出维度id集合（同级唯一）、指标类名称集合（该维度下唯一）、指标编码集合（唯一）
    filterData(data, dimensionIds, indexNameObj, indexCodes) {
      data.forEach((ele) => {
        // indexLevel	层级（1一级，2二级 3三级）
        if (ele.indexLevel == 1) {
          // 维度
          dimensionIds.push(ele.dimensionId)
          indexNameObj[ele.dimensionId] = []
          if (ele.childrenList && ele.childrenList.length) {
            this.filterData(ele.childrenList, dimensionIds, indexNameObj, indexCodes)
          }
        } else {
          // indexType	指标类型（1指标类, 2指标）
          if (ele.indexType == 1) {
            // 指标类
            indexNameObj[ele.dimensionId].push(ele.indexName)
            if (ele.childrenList && ele.childrenList.length) {
              this.filterData(ele.childrenList, dimensionIds, indexNameObj, indexCodes)
            }
          } else {
            // 指标
            indexCodes.push(ele.indexCode)
          }
        }
      })
    },
    init() {
      // 筛选出维度id集合（同级唯一）、指标类名称集合（该维度下唯一）、指标编码集合（唯一）
      this.filterData(
        this.modalData.planDetail.itemDTOList,
        this.dimensionIdArr,
        this.indexNameObj,
        this.indexCodeArr
      )

      this.$API.planDetail.dimensionListQuery().then((res) => {
        // console.log(res)
        this.dimensionList = res.data
      })
      if (this.modalData.showNum == 3) {
        this.$API.planDetail
          .queryListByDimensionIdAndOrgId(
            `orgId=${this.modalData.orgId}&&dimensionId=${this.formObject.dimensionId}`
          )
          .then((res) => {
            console.log(res)
            if (this.modalData.data.indexLevel == 2) {
              this.indexList = res.data.records.filter((item) => {
                return item.indexType == 1
              })
            } else {
              this.indexList = res.data.records
            }
          })
      }

      // indexList
    },
    focusOut() {
      console.log(123)
    },
    indexSelectChange(e) {
      // this.formObject['indexCode']=e.itemData.indexCode
      this.$set(this.formObject, 'indexName', e.itemData.indexName || '')
      this.$set(this.formObject, 'indexCode', e.itemData.indexCode || '')
      this.$set(this.formObject, 'indexVersion', e.itemData.version || '')
      this.$set(this.formObject, 'indexKind', e.itemData.indexType || '')
      this.$set(this.formObject, 'indexDescribe', e.itemData.indexDescribe || '')
      this.$set(this.formObject, 'remark', e.itemData.remark || '')
      if (this.formObject.indexKind == 2) {
        this.formRules['scoreWeight'] = []
        this.formRules['oneVeto'] = []
      }
      if (e.itemData.calcRuleDTOList && e.itemData.calcRuleDTOList.length > 0) {
        this.$set(this.formObject, 'calcRuleCode', e.itemData.calcRuleDTOList[0].calcRuleCode || '')
        this.$set(this.formObject, 'calcRuleId', e.itemData.calcRuleDTOList[0].id || '')
        this.$set(this.formObject, 'calcRuleName', e.itemData.calcRuleDTOList[0].calcRuleName || '')
        this.$set(this.formObject, 'maxValue', e.itemData.calcRuleDTOList[0].maxValue || 0)
        // this.$set(
        //   this.formObject,
        //   "scoreTypes",
        //   e.itemData.calcRuleDTOList[0].calcRuleType
        // );

        this.$set(this.formObject, 'minValue', e.itemData.calcRuleDTOList[0].minValue || 0)
        this.$set(
          this.formObject,
          'referenceValue',
          e.itemData.calcRuleDTOList[0].referenceValue || 0
        )
        this.$set(
          this.formObject,
          'indexRange',
          e.itemData.calcRuleDTOList[0].referenceValue +
            '~' +
            e.itemData.calcRuleDTOList[0].maxValue
        )
      }

      console.log(e, this.formObject)
    },
    handleSelectChange(e) {
      console.log(e)
      // this.formObject["dimensionId"] = e.itemData.id;
      this.formObject['dimensionCode'] = e.itemData.dimensionCode
      this.formObject['dimensionName'] = e.itemData.dimensionName
      console.log(this.formObject)
    },
    changeWeight() {},
    // 校验新添加的维度是否重复（同级，维度只会存在于一级，判断所有维度）
    checkNewAddDimensionIdIsRepeated(newDimensionId) {
      return (
        this.dimensionIdArr.findIndex((dimensionId) => {
          return dimensionId === newDimensionId
        }) != -1
      )
    },
    // 校验新添加的指标类是否重复（同级，判断该维度下）
    checkNewAddIndexNameIsRepeated(newIndexName) {
      return (
        this.indexNameObj[this.modalData.data.dimensionId].findIndex((indexName) => {
          return indexName === newIndexName
        }) != -1
      )
    },
    // 校验新添加的指标是否重复（唯一，判断所有指标）
    checkNewAddIndexCodeIsRepeated(newIndexCode) {
      return (
        this.indexCodeArr.findIndex((indexCode) => {
          return indexCode === newIndexCode
        }) != -1
      )
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.formObject))
          // console.log(this.modalData.minweight,params.scoreWeight)

          let minweight = this.modalData.minweight * 100
          if (params.childrenList && params.childrenList.length > 0) {
            let num = 0
            params.childrenList.forEach((ele) => {
              if (ele.indexKind != 2) {
                num = num + Number(ele.maxValue)
              }
            })
            if (num > params.maxValue && !this.modalData.weightShow) {
              this.$toast({
                content: this.$t('满分值不可小于子级分值之和，请重新设置'),
                type: 'warning'
              })
              return
            }
          }
          if (this.modalData.weightShow) {
            if (
              (params.scoreWeight &&
                Number(params.scoreWeight) > 0 &&
                Number(params.scoreWeight) + minweight <= 100) ||
              params.indexKind == 2
            ) {
              //新增操作，可以通过下拉列表，选择维度
              this.$emit('confirm-function', params)
            } else {
              this.$toast({
                content: this.$t('权重之和大于100%，请重新设置'),
                type: 'warning'
              })
            }
          } else {
            if (params.indexKind != 2 && this.modalData.minweight < Number(params.maxValue)) {
              this.$toast({
                content: this.$t('满分值和不能大于父级满分值，请重新设置'),
                type: 'warning'
              })
            } else {
              this.$emit('confirm-function', params)
            }
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 最小变化
    minChange(item) {
      if (!item.minValue) {
        return
      }
      if (item.maxValue && Number(item.minValue) > Number(item.maxValue)) {
        item.minValue = null
        this.$toast({
          content: this.$t('最小值不得大于满分值'),
          type: 'warning'
        })
        return
      }
    },
    // 满分值变化
    maxChange(item) {
      if (!item.maxValue) {
        return
      }
      if (item.minValue && Number(item.minValue) > Number(item.maxValue)) {
        item.maxValue = null
        this.$toast({
          content: this.$t('满分值不得小于最小值'),
          type: 'warning'
        })
        return
      }
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 32px 6px 0 6px !important;
}
</style>
