// 供应商调查表管理 接口
import { API } from '@mtech-common/http'
const NAME = 'SupplierPunishment'
const PROXY_BASE = '/supplier'
const PROXY_File = '/file'
const PROXY_MASTERDATA = '/masterDataManagement'
const APIS = {
  // 新增调查表
  addFormTemplate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/share/add`, data)
  },
  //获取共享表详情
  getDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/share/detail`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 删除共享
  deleteshare: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/share/delete`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 编辑共享
  updatashare: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/share/update`, data)
  },
  // 编辑升级
  updatagrade: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/grade/update`, data)
  },
  // 删除分级
  deleteba: (data = []) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/grade/delete`, data)
  },
  // 删除处罚
  deletebb: (data = []) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/punish/delete`, data)
  },
  deleteNew: (data = []) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/punish/del`, data)
  },
  // 删除解除处罚
  deletebc: (data = []) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/relive/delete`, data)
  },
  // 删除升降级
  deletebd: (data = []) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/stage/delete`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  //获取分级表详情
  getclassificationDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/grade/detail/${data.id}`)
  },
  // 删除调查表
  delStage: (data = []) => {
    return API.get(`${PROXY_BASE}/tenant/common/file/delete`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 获取分级的级别
  getjibeilist: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/label/define/defineList`, data)
  },

  // 新增附件
  adduploader: (data = []) => {
    return API.post(`${PROXY_BASE}/tenant/common/file/add`, data)
  },

  // 新增附件
  addUploaderResult: (data = []) => {
    return API.post(`${PROXY_BASE}/tenant/common/file/addResult`, data)
  },
  // 新增附件
  addUploaderaddAccessory: (data = []) => {
    return API.post(`${PROXY_BASE}/tenant/common/file/addAccessory`, data)
  },
  deleteOneFile: (data = []) => {
    return API.post(`${PROXY_BASE}//tenant/common/file/deleteOne`, data)
  },

  // 编辑附件
  updateFile: (data = []) => {
    return API.post(`${PROXY_BASE}/tenant/common/file/update`, data)
  },

  // Header: api-token
  uploadLogo(params) {
    return API.post('/file/user/file/uploadLogo', params)
  },

  // 文件上传
  fileUpload: (data = []) => {
    return API.post(`${PROXY_File}/user/file/uploadPublic`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  // 文件上传
  fileUploadSit: (data = []) => {
    return API.post(`${PROXY_File}/user/file/uploadPrivate`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  // 文件上传
  fileUploadSitHaz: (data = []) => {
    return API.post(`${PROXY_File}/user/file/uploadPrivate?useType=1`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },
  fileDownload: (data = {}) =>
    API.get(`/file/user/file/downloadPrivateFile?useType=1&id=${data}`, '', {
      responseType: 'blob'
    }),
  filepreview: (data = {}) => API.get(`/file/user/file/mtPreview`, data),
  // 获取惩罚淘汰详情
  getchengfaDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/punish/detail/${data.id}`)
  },
  // 获取解除惩罚淘汰详情
  getRelieveDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/relive/detail/${data.id}`)
  },
  // 获取全量
  getAllPurchaseOrg: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/effective/purchaseOrg/list`, data)
  },
  // 获取采购组织
  listByPartnerAndOrg: (data = {}) => {
    return API.post(
      `${PROXY_MASTERDATA}/tenant/business_partner_purchasing/listByPartnerAndOrg`,
      data
    )
  },
  // 修改惩罚
  updatapunishment: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/punish/update`, data)
  },
  // 修改
  updatePurchaseOrg: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/punish/updatePurchaseOrg`, data)
  },
  // 获取解除淘汰的详情
  getjeiDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/relive/detail/${data.id}`)
  },
  // 修改解除申请单
  updatapuncomfm: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/relive/update`, data)
  },
  // 分级详情
  getzidongfenjiDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/stage/detail`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 惩罚新增
  getaddpunish: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/punish/add`, data)
  },
  // 解除新增
  getReliveAdd: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/relive/add`, data)
  },
  // 分级新增
  addnewfenji: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/grade/add`, data)
  },
  // 所有的提交
  deare: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/info/submit`, data)
  },
  // 申请提交工作流
  applySubmit: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/info/submit`, data)
  },

  // 批量冻结-导入
  importFreezApi: (data = {}) =>
    API.post(`/supplier/tenant/buyer/apply/punish/freez/importTemplate`, data, {
      responseType: 'blob'
    }),
  // 批量冻结-模板下载
  downloadFreezApi: (data = {}) =>
    API.post(`/supplier/tenant/buyer/apply/punish/freezTemplate`, data, {
      responseType: 'blob'
    }),

  // 批量退出-导入
  importDisuseApi: (data = {}) =>
    API.post(`/supplier/tenant/buyer/apply/punish/disuse/importTemplate`, data, {
      responseType: 'blob'
    }),
  // 批量退出-模板下载
  downloadDisuseApi: (data = {}) =>
    API.post(`/supplier/tenant/buyer/apply/punish/disuseTemplate`, data, {
      responseType: 'blob'
    }),

  // 供应商批量惩罚-处罚申请详情
  batchDetailApi: (data = {}) => {
    return API.post(`/supplier/tenant/buyer/apply/punish/batchDetail`, data)
  },
  // 供应商批量惩罚-处罚删除详情
  batchDeleteApi: (data = {}) => {
    return API.post(`/supplier/tenant/buyer/apply/punish/batchDelete`, data)
  },
  // 供应商批量惩罚-更新原因
  batchUpdateApi: (data = {}) => {
    return API.post(`/supplier/tenant/buyer/apply/punish/batchUpdate`, data)
  },
  // 供应商批量惩罚-提交
  batchSubmitApi: (data = {}) => {
    return API.post(`/supplier/tenant/buyer/apply/info/batch/submit`, data)
  },
  // 供应商批量惩罚-删除日志列表
  pageBatchLogQueryApi: (data = {}) => {
    return API.post(`/supplier/tenant/buyer/apply/punish/pageBatchLogQuery`, data)
  },

  // 租户级-组织机构接口-获取当前组织的父级组织节点
  getParentOrg: (data = {}) => {
    return API.get(`${PROXY_MASTERDATA}/tenant/organization/getParentOrg`, data)
  },

  // 新增晋级申请单
  upDownAdd: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/stage/add`, data)
  },

  // 编辑
  upDownUpdate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/stage/update`, data)
  },

  // 第一层品类
  getProductList: (data = {}) => {
    return API.post(`${PROXY_MASTERDATA}/common/category/platform-product-list`, data)
  },

  // 获取相关状态得品类
  getCategoryPartnerRelationsByStatus: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/partner/relation/getCategoryPartnerRelationsByStatus`,
      data
    )
  },

  // 申请单详情新增处罚品类
  addCategory: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/punish/addCategory`, data)
  },
  // 申请单详情新增处罚品类
  updateCategory: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/punish/updateCategory`, data)
  },
  // 下发
  punishIssue: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/punish/sync/sap/status`, data)
  },
  // 退出里程碑 - 清理PO-查询接口
  getPurchaseOrder: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/punish/getPurchaseOrder/${data.id}`)
  },
  // 退出里程碑 - 财务账务清理-查询接口
  getOutstandingClaim: (data = {}) => {
    return API.get(
      `${PROXY_BASE}/tenant/buyer/apply/punish/getOutstandingClaim/${data.id}/${data.type}`
    )
  }
}

export default {
  NAME,
  APIS
}
