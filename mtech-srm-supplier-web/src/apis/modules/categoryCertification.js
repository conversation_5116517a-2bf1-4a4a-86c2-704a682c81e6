// 品类认证管理
import { API } from '@mtech-common/http'
const NAME = 'CategoryCertification'
const PROXY_BASE = '/supplier'
const PROXY_BASEASONE = '/masterDataManagement' //主数据

const APIS = {
  // 认证结果字典
  approvedict: (data = {}) => {
    return API.post(`${PROXY_BASEASONE}/tenant/dict-item/dict-code`, data)
  },
  // 认证需求列表
  demandQuery: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/manager/demandQuery`, data)
  },

  // 认证需求详情
  queryDemandDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/manager/demandDetail`, data)
  },
  // 新增认证需求
  addDemand: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/manager/addDemand`, data)
  },
  // 编辑认证需求
  updateDemand: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/manager/updateDemand`, data)
  },
  // 删除认证需求
  deleteDemand: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/manager/deleteDemand`, data)
  },
  // 供应商视图
  supplierView: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/manager/supplierView`, data)
  },

  // 认证项目详情
  queryProjectDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/manager/projectDetail`, data)
  },
  // 新增认证项目
  addProject: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/manager/addProject`, data)
  },
  // 编辑认证项目
  updateProject: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/manager/updateProject`, data)
  },
  // 删除认证项目
  deleteProject: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/manager/deleteProject`, data)
  },
  // 品类认证项目-终止认证
  stopProjects: (data = []) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/manager/stopProjects`, data, {
      headers: { 'Content-Type': 'application/json' }
    })
  },
  // 品类认证项目-重新认证
  restartProjects: (data = []) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/manager/restartProjects`, data, {
      headers: { 'Content-Type': 'application/json' }
    })
  },

  // 认证需求详情
  projectStageTask: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/manager/projectStageTask`, data)
  },

  // 品类认证管理-跳过
  skipTaskApi: (data = {}) => {
    return API.post(`supplier/tenant/buyer/auth/manager/skipTask`, data)
  },

  // 提交任务
  submitTask: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/manager/submit`, data)
  },

  // 品类供应商关系清单
  getSupListByCategory: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/process/manager/listByCategoryId`, data)
  },

  // 新增小组成员
  addGroup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/group/addGroup`, data)
  },
  // 编辑小组成员
  updateGroup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/group/updateGroup`, data)
  },
  // 删除小组成员
  deleteGroup: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/group/deleteGroup`, data)
  },
  // 新增策略共识
  addView: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/view/addView`, data)
  },
  // 删除策略共识
  deleteView: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/view/deleteView`, data)
  },
  // 新增供应商
  addSupplier: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/supplier/add`, data)
  },
  // 编辑供应商
  updateSupplier: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/supplier/update`, data)
  },
  // 删除供应商
  deleteSupplier: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/supplier/delete`, data)
  },

  // 引入供应商列表
  getSupListByAuthCode: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/supplier/list`, data)
  },

  // 引入供应商列表分页查询--项目任务已完成
  getFinishedSupList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/supplier/checkAuth/query`, data)
  },

  // 引入供应商列表分页查询
  importedSupplier: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/supplier/query`, data)
  },
  // 引入供应商列表部分字段查询
  queryByAuthProjectSupplierIdApi: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/exit/supplier/check/import/queryByAuthProjectSupplierId`,
      data
    )
  },

  // 新增附件
  addUploader: (data = []) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/category/approve/add`, data)
  },

  // 删除附件
  delUploader: (data = []) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/category/approve/delete`, data)
  },

  // 门槛匹配详情
  queryThresholdMatchDetail: (id) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/threshold/detail/${id}`)
  },

  // 删除认证项目综合审批
  delApprove: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/approve/del`, data)
  },

  // 新增认证项目综合审批
  addApprove: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/approve/add`, data)
  },

  // 修改认证项目综合审批
  updateApprove: (data = {}) => {
    return API.put(`${PROXY_BASE}/tenant/buyer/auth/approve/update`, data)
  },

  // 综合审批申请单提交
  submitApprove: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/approve/submit`, data)
  },

  listByCompanyId: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/process/manager/listByCompanyId`, data)
  },

  projectSupplier: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/manager/projectSupplier`, data)
  },

  publicityAdd: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/after/effective/publicity/add`, data)
  },

  publicityDelete: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/after/effective/publicity/delete`, data)
  },

  publicitySubmit: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/after/effective/publicity/submit`, data)
  },

  publishAdd: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/after/effective/publish/add`, data)
  },

  publishDelete: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/after/effective/publish/delete`, data)
  },

  publishSubmit: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/after/effective/publish/submit`, data)
  },

  supplierList: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/after/effective/publish/supplier/list`, data)
  },

  queryEmployee: (data = {}) => {
    return API.post(`${PROXY_BASEASONE}/tenant/employee/paged-query`, data)
  },

  publicityDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/after/effective/publicity/detail`, data)
  },
  publicityUpdate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/after/effective/publicity/update`, data)
  },

  publishDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/after/effective/publish/detail`, data)
  },
  publishUpdate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/after/effective/publish/update`, data)
  },
  // 综合审批提交前校验
  checkQualification: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/approve/checkQualification`, data)
  },

  // 小批量认证、样品认证-附件列表更新
  updateFileList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/category/approve/updateFile`, data)
  },
  // 品类认证管理-认证需求-导出
  exportCertifyDemand: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/manager/demandQuery/export`, data, {
      responseType: 'blob'
    })
  },
  // 品类认证管理-认证项目-导出
  exportCertifyProject: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/manager/projectQuery/export`, data, {
      responseType: 'blob'
    })
  },

  // 采方-预审调查表-查询
  queryPreReviewSurveyApi: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/preReviewSurvey/queryList`, data)
  },
  // 采方-预审调查表-发布
  publishPreReviewSurveyApi: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/preReviewSurvey/publish`, data)
  },
  // 采方-预审调查表-批准
  passPreReviewSurveyApi: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/preReviewSurvey/pass`, data)
  },
  // 采方-预审调查表-驳回
  rejectPreReviewSurveyApi: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/preReviewSurvey/reject`, data)
  },
  // 采方-预审调查表-提交OA审批
  submitOAPreReviewSurveyApi: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/preReviewSurvey/submitOA`, data)
  },
  // 采方-预审调查表-查看OA审批
  checkOAPreReviewSurveyApi: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/preReviewSurvey/checkOA`, data)
  }
}

export default {
  NAME,
  APIS
}
