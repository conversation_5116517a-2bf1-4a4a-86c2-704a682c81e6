import { API } from '@mtech-common/http'
const NAME = 'supplierReviewTask'
const PROXY_BASE = '/supplier'
const PROXY_SOURCING = '/sourcing'
// 主数据的
const PROXY_Master = '/masterDataManagement'
const APIS = {
  // 任务列表
  taskReviewTaskRecord: `${PROXY_BASE}/tenant/buyer/review/task/pageList`,
  // 供应商评审管理-任务详情-供应商自查-删除
  delReviewTask: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/delReviewTask`, data)
  },
  // 新增任务
  taskReviewTaskSave: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/add`, data)
  },
  //   更新任务
  taskReviewTaskUpdate: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/update`, data)
  },
  // 任务详情
  taskReviewTaskDetail: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/detail`, data)
  },
  // 审查单-保存
  saveReviewTaskApi: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/reviewTaskSave`, data)
  },
  // 审查单-发布
  publishReviewTaskApi: (id) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/reviewTaskPublish/${id}`)
  },
  // 审查单-获取详情
  queryReviewTaskApi: (data = []) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/review/task/reviewTaskDetailQuery`, data)
  },
  // 审查单-附件类型打分人
  getFileTypeScoreUserInfo: (data) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/review/task/fileTypeScoreUserInfo`, data)
  },
  //   主数据获取公司
  getMasterCompay: (data) => {
    data = { orgLevelCode: 'ORG02', orgType: 'ORG001PRO' }
    return API.post(`${PROXY_Master}/tenant/organization/getStatedLimitTree`, data)
  },
  getMasterSupplier: (data) => {
    // {"orgId":"1457546203432607746"}
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/relation/getOrgPartnerRelations`, data)
  },
  // 获取供应商
  getMasterCate: (data) => {
    // {"partnerRelationId":"1495965973685301249"}
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/relation/getCategoryPartnerRelations`, data)
  },
  // 获取注册地址
  getDetailAddress: (data) => {
    // {"partnerRelationId":"1495965973685301249"}
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/archive/base/baseInfo`, data)
  },
  //评审类型
  getMasterReviewType: (data) => {
    // {dictCode: "reviewType"}
    return API.post(`${PROXY_Master}/tenant/dict-item/dict-code`, data)
  },
  //评审类型
  getMasterReviewTemplateType: (data) => {
    // {dictCode: "reviewTempType"}
    return API.post(`${PROXY_Master}/tenant/dict-item/item-tree`, data)
  },
  //获取主数据部门
  getMasterDepartMent: (data) => {
    // {"organizationId":"1399202858620522497"}
    return API.post(`${PROXY_Master}/tenant/organization/getCompanyDepartmentTree`, data)
  },
  // 获取专家
  getSpecialist: (data) => {
    return API.post(`${PROXY_SOURCING}/tenant/expert/info/query`, data)
  },
  // 获取专家 -- 通采
  getSpecialist_ordinarySpplyType: (data = []) => {
    // 通采情况
    return API.get(`${PROXY_SOURCING}/tenant/expert/info/query/personnel`, data)
  },
  //获取员工
  getMaseterEmplyee: (data) => {
    // {"orgId":"1475797808444964865"}
    return API.post(`${PROXY_Master}/tenant/organization/getOrganizationEmployees`, data)
  },
  //获取模板与评审项
  getPrestItem: (data) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/template/review/queryTemplateByTypeId?orgId=${data.orgId}&categoryId=${data.categoryId}`,
      data.ids
    )
  },

  // 获取评审详情接口
  getPreviewDetail: (params = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/detail`, params, {
      headers: { 'Content-Type': 'multipart/form-data;boundary=multipart/form-data' }
    })
  },
  // 场景下拉
  getSceneList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/scene/define/listQuery`, data)
  },
  // 我的评分接口
  getScoreList: `${PROXY_BASE}/tenant/buyer/review/task/myScoreList`,
  // 我的评分详情接口
  getScoreDetail: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/myScoreDetail`, data)
  },
  // 我的评分-详情-保存
  batchSaveDetailScore: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/batchScore`, data)
  },
  // 我的评分-详情-保存并提交
  batchSubmitDetailScore: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/submitScore`, data)
  },
  // 评审包第二步
  queryByOrgIdAndCategoryId: (packageCode = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/review/package/queryByPackageCode?packageCode=${packageCode}`
    )
  },
  // 评审包第一步
  queryByOrgIdAndCategoryIdPre: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/manager/queryAuthReviewPackage`, data)
  },
  //供方提交
  saveSupplier: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/review/task/submit`, data)
  },
  //供方提交
  supplierPreviewDetail: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/review/task/detail?code=${data.code}`, data)
  },
  //供方打分
  supplierSaveOrSubmit: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/review/task/update`, data)
  },
  //导入
  importExcel: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/importData`, data)
  },
  // 导出
  exportExcel: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/export`, data)
  },

  // 提交任务结果评审
  sumitReviewApiRist: (code, data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/submit/resultDetail?code=${code}`, data)
  },

  // 导出
  taskExport: (data) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/review/task/export?code=${data.code}`, data, {
      responseType: 'blob'
    })
  },
  taskImport: (data) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/review/task/importData`, data)
  },

  // 评审清单详情页--保存
  submitScorer: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/updateScoreUser`, data)
  },
  // 评审清单详情页--保存并发布
  submitReleaseScorer: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/publishReviewTask`, data)
  },
  // 评审清单-专家重新抽取
  redrawExpert: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/reExtract`, data)
  },
  // 专家确认取消按钮
  setExpertIsConfirm: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/operate`, data)
  },

  // 查看不合格附件
  getUnqualifiedFilesApi: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/getUnqualifiedFiles`, data)
  },
  // 上传不合格附件
  uploadUnqualifiedFileApi: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/uploadUnqualifiedFile`, data)
  },
  // 删除不合格附件
  deleteUnqualifiedFileApi: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/deleteUnqualifiedFile`, data)
  },

  // 采方-查看审查结果附件
  getExpertResultFileApi: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/getExpertResultFile`, data)
  },
  // 采方-上传专家评审附件
  uploadExpertResultFileApi: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/uploadExpertResultFile`, data)
  },
  // 采方-删除专家评审附件
  deleteExpertResultFileApi: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/task/deleteExpertResultFile`, data)
  },
  // 供方-查看供应商评审结果附件
  getSelfResultFileApi: (data) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/review/task/getSelfResultFile`, data)
  },
  // 供方-上传供应商评审结果附件
  uploadSelfResultFileApi: (data) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/review/task/uploadSelfResultFile`, data)
  },
  // 供方-删除供应商评审结果附件
  deleteSelfResultFileApi: (data) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/review/task/deleteSelfResultFile`, data)
  }
}

export default {
  NAME,
  APIS
}
