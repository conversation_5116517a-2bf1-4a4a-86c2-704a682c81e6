// 阶段配置 接口
import { API } from '@mtech-common/http'
const NAME = 'ModuleConfig'
const PROXY_BASE = '/supplier'

// 主数据的
const PROXY_Master = '/masterDataManagement'
const APIS = {
  // 获取已定义供应商阶段
  queryDefinedStage: (id) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/setting/stage_define/list?scopeId=${id}`)
  },
  // 新增供应商阶段
  addStage: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/setting/stage_define/add`, data)
  },
  // 编辑供应商阶段
  editStage: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/setting/stage_define/modify`, data)
  },
  // 删除供应商阶段
  deleteStage: (id) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/setting/stage_define/delete?stageId=${id}`)
  },

  // 获取供应商阶段配置
  queryStageConfig: (id = '') => {
    return API.get(`${PROXY_BASE}/tenant/buyer/setting/stage_rule/list?scopeId=${id}`)
  },
  // 新增规则和任务
  addStageRule: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/setting/stage_rule/add`, data)
  },
  // 编辑阶段规则
  editStageRule: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/setting/stage_rule/modify`, data)
  },
  // 刪除阶段规则
  deleteStageRule: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/setting/stage_rule/delete`, data)
  },

  // 供应商阶段拖动顺序
  moveStage: (data = '') => {
    return API.post(`${PROXY_BASE}/tenant/buyer/setting/stage_define/move_to?${data}`)
  },

  // 获取供应商类型列表
  querySupplierType: () => {
    return API.get(`${PROXY_BASE}/tenant/buyer/setting/supplier_type/list`)
  },
  // 分组列出表单任务定义
  queryFormTaskDefine: (id = '') => {
    return API.get(`${PROXY_BASE}/tenant/buyer/setting/form_task_define/list?scopeId=${id}`)
  },
  // 查询公司 模糊查询
  getCompanys: () => {
    return API.post(`${PROXY_Master}/tenant/company/criteria-query`)
  },

  // ============【参数配置】============
  // 添加信息定义数据
  addInfoDef: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/form/define/add`, data)
  },

  // 更新信息定义数据
  editInfoDef: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/form/define/update`, data)
  },

  // 分页查询门槛定义数据
  queryThresholdDefList: (data) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/threshold/define/query`, data)
  },

  // 查询门槛字段列表
  queryThresholdFieldList: () => {
    return API.post(`${PROXY_BASE}/tenant/buyer/field/instance/query`)
  },

  // 添加门槛定义数据
  addThresholdDef: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/threshold/define/add`, data)
  },

  // 更新门槛定义数据
  updateThresholdDef: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/threshold/define/update`, data)
  },

  // 删除门槛定义数据
  deleteThresholdDef: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/threshold/define/delete`, data)
  },

  //=====【参数配置】评审项定义=====
  // 判断该资质项定义有没有被模板引用
  adoptedReviewDef: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/define/review/adopted`, data)
  },
  // 添加评审项定义
  addReviewDef: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/define/review/add`, data)
  },

  // 修改评审项定义
  editReviewDef: (data = {}) => {
    return API.put(`${PROXY_BASE}/tenant/buyer/define/review/update`, data)
  },

  // 删除评审项定义数据
  deleteReviewDef: (data = {}) => {
    return API.delete(`${PROXY_BASE}/tenant/buyer/define/review/delete`, data)
  },

  // 评审项定义列表查询
  queryReviewDef: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/define/review/query`, data)
  },

  //=====【参数配置】资质项定义=====
  qualificationDefineQueryPage: (data = {}) => {
    // 资质项定义==列表查询
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/define/queryPage`, data)
  },
  qualificationDefineAdd: (data = {}) => {
    // 资质项定义==添加
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/define/add`, data)
  },
  getOrganizationEmployees: (data = {}) => {
    // 人员信息
    return API.post(`/masterDataManagement/tenant/organization/getOrganizationEmployees`, data)
  },
  qualificationDefineUpdate: (data = {}) => {
    // 资质项定义==修改
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/define/update`, data)
  },
  qualificationDefineDisable: (data = '') => {
    // 资质项定义==停用
    return API.post(
      `${PROXY_BASE}/tenant/buyer/qualification/define/disable?qualificationCode=${data}`
    )
  },
  qualificationDefineEnable: (data = {}) => {
    // 资质项定义==启用
    return API.post(
      `${PROXY_BASE}/tenant/buyer/qualification/define/enable?qualificationCode=${data}`
    )
  },

  // 查询指定业务类型字段实例信息列表(门槛项定义监控字段查询)
  queryByBusinessType: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/field/instance/queryBusinessType`, data)
  },

  // 添加门槛定义数据
  // addThresholdDef: (data = {}) => {
  //   return API.post(`${PROXY_BASE}/tenant/buyer/threshold/define/add`, data);
  // },
  // qualificationDefineUpdate: (data = {}) => { // 资质项定义==修改
  //   return API.post(`${PROXY_BASE}/tenant/buyer/qualification/define/update`, data);
  // },
  qualificationDefineDel: (data) => {
    // 资质项定义==删除
    return API.post(
      `${PROXY_BASE}/tenant/buyer/qualification/define/del?qualificationCode=${data}`,
      '',
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    )
  },
  // qualificationGetStatedLimitTree: (data) => { // 资质项定义==组织树(先调用组织树，再查确认部门)
  //   return API.post(`${PROXY_BASE}/masterDataManagement/tenant/organization/getStatedLimitTree,`, data);
  // },
  // masterDataManagement/tenant/organization/getCompanyDepartmentTree
  //=====【参数配置】状态定义=====
  // 获取状态定义
  getStatusDef: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/define/status/query`, data)
  },

  // 申请单提交(停启用门槛项定义)
  updateThresholdStatus: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/threshold/define/enable`, data)
  },

  // 认证场景定义详情
  querySceneDetail: (id) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/scene/define/detail?id=${id}`)
  },

  // 新增认证场景定义
  addSceneDef: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/scene/define/add`, data)
  },

  // 修改认证场景定义
  editSceneDef: (data = {}) => {
    return API.put(`${PROXY_BASE}/tenant/buyer/scene/define/update`, data)
  },

  // 删除认证场景定义
  deleteSceneDef: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/scene/define/del`, data)
  },

  // 更改认证场景定义状态
  updateSceneStatus: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/scene/define/updateStatus`, data)
  },

  // ============【模板配置】============

  // 保存供应商表单信息
  updateFormTask: (data = {}) => {
    return API.put(`${PROXY_BASE}/tenant/buyer/template/task/saveForm`, data)
  },

  // 新增评审模板
  addReviewTemp: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/review/add`, data)
  },

  // 删除评审模板
  deleteReviewTemp: (data = {}) => {
    return API.delete(`${PROXY_BASE}/tenant/buyer/template/review/delete`, data)
  },

  // 修改评审模板
  updateReviewTemp: (data = {}) => {
    return API.put(`${PROXY_BASE}/tenant/buyer/template/review/update`, data)
  },

  // 启用评审模板
  enableReviewTemp: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/review/enable`, data)
  },

  // 停用评审模板
  disableReviewTemp: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/review/unable`, data)
  },

  // 下载评审模板导入模板
  downloadReviewTempTemplate: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/template/review/getTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 导入评审模板
  importReviewTemp: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/review/importTemplate`, data, {
      responseType: 'blob'
    })
  },

  // 查询评审模板明细
  queryReviewTempDetail: (templateId) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/template/review/item/query/${templateId}`)
  },

  // 添加门槛模板
  addThresholdTemp: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/threshold/template/add`, data)
  },

  // 删除门槛模板
  delThresholdTemp: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/threshold/template/delete`, data)
  },

  // 更新门槛模板
  updateThresholdTemp: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/threshold/template/update`, data)
  },

  // 门槛模板--启用、停用策略设置
  editThresholdTempStatus: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/threshold/template/enable`, data)
  },

  // 门槛模板--查询门槛模板数据详情
  queryThresholdTempDetail: (id) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/threshold/template/queryDetail?id=${id}`)
  },

  // 门槛模板--添加门槛模板子表数据
  addThresholdTempItems: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/threshold/template/addItem`, data)
  },
  // ====【模板配置】资质模板=====
  qualificationTemplateConditionQuery: (data) => {
    //资质模板==模板类型
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/template/condition/query`, data)
  },
  qualificationTemplatePageQueryBack: () => {
    //资质模板==(资质模板)同下,其他页面用到
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/template/pageQuery`)
  },
  qualificationTemplatePageQuery: `${PROXY_BASE}/tenant/buyer/qualification/template/pageQuery`,
  qualificationsTempType: (data = {}) => {
    // 资质模板==类型
    return API.get(`${PROXY_Master}/tenant/dict-item/getByDictCode/qualificationsTempType`, data)
  },
  definelistQuery: (data = {}) => {
    // 资质模板==场景
    return API.post(`${PROXY_BASE}/tenant/buyer/scene/define/listQuery`, data)
  },
  applicablescene: (data = {}) => {
    // 资质模板==适用场景
    return API.post(`${PROXY_BASE}/tenant/buyer/scene/define/findByOrgCategoryByInfos`, data)
  },

  getFuzzyCompanyTree: (data = {}) => {
    // 资质模板==组织
    return API.post(`${PROXY_Master}/tenant/organization/getFuzzyCompanyTree`, data)
  },
  qualificationTemplateAdd: (data = {}) => {
    // 资质模板==新增
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/template/add`, data)
  },
  qualificationTemplateDelete: (data = {}) => {
    // 资质模板==删除
    return API.delete(`${PROXY_BASE}/tenant/buyer/qualification/template/delete`, data)
  },
  qualificationTemplateEnable: (data = {}) => {
    // 资质模板==启用
    return API.put(`${PROXY_BASE}/tenant/buyer/qualification/template/enable`, data)
  },
  qualificationTemplateDisable: (data = {}) => {
    // 资质模板==禁用
    return API.put(`${PROXY_BASE}/tenant/buyer/qualification/template/disable`, data)
  },
  qualificationTemplateAddItem: (data = {}) => {
    // 资质模板详情==新增(不知道别的地方用不用了,换了下面的template/saveItem)
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/template/addItem`, data)
  },
  qualificationTemplateDisableItem: (data = {}) => {
    // 资质模板详情==删除(不知道别的地方用不用了,换了下面的template/saveItem)
    return API.delete(`${PROXY_BASE}/tenant/buyer/qualification/template/deleteItem`, data)
  },
  qualificationTemplateSaveItem: (data = {}) => {
    // 资质模板详情==保存(在右上角保存用到)
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/template/saveItem`, data)
  },
  qualificationTemplateQuery: (data = {}) => {
    // 资质模板详情==查询、
    return API.get(`${PROXY_BASE}/tenant/buyer/qualification/template/query`, data)
  },
  qualificationTemplatemodify: (data = {}) => {
    // 资质模板详情==查询、
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/template/modify`, data)
  },
  //资质项
  qualificationQueryPage: `${PROXY_BASE}/tenant/buyer/qualification/define/queryPage`,
  // qualificationQueryPage: (data) => { // 资质项列表==查询、
  //   return API.post(`${PROXY_BASE}/tenant/buyer/qualification/define/queryPage`);
  // },

  // 新增评审包
  addReviewPackage: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/package/add`, data)
  },

  // 删除评审包
  deleteReviewPackage: (data = {}) => {
    return API.delete(`${PROXY_BASE}/tenant/buyer/review/package/delete`, data)
  },

  // 启用评审包
  enableReviewPackage: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/package/enable`, data)
  },

  // 停用评审包
  disableReviewPackage: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/review/package/unable`, data)
  },

  // 修改评审包
  updateReviewPackage: (data = {}) => {
    return API.put(`${PROXY_BASE}/tenant/buyer/review/package/update`, data)
  },

  /*
    主数据
  */
  // 获取主品类树特定层级数据--认证场景定义
  getCategoryList: (data = {}) => {
    return API.post(`${PROXY_Master}/tenant/category/product-list`, data)
  },

  //租户级-组织结构接口-获取指定范围树结构数据--认证场景定义
  //公司：ORG02，部门：ORG03，岗位：ORG04，员工：ORG05
  getStatedLimitTree: (data = {}) => {
    return API.post(`${PROXY_Master}/tenant/organization/getStatedLimitTree`, data)
  },
  qualificationGetCompanyDepartmentTree: (data) => {
    // 资质项定义== 确认部门(组织树) (从公司查找)
    return API.post(`${PROXY_Master}/tenant/organization/getCompanyDepartmentTree`, data)
  },
  //====通用=====
  // 根据字典类型编码获取字典详情
  queryDict: (data = {}) => {
    return API.post(`${PROXY_Master}/tenant/dict-item/dict-code`, data)
  },
  // 资质项动态字段列表(详情页列表拼接用到)
  qualificationGetAllDynamicFields: () => {
    return API.get(`${PROXY_BASE}/tenant/common/qualification/getAllDynamicFields`, '', {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 参数设置资质项定义--模板下载
  downloadParameterTemplate: (data = {}) => {
    return API.post(`supplier/tenant/buyer/qualification/define/download`, data, {
      responseType: 'blob'
    })
  },
  // 参数设置资质项定义--导入文件
  importParameterFile: (data = {}) => {
    return API.post(`supplier/tenant/buyer/qualification/define/importTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 参数设置评审项定义--模板下载
  downloadReviewTemplate: (data = {}) => {
    return API.post(`supplier/tenant/buyer/define/review/download`, data, {
      responseType: 'blob'
    })
  },
  // 参数设置评审项定义--导入文件
  importReviewFile: (data = {}) => {
    return API.post(`supplier/tenant/buyer/define/review/importTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 专家库--模板下载
  downloadExpertTemplate: (data = {}) => {
    return API.post(`sourcing/tenant/expert/apply/downloadTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 专家库--导入文件
  importExpertFile: (data = {}) => {
    return API.post(`sourcing/tenant/expert/apply/importTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 专家库--导出文件
  exportExpertFile: (data = {}) => {
    return API.post(`sourcing/tenant/expert/apply/export`, data, {
      responseType: 'blob'
    })
  },
  // 档案导入--模板下载
  downloadTemplateFeleApi: (data = {}) => {
    return API.post(`/supplier/tenant/buyer/process/archive/excel/supplierImportTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 档案导入--导入文件
  importFileApi: (data = {}) => {
    return API.post(`/supplier/tenant/buyer/process/archive/excel/supplierImport`, data, {
      responseType: 'blob'
    })
  },
}

export default {
  NAME,
  APIS
}
