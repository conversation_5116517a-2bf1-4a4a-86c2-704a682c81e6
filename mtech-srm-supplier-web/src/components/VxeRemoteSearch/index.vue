<template>
  <!-- 远程搜索下拉选择框 -->
  <div>
    <vxe-pulldown ref="pulldownRef" v-model="pulldownShow" transfer>
      <template #default>
        <vxe-input
          v-model="showValue"
          suffix-icon="vxe-icon-caret-down"
          readonly
          clearable
          :placeholder="placeholder"
          @click="handlePulldown"
          @clear="handleClear"
          @suffix-click="handlePulldown"
        />
      </template>
      <template #dropdown>
        <vxe-input
          style="width: 100%"
          ref="purchaserSearcInputRef"
          v-model="searchText"
          :prefix-icon="'vxe-icon-search'"
          clearable
          :placeholder="$t('搜索')"
          @input="handlePulldownSearchInput"
          transfer
        />
        <vxe-list class="my-dropdown2" :data="dataList" auto-resize>
          <template #default="{ items }">
            <div v-if="items.length">
              <!-- 多选 -->
              <div v-if="multiple">
                <vxe-checkbox-group
                  v-model="valGroup"
                  :max="selectLimit"
                  @change="handlePulldownItemSelected"
                >
                  <vxe-checkbox
                    class="list-item2"
                    v-for="item in items"
                    :key="item[fileds.value]"
                    :label="item[fileds.value]"
                    :content="item.text"
                  />
                </vxe-checkbox-group>
              </div>
              <!-- 单选 -->
              <div v-else>
                <div
                  class="list-item2"
                  v-for="item in items"
                  :key="item[fileds.value]"
                  @click="handlePulldownItemSelected(item)"
                >
                  <span
                    :class="{
                      isSelected: item[fileds.value] === showValue || item.text === showValue
                    }"
                    >{{ item.text }}</span
                  >
                </div>
              </div>
            </div>
            <div v-else class="empty-tip">
              {{ $t('暂无数据') }}
            </div>
          </template>
        </vxe-list>
      </template>
    </vxe-pulldown>
  </div>
</template>

<script>
import debounce from 'lodash.debounce'
import { CheckboxGroup as VxeCheckboxGroup } from 'vxe-table'

export default {
  components: { VxeCheckboxGroup },
  model: {
    prop: 'value',
    event: 'syncValue'
  },
  props: {
    value: {
      type: [String, Array],
      default: null
    },
    placeholder: {
      type: String,
      default: null
    },
    fileds: {
      type: Object,
      default: () => {
        return { value: 'value', text: 'text' }
      }
    },
    // 请求数据的信息
    requestInfo: {
      type: Object,
      default: () => {
        return {
          urlPre: null,
          url: null,
          searchKey: null,
          params: {},
          recordsPosition: 'data'
        }
      }
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 最多选择数量
    selectLimit: {
      type: Number,
      default: 20
    }
  },
  data() {
    return {
      pulldownShow: false,
      showValue: null,
      searchText: null,
      dataList: [],
      selectItem: {},
      valGroup: []
    }
  },
  computed: {},
  mounted() {
    this.init()
  },
  methods: {
    init() {
      if (this.dataList?.length) {
        return
      }
      if (this.multiple && this.value?.length) {
        this.showValue = this.value?.join(',')
        this.valGroup = this.value
        this.valGroup?.forEach((val) => this.getDataList(val, 'init'))
      } else {
        this.showValue = this.value
        this.getDataList(this.value)
      }
    },
    // 远程搜索查询-展开面板
    handlePulldown() {
      this.$refs.pulldownRef?.togglePanel()
      this.$nextTick(() => {
        this.searchText = null
        this.$refs.purchaserSearcInputRef?.focus()
      })
    },
    // 远程搜索查询-查询
    handlePulldownSearchInput: debounce(function (e) {
      this.getDataList(e?.value)
    }, 500),
    // 远程搜索查询-选中
    handlePulldownItemSelected(item) {
      if (this.multiple) {
        this.showValue = this.valGroup?.join(',')
        !this.selectItem?.length && (this.selectItem = [])
        if (item.checked) {
          // 选中
          const temp = this.dataList?.find((d) => d[this.fileds.value] === item.label)
          this.selectItem.push(temp)
        } else {
          // 取消
          const index = this.selectItem?.findIndex((d) => d[this.fileds.value] === item.label)
          index >= 0 && this.selectItem.splice(index, 1)
        }
        this.$emit('syncValue', this.valGroup)
        this.$emit('change', this.selectItem)
      } else {
        this.showValue = item.text || item[this.fileds.text]
        this.selectItem = item

        this.$refs.pulldownRef?.hidePanel()
        this.$emit('syncValue', item[this.fileds.value])
        this.$emit('change', item)
      }
    },
    // 远程搜索查询-清空选择
    handleClear() {
      this.showValue = null
      if (this.multiple) {
        this.valGroup = []
        this.selectItem = []
        this.$emit('syncValue', [])
        this.$emit('change', [])
      } else {
        this.selectItem = {}
        this.$emit('syncValue', null)
        this.$emit('change', null)
      }
    },
    // 查询列表
    async getDataList(searchText, type) {
      const { urlPre, url, searchKey, params, recordsPosition } = this.requestInfo
      if (!urlPre || !url) {
        this.$toast({ content: this.$t('缺少必要查询信息'), type: 'warning' })
        return
      }
      const queryInfo = { ...params }
      queryInfo[searchKey] = searchText
      const res = await this.$API[urlPre][url](queryInfo)
      if (res?.code === 200) {
        res[recordsPosition]?.forEach((item) => {
          item.text = item[this.fileds.value] + '-' + item[this.fileds.text]
        })
        if (type === 'init') {
          const list = [...this.dataList, ...res[recordsPosition]]
          const newList = Array.from(new Set(list))
          this.dataList = newList
          this.handlePulldownItemSelected({ label: searchText, checked: true })
        } else {
          this.dataList = res[recordsPosition]
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 250px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  display: block;
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}

::v-deep {
  .vxe-input,
  .vxe-pulldown {
    width: 100%;
  }
  .vxe-checkbox {
    display: block;
    margin-left: 0;
  }
}
</style>
