<template>
  <div>
    <vxe-pulldown ref="pulldownRef" transfer>
      <template #default>
        <vxe-input
          v-model="dateString"
          size="mini"
          suffix-icon="vxe-icon-calendar"
          placeholder="下拉选择"
          @suffix-click="togglePanel"
        ></vxe-input>
      </template>
      <template #dropdown>
        <div>
          <ejs-calendar
            :values="calenderList"
            v-bind="$attrs"
            v-on="$listeners"
            :placeholder="$t('请选择')"
            @change="change"
          ></ejs-calendar>
        </div>
      </template>
    </vxe-pulldown>
  </div>
</template>
<script>
import Vue from 'vue'
import MtCalendar from '@mtech-ui/calendar'
Vue.use(MtCalendar)
import '@/components/ScTable/src/vxetable.scss'
import { Icon, Input, Pulldown } from 'vxe-table'
import utils from '@/utils/utils'
Vue.use(Icon).use(Input).use(Pulldown)

export default {
  model: {
    prop: 'modelVal',
    event: 'calenderChange'
  },
  props: {
    modelVal: {
      type: [String, Number, Array],
      default: () => {
        return null
      }
    }
  },
  data() {
    return {
      dateString: ''
    }
  },
  // computed: {
  //   calenderList: {
  //     get() {
  //       const list = this.modelVal || []
  //       this.setDisplayText(list)
  //       return list.map((item) => new Date(item))
  //     },
  //     set(val) {
  //       console.log(val)
  //     }
  //   }
  // },
  watch: {
    modelVal: {
      handler(val) {
        const list = val || []
        this.setDisplayText(list)
        this.calenderList = list.map((item) => new Date(item))
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    togglePanel() {
      this.$refs.pulldownRef.togglePanel()
    },
    change(args) {
      const list = args.values || []
      const res = this.setDisplayText(list)
      this.$emit('calenderChange', res)
    },
    setDisplayText(list) {
      const res = list.map((item) => {
        return utils.formateTime(item)
      })
      this.dateString = res.join()
      return res
    }
  }
}
</script>
<style lang="scss" scoped>
.showBar {
  display: flex;
  align-items: center;
}
::v-deep .vxe-pulldown {
  width: 100%;
  .vxe-input {
    width: 100%;
  }
}
::v-deep .vxe-input--inner {
  width: 100%;
  height: 100%;
  border-radius: 0px;
  outline: 0;
  margin: 0;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
  padding: 0 0.6em;
  color: #606266;
  border: 1px solid rgba(0, 0, 0, 0.6);
  border-top: none;
  border-left: none;
  border-right: none;
  background-color: #fff;
  box-shadow: none;
}
</style>
