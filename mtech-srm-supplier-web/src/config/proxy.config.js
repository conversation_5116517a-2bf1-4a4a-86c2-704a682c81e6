/*
 * @Author: wenjie20.wang <EMAIL>
 * @Date: 2022-10-09 14:27:17
 * @LastEditors: wenjie20.wang <EMAIL>
 * @LastEditTime: 2022-10-22 12:04:48
 * @FilePath: \mtech-srm-supplier-web\src\config\proxy.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
module.exports = {
  '/api': {
    // target: "http://srm.test.qeweb.com",
    // target: "https://srm-sit-main.sct.tcl.com",
    // target: " https://srm-uat-main.sct.tcl.com",
    // target: ' https://srm-sit-supplier.sct.tcl.com',
    target: 'http://srm-uat-gw.eads.tcl.com',
    // target: 'http://srm-sit-gw.eads.tcl.com',
    // target:"http://srm.demo.qeweb.com/",
    // target: "http://gateway.dev.qeweb.com",
    changeOrigin: true,
    pathRewrite: {
      // "^/api/": "",
      // "^/api/supplier": "",
    }
  }

  // "/api/supplier": {
  //   // target: "http://srm.dev.qeweb.com",
  //   // target: "http://file.dev.qeweb.com",
  //   target: "http://*************:9112",
  //   changeOrigin: true,
  //   pathRewrite: {
  //     // "^/api/": "",
  //     "^/api/supplier": "",
  //   },
  // },

  // "/api/masterDataManagement": {
  //   target: "http://srm.dev.qeweb.com",
  //   // target: "http://file.dev.qeweb.com",
  //   // target: "http://10.14.243.159:9112",
  //   changeOrigin: true,
  //   pathRewrite: {
  //     // "^/api/": "",
  //     // "^/api/file": "",
  //   },
  // },

  // "/api/iam": {
  //   target: "http://srm.dev.qeweb.com",
  //   // target: "http://file.dev.qeweb.com",
  //   // target: "http://10.14.243.159:9112",
  //   changeOrigin: true,
  //   pathRewrite: {
  //     // "^/api/": "",
  //     // "^/api/file": "",
  //   },
  // },
}
